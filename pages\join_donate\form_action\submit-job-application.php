<?php
// Validate required text fields
$required = ['firstName', 'lastName', 'email', 'phone', 'birthDate', 'nationality', 'position', 'experience', 'education', 'languages', 'terms'];
foreach ($required as $field) {
    if (empty($_POST[$field])) {
        die('يرجى تعبئة جميع الحقول المطلوبة.');
    }
}
// Validate required file field (cv)
if (!isset($_FILES['cv']) || $_FILES['cv']['error'] !== UPLOAD_ERR_OK) {
    die('يرجى رفع السيرة الذاتية (PDF).');
}

// Sanitize input
$firstName = htmlspecialchars($_POST['firstName']);
$lastName = htmlspecialchars($_POST['lastName']);
$email = filter_var($_POST['email'], FILTER_VALIDATE_EMAIL);
$phone = htmlspecialchars($_POST['phone']);
$birthDate = htmlspecialchars($_POST['birthDate']);
$nationality = htmlspecialchars($_POST['nationality']);
$position = htmlspecialchars($_POST['position']);
$experience = htmlspecialchars($_POST['experience']);
$education = htmlspecialchars($_POST['education']);
$skills = htmlspecialchars($_POST['skills'] ?? '');
$languages = htmlspecialchars($_POST['languages']);

// Prepare upload directory
$date = date('Ymd_His');
$dirName = $date . '_' . preg_replace('/\s+/', '_', $firstName . '_' . $lastName);
$uploadDir = __DIR__ . '/../join_donate_databases/uploads/job-application/' . $dirName . '/';
if (!is_dir($uploadDir)) {
    mkdir($uploadDir, 0775, true);
}

// Handle CV upload (required)
$cvFileName = uniqid('cv_') . '_' . basename($_FILES['cv']['name']);
$cvFilePath = $uploadDir . $cvFileName;
if (!move_uploaded_file($_FILES['cv']['tmp_name'], $cvFilePath)) {
    die('حدث خطأ أثناء رفع السيرة الذاتية.');
}
$cv = 'join_donate_databases/uploads/job-application/' . $dirName . '/' . $cvFileName;

// Handle cover letter upload (optional)
$coverLetter = '';
if (isset($_FILES['coverLetter']) && $_FILES['coverLetter']['error'] === UPLOAD_ERR_OK) {
    $coverLetterFileName = uniqid('cover_') . '_' . basename($_FILES['coverLetter']['name']);
    $coverLetterFilePath = $uploadDir . $coverLetterFileName;
    if (move_uploaded_file($_FILES['coverLetter']['tmp_name'], $coverLetterFilePath)) {
        $coverLetter = 'join_donate_databases/uploads/job-application/' . $dirName . '/' . $coverLetterFileName;
    }
}

// Handle certificates upload (optional)
$certificates = '';
if (isset($_FILES['certificates']) && $_FILES['certificates']['error'] === UPLOAD_ERR_OK) {
    $certificatesFileName = uniqid('cert_') . '_' . basename($_FILES['certificates']['name']);
    $certificatesFilePath = $uploadDir . $certificatesFileName;
    if (move_uploaded_file($_FILES['certificates']['tmp_name'], $certificatesFilePath)) {
        $certificates = 'join_donate_databases/uploads/job-application/' . $dirName . '/' . $certificatesFileName;
    }
}

// Store in SQLite database
try {
    $dbPath = __DIR__ . '/../join_donate_databases/join_forms.db';
    $db = new PDO('sqlite:' . $dbPath);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Create table if not exists
    $db->exec("CREATE TABLE IF NOT EXISTS job_application (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        firstName TEXT,
        lastName TEXT,
        email TEXT,
        phone TEXT,
        birthDate TEXT,
        nationality TEXT,
        position TEXT,
        experience TEXT,
        education TEXT,
        skills TEXT,
        languages TEXT,
        cv TEXT,
        coverLetter TEXT,
        certificates TEXT,
        submitted_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )");

    // Insert data
    $stmt = $db->prepare("INSERT INTO job_application 
        (firstName, lastName, email, phone, birthDate, nationality, position, experience, education, skills, languages, cv, coverLetter, certificates)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    $stmt->execute([
        $firstName, $lastName, $email, $phone, $birthDate, $nationality, $position, $experience, $education, $skills, $languages, $cv, $coverLetter, $certificates
    ]);
} catch (PDOException $e) {
    die('خطأ في حفظ البيانات: ' . $e->getMessage());
}

// Confirmation message
function showSuccessMessage(
    $firstName, $lastName, $email, $phone, $position, $cv, $coverLetter, $experience, $skills, $languages, $mailSuccess = true, $education = '', $certificates = '', $birthDate = '', $nationality = ''
) {
    $bg = $mailSuccess ? '#f8fff8' : '#fff3cd';
    $border = $mailSuccess ? '#c3e6cb' : '#ffeeba';
    ?>
    <style>
    .job-success-container {
        max-width: 700px;
        margin: 40px auto;
        padding: 30px 20px;
        background: <?php echo $bg; ?>;
        border: 1.5px solid <?php echo $border; ?>;
        border-radius: 12px;
        text-align: center;
        font-family: 'Cairo', sans-serif;
        box-shadow: 0 2px 12px #e6f2e6;
        direction: rtl;
    }
    .job-success-container h2 {
        color: <?php echo $mailSuccess ? '#28a745' : '#856404'; ?>;
        margin-bottom: 10px;
    }
    .job-success-container p {
        font-size: 1.1em;
        margin-bottom: 18px;
    }
    .job-success-btns button {
        margin: 5px 10px;
        padding: 10px 25px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 1em;
        transition: background 0.2s;
    }
    .job-success-btns .back-btn { background: #007bff; color: #fff; }
    .job-success-btns .view-btn { background: #17a2b8; color: #fff; }
    .job-success-btns .pdf-btn { background: #ffc107; color: #212529; }
    .job-form-details {
        display: none;
        text-align: right;
        direction: rtl;
        background: #fff;
        border: 1px solid #eee;
        padding: 24px 18px;
        border-radius: 8px;
        margin-top: 24px;
        font-size: 1.08em;
        box-shadow: 0 1px 8px #f3f3f3;
    }
    .job-form-details h3 {
        color: #17a2b8;
        margin-bottom: 18px;
        font-size: 1.2em;
        border-bottom: 1px solid #e0e0e0;
        padding-bottom: 8px;
    }
    .job-form-details p {
        margin: 8px 0;
        line-height: 1.7;
        word-break: break-word;
    }
    .job-form-details strong {
        color: #007bff;
        min-width: 110px;
        display: inline-block;
    }
    .job-form-details a {
        color: #17a2b8;
        text-decoration: underline;
        word-break: break-all;
    }
    @media print {
        body * { visibility: hidden !important; }
        #job-details, #job-details * { visibility: visible !important; display: block !important; }
        #job-details-print, #job-details-print * { visibility: visible !important; }
        #job-details-print {
            position: absolute;
            left: 0; top: 0; width: 100vw; background: #fff;
            box-shadow: none; border: none; padding: 0;
        }
        .job-success-btns, .job-success-container h2, .job-success-container p { display: none !important; }
    }
    </style>
    <div class="job-success-container">
        <h2>
            <?php echo $mailSuccess ? 'تم إرسال طلب التوظيف بنجاح' : 'تم حفظ طلبك'; ?>
        </h2>
        <p>
            <?php echo $mailSuccess ? 'شكراً لتقديمك طلب التوظيف!' : 'لكن حدث خطأ أثناء إرسال البريد الإلكتروني.'; ?>
        </p>
        <div class="job-success-btns">
            <button class="back-btn" onclick="window.location.href='../join_donate'">رجوع</button>
            <button class="view-btn" onclick="document.getElementById('job-details').style.display='block';">عرض طلبي</button>
            <button class="pdf-btn" onclick="printJobDetails()">حفظ كملف PDF</button>
        </div>
        <div id="job-details" class="job-form-details">
            <div id="job-details-print">
                <h3>معلومات طلب التوظيف:</h3>
                <p><strong>الاسم:</strong> <?php echo htmlspecialchars($firstName . ' ' . $lastName); ?></p>
                <p><strong>البريد الإلكتروني:</strong> <?php echo htmlspecialchars($email); ?></p>
                <p><strong>رقم الهاتف:</strong> <?php echo htmlspecialchars($phone); ?></p>
                <p><strong>تاريخ الميلاد:</strong> <?php echo htmlspecialchars($birthDate); ?></p>
                <p><strong>الجنسية:</strong> <?php echo htmlspecialchars($nationality); ?></p>
                <p><strong>الوظيفة المطلوبة:</strong> <?php echo htmlspecialchars($position); ?></p>
                <p><strong>المؤهل العلمي:</strong> <?php echo htmlspecialchars($education); ?></p>
                <p><strong>الخبرات:</strong> <?php echo nl2br(htmlspecialchars($experience)); ?></p>
                <p><strong>المهارات:</strong> <?php echo nl2br(htmlspecialchars($skills)); ?></p>
                <p><strong>اللغات:</strong> <?php echo htmlspecialchars($languages); ?></p>
                <p><strong>السيرة الذاتية:</strong>
                    <?php if ($cv) { ?>
                        <a href="/pages/join_donate/<?php echo htmlspecialchars($cv); ?>" target="_blank">عرض/تحميل</a>
                    <?php } else { echo 'غير متوفر'; } ?>
                </p>
                <p><strong>رسالة التغطية:</strong>
                    <?php if ($coverLetter) { ?>
                        <a href="/pages/join_donate/<?php echo htmlspecialchars($coverLetter); ?>" target="_blank">عرض/تحميل</a>
                    <?php } else { echo 'غير متوفر'; } ?>
                </p>
                <p><strong>الشهادات:</strong>
                    <?php if ($certificates) { ?>
                        <a href="/pages/join_donate/<?php echo htmlspecialchars($certificates); ?>" target="_blank">عرض/تحميل</a>
                    <?php } else { echo 'غير متوفر'; } ?>
                </p>
            </div>
        </div>
    </div>
    <script>
    function printJobDetails() {
        var details = document.getElementById('job-details');
        var wasHidden = details.style.display === 'none' || window.getComputedStyle(details).display === 'none';
        details.style.display = 'block';
        window.print();
        if (wasHidden) details.style.display = 'none';
    }
    </script>
    <?php
}

// Optional: Send email notification (customize as needed)
$to = '<EMAIL>';
$subject = 'طلب توظيف جديد';
$message = "
الاسم: $firstName $lastName
البريد الإلكتروني: $email
رقم الهاتف: $phone
الوظيفة المطلوبة: $position
السيرة الذاتية: $cv
رسالة التغطية: $coverLetter
الخبرات: $experience
المهارات: $skills
اللغات: $languages
";
$headers = "From: $email\r\nContent-Type: text/plain; charset=utf-8";

if (mail($to, $subject, $message, $headers)) {
    showSuccessMessage($firstName, $lastName, $email, $phone, $position, $cv, $coverLetter, $experience, $skills, $languages, true, $education, $certificates, $birthDate, $nationality);
} else {
    showSuccessMessage($firstName, $lastName, $email, $phone, $position, $cv, $coverLetter, $experience, $skills, $languages, false, $education, $certificates, $birthDate, $nationality);
}
?>