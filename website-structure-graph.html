<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Holy Work Organization - Website Structure</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 1400px;
            margin: 0 auto;
        }
        
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        #graph {
            width: 100%;
            height: 800px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            background: #fafafa;
        }
        
        .node {
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .node:hover {
            transform: scale(1.05);
        }
        
        .node-homepage { fill: #e74c3c; stroke: #c0392b; }
        .node-main-section { fill: #3498db; stroke: #2980b9; }
        .node-service { fill: #2ecc71; stroke: #27ae60; }
        .node-admin { fill: #f39c12; stroke: #e67e22; }
        .node-database { fill: #9b59b6; stroke: #8e44ad; }
        .node-static { fill: #95a5a6; stroke: #7f8c8d; }
        
        .link {
            fill: none;
            stroke: #bdc3c7;
            stroke-width: 2;
            transition: all 0.3s ease;
        }
        
        .link:hover {
            stroke: #3498db;
            stroke-width: 3;
        }
        
        .node-text {
            font-family: 'Arial', sans-serif;
            font-size: 12px;
            font-weight: bold;
            text-anchor: middle;
            dominant-baseline: middle;
            fill: white;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
        
        .legend {
            margin-top: 20px;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 20px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 15px;
            background: #f8f9fa;
            border-radius: 20px;
            border: 1px solid #e9ecef;
        }
        
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 2px solid #333;
        }
        
        .info-panel {
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 5px solid #3498db;
        }
        
        .info-panel h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        
        .info-panel ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .info-panel li {
            margin: 5px 0;
            color: #555;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌟 Holy Work Organization - Website Structure</h1>
        <div id="graph"></div>
        
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color node-homepage"></div>
                <span>Homepage</span>
            </div>
            <div class="legend-item">
                <div class="legend-color node-main-section"></div>
                <span>Main Sections</span>
            </div>
            <div class="legend-item">
                <div class="legend-color node-service"></div>
                <span>Services</span>
            </div>
            <div class="legend-item">
                <div class="legend-color node-admin"></div>
                <span>Admin/Backend</span>
            </div>
            <div class="legend-item">
                <div class="legend-color node-database"></div>
                <span>Databases</span>
            </div>
            <div class="legend-item">
                <div class="legend-color node-static"></div>
                <span>Static Assets</span>
            </div>
        </div>
        
        <div class="info-panel">
            <h3>📋 Website Overview</h3>
            <ul>
                <li><strong>Main Language:</strong> Arabic (RTL layout)</li>
                <li><strong>Technology Stack:</strong> PHP, HTML, CSS, JavaScript, SQLite databases</li>
                <li><strong>Key Features:</strong> Multi-language support, responsive design, admin panel, newsletter system</li>
                <li><strong>Services:</strong> 10 different service categories with dedicated pages</li>
                <li><strong>Interactive Elements:</strong> Contact forms, donation forms, volunteer applications, news management</li>
                <li><strong>Geographic Focus:</strong> Middle East region (Iraq, Syria, Lebanon, Turkey, etc.)</li>
            </ul>
        </div>
    </div>

    <script>
        // Website structure data
        const data = {
            nodes: [
                // Homepage
                { id: "index.php", name: "Homepage\n(index.php)", type: "homepage", level: 0 },
                
                // Main sections
                { id: "about", name: "About\n(حول المنظمة)", type: "main-section", level: 1 },
                { id: "services", name: "Services\n(خدماتنا)", type: "main-section", level: 1 },
                { id: "area-work", name: "Area of Work\n(أين نعمل؟)", type: "main-section", level: 1 },
                { id: "news", name: "News\n(الأخبار)", type: "main-section", level: 1 },
                { id: "join-donate", name: "Join/Donate\n(انضم إلينا/تبرع)", type: "main-section", level: 1 },
                { id: "contact", name: "Contact\n(اتصل بنا)", type: "main-section", level: 1 },
                
                // Services
                { id: "health-center", name: "Health Center\n(المركز الصحي)", type: "service", level: 2 },
                { id: "health-services", name: "Health Services\n(الخدمات الصحية)", type: "service", level: 2 },
                { id: "poverty-services", name: "Poverty Services\n(خدمة الفقراء)", type: "service", level: 2 },
                { id: "special-needs", name: "Special Needs\n(ذوي الاحتياجات الخاصة)", type: "service", level: 2 },
                { id: "family-awareness", name: "Family Awareness\n(التوعية الأسرية)", type: "service", level: 2 },
                { id: "children-services", name: "Children Services\n(خدمة الأطفال)", type: "service", level: 2 },
                { id: "teen-services", name: "Teen Services\n(خدمة المراهقين)", type: "service", level: 2 },
                { id: "survivor-services", name: "Survivor Services\n(خدمة الناجيات)", type: "service", level: 2 },
                { id: "psychological-support", name: "Psychological Support\n(الدعم النفسي)", type: "service", level: 2 },
                { id: "small-projects", name: "Small Projects\n(المشروعات الصغيرة)", type: "service", level: 2 },
                
                // Join/Donate sub-pages
                { id: "volunteer", name: "Volunteer\n(تطوع)", type: "service", level: 2 },
                { id: "financial-donation", name: "Financial Donation\n(تبرع مالي)", type: "service", level: 2 },
                { id: "in-kind-donation", name: "In-Kind Donation\n(تبرع عيني)", type: "service", level: 2 },
                { id: "monthly-donation", name: "Monthly Donation\n(تبرع شهري)", type: "service", level: 2 },
                { id: "partnership", name: "Partnership\n(شراكة)", type: "service", level: 2 },
                { id: "job-application", name: "Job Application\n(طلب وظيفة)", type: "service", level: 2 },
                
                // Admin/Backend
                { id: "news-admin", name: "News Admin\nPanel", type: "admin", level: 2 },
                { id: "form-processing", name: "Form Processing\nSystem", type: "admin", level: 2 },
                { id: "newsletter", name: "Newsletter\nSystem", type: "admin", level: 2 },
                
                // Databases
                { id: "news-db", name: "News\nDatabase", type: "database", level: 3 },
                { id: "contact-db", name: "Contact Forms\nDatabase", type: "database", level: 3 },
                { id: "donation-db", name: "Donation Forms\nDatabase", type: "database", level: 3 },
                { id: "newsletter-db", name: "Newsletter\nDatabase", type: "database", level: 3 },
                
                // Static assets
                { id: "css", name: "CSS Styles\n(Bootstrap, Custom)", type: "static", level: 3 },
                { id: "js", name: "JavaScript\n(jQuery, Plugins)", type: "static", level: 3 },
                { id: "images", name: "Images\n(Gallery, Assets)", type: "static", level: 3 },
                { id: "fonts", name: "Fonts\n(FontAwesome, Icons)", type: "static", level: 3 }
            ],
            links: [
                // Homepage to main sections
                { source: "index.php", target: "about" },
                { source: "index.php", target: "services" },
                { source: "index.php", target: "area-work" },
                { source: "index.php", target: "news" },
                { source: "index.php", target: "join-donate" },
                { source: "index.php", target: "contact" },
                
                // Services connections
                { source: "services", target: "health-center" },
                { source: "services", target: "health-services" },
                { source: "services", target: "poverty-services" },
                { source: "services", target: "special-needs" },
                { source: "services", target: "family-awareness" },
                { source: "services", target: "children-services" },
                { source: "services", target: "teen-services" },
                { source: "services", target: "survivor-services" },
                { source: "services", target: "psychological-support" },
                { source: "services", target: "small-projects" },
                
                // Join/Donate connections
                { source: "join-donate", target: "volunteer" },
                { source: "join-donate", target: "financial-donation" },
                { source: "join-donate", target: "in-kind-donation" },
                { source: "join-donate", target: "monthly-donation" },
                { source: "join-donate", target: "partnership" },
                { source: "join-donate", target: "job-application" },
                
                // Admin connections
                { source: "news", target: "news-admin" },
                { source: "contact", target: "form-processing" },
                { source: "join-donate", target: "form-processing" },
                { source: "index.php", target: "newsletter" },
                
                // Database connections
                { source: "news-admin", target: "news-db" },
                { source: "form-processing", target: "contact-db" },
                { source: "form-processing", target: "donation-db" },
                { source: "newsletter", target: "newsletter-db" },
                
                // Static asset connections
                { source: "index.php", target: "css" },
                { source: "index.php", target: "js" },
                { source: "index.php", target: "images" },
                { source: "index.php", target: "fonts" }
            ]
        };

        // Set up the SVG
        const width = 1340;
        const height = 800;
        const svg = d3.select("#graph").append("svg")
            .attr("width", width)
            .attr("height", height);

        // Create force simulation
        const simulation = d3.forceSimulation(data.nodes)
            .force("link", d3.forceLink(data.links).id(d => d.id).distance(100))
            .force("charge", d3.forceManyBody().strength(-300))
            .force("center", d3.forceCenter(width / 2, height / 2))
            .force("collision", d3.forceCollide().radius(50));

        // Create links
        const link = svg.append("g")
            .selectAll("line")
            .data(data.links)
            .enter().append("line")
            .attr("class", "link");

        // Create nodes
        const node = svg.append("g")
            .selectAll("g")
            .data(data.nodes)
            .enter().append("g")
            .attr("class", "node")
            .call(d3.drag()
                .on("start", dragstarted)
                .on("drag", dragged)
                .on("end", dragended));

        // Add circles to nodes
        node.append("circle")
            .attr("r", d => d.type === "homepage" ? 25 : d.type === "main-section" ? 20 : 15)
            .attr("class", d => `node-${d.type}`);

        // Add text to nodes
        node.append("text")
            .attr("class", "node-text")
            .attr("dy", "0.35em")
            .style("font-size", d => d.type === "homepage" ? "10px" : d.type === "main-section" ? "9px" : "8px")
            .text(d => d.name);

        // Update positions on simulation tick
        simulation.on("tick", () => {
            link
                .attr("x1", d => d.source.x)
                .attr("y1", d => d.source.y)
                .attr("x2", d => d.target.x)
                .attr("y2", d => d.target.y);

            node
                .attr("transform", d => `translate(${d.x},${d.y})`);
        });

        // Drag functions
        function dragstarted(event, d) {
            if (!event.active) simulation.alphaTarget(0.3).restart();
            d.fx = d.x;
            d.fy = d.y;
        }

        function dragged(event, d) {
            d.fx = event.x;
            d.fy = event.y;
        }

        function dragended(event, d) {
            if (!event.active) simulation.alphaTarget(0);
            d.fx = null;
            d.fy = null;
        }
    </script>
</body>
</html>
