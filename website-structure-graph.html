<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Holy Work Organization - Website Structure</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            overflow-x: auto;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            max-width: 1500px;
            margin: 0 auto;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        h1 {
            text-align: center;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 40px;
            font-size: 3em;
            font-weight: 700;
            text-shadow: none;
            letter-spacing: -1px;
        }

        #graph {
            width: 100%;
            height: 900px;
            border: none;
            border-radius: 15px;
            background: radial-gradient(circle at 30% 20%, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.05) 50%, transparent 100%),
                        radial-gradient(circle at 70% 80%, rgba(240, 147, 251, 0.1) 0%, rgba(102, 126, 234, 0.05) 50%, transparent 100%),
                        #fafbfc;
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.05);
            position: relative;
            overflow: hidden;
        }

        .node {
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            filter: drop-shadow(0 4px 8px rgba(0,0,0,0.1));
        }

        .node:hover {
            transform: scale(1.15);
            filter: drop-shadow(0 8px 16px rgba(0,0,0,0.2));
        }

        .node circle {
            stroke-width: 3;
            transition: all 0.3s ease;
        }

        .node:hover circle {
            stroke-width: 4;
        }

        .node-homepage {
            fill: linear-gradient(135deg, #ff6b6b, #ee5a24);
            stroke: #c44569;
        }
        .node-main-section {
            fill: linear-gradient(135deg, #4834d4, #686de0);
            stroke: #30336b;
        }
        .node-service {
            fill: linear-gradient(135deg, #00d2d3, #01a3a4);
            stroke: #006266;
        }
        .node-admin {
            fill: linear-gradient(135deg, #ff9ff3, #f368e0);
            stroke: #b83dba;
        }
        .node-database {
            fill: linear-gradient(135deg, #feca57, #ff9ff3);
            stroke: #ff6348;
        }
        .node-static {
            fill: linear-gradient(135deg, #a4b0be, #747d8c);
            stroke: #57606f;
        }

        .link {
            fill: none;
            stroke: rgba(116, 125, 140, 0.4);
            stroke-width: 2;
            transition: all 0.3s ease;
            opacity: 0.7;
        }

        .link:hover {
            stroke: rgba(102, 126, 234, 0.8);
            stroke-width: 4;
            opacity: 1;
        }

        .node-text {
            font-family: 'Inter', sans-serif;
            font-size: 11px;
            font-weight: 600;
            text-anchor: middle;
            dominant-baseline: middle;
            fill: white;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            pointer-events: none;
        }

        .legend {
            margin-top: 30px;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 15px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 20px;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border-radius: 25px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            font-weight: 500;
            color: #2c3e50;
        }

        .legend-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            background: rgba(255, 255, 255, 0.95);
        }

        .legend-color {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            border: 3px solid rgba(255, 255, 255, 0.8);
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .info-panel {
            margin-top: 30px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(15px);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .info-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
        }

        .info-panel h3 {
            margin-top: 0;
            color: #2c3e50;
            font-size: 1.5em;
            font-weight: 600;
            margin-bottom: 20px;
        }

        .info-panel ul {
            margin: 15px 0;
            padding-left: 0;
            list-style: none;
        }

        .info-panel li {
            margin: 12px 0;
            color: #555;
            padding-left: 25px;
            position: relative;
            line-height: 1.6;
        }

        .info-panel li::before {
            content: '✨';
            position: absolute;
            left: 0;
            top: 0;
        }

        .controls {
            margin-bottom: 20px;
            text-align: center;
        }

        .control-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 0 5px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 12px;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌟 Holy Work Organization - Website Structure</h1>

        <div class="controls">
            <button class="control-btn" onclick="resetLayout()">🔄 Reset Layout</button>
            <button class="control-btn" onclick="centerGraph()">🎯 Center View</button>
            <button class="control-btn" onclick="toggleAnimation()">⚡ Toggle Animation</button>
        </div>

        <div id="graph"></div>
        <div class="tooltip" id="tooltip"></div>

        <div class="legend">
            <div class="legend-item">
                <div class="legend-color node-homepage"></div>
                <span>Homepage</span>
            </div>
            <div class="legend-item">
                <div class="legend-color node-main-section"></div>
                <span>Main Sections</span>
            </div>
            <div class="legend-item">
                <div class="legend-color node-service"></div>
                <span>Services</span>
            </div>
            <div class="legend-item">
                <div class="legend-color node-admin"></div>
                <span>Admin/Backend</span>
            </div>
            <div class="legend-item">
                <div class="legend-color node-database"></div>
                <span>Databases</span>
            </div>
            <div class="legend-item">
                <div class="legend-color node-static"></div>
                <span>Static Assets</span>
            </div>
        </div>

        <div class="info-panel">
            <h3>📋 Website Overview</h3>
            <ul>
                <li><strong>Main Language:</strong> Arabic (RTL layout)</li>
                <li><strong>Technology Stack:</strong> PHP, HTML, CSS, JavaScript, SQLite databases</li>
                <li><strong>Key Features:</strong> Multi-language support, responsive design, admin panel, newsletter system</li>
                <li><strong>Services:</strong> 10 different service categories with dedicated pages</li>
                <li><strong>Interactive Elements:</strong> Contact forms, donation forms, volunteer applications, news management</li>
                <li><strong>Geographic Focus:</strong> Middle East region (Iraq, Syria, Lebanon, Turkey, etc.)</li>
            </ul>
        </div>
    </div>

    <script>
        // Website structure data
        const data = {
            nodes: [
                // Homepage
                { id: "index.php", name: "Homepage\n(index.php)", type: "homepage", level: 0 },

                // Main sections
                { id: "about", name: "About\n(حول المنظمة)", type: "main-section", level: 1 },
                { id: "services", name: "Services\n(خدماتنا)", type: "main-section", level: 1 },
                { id: "area-work", name: "Area of Work\n(أين نعمل؟)", type: "main-section", level: 1 },
                { id: "news", name: "News\n(الأخبار)", type: "main-section", level: 1 },
                { id: "join-donate", name: "Join/Donate\n(انضم إلينا/تبرع)", type: "main-section", level: 1 },
                { id: "contact", name: "Contact\n(اتصل بنا)", type: "main-section", level: 1 },

                // Services
                { id: "health-center", name: "Health Center\n(المركز الصحي)", type: "service", level: 2 },
                { id: "health-services", name: "Health Services\n(الخدمات الصحية)", type: "service", level: 2 },
                { id: "poverty-services", name: "Poverty Services\n(خدمة الفقراء)", type: "service", level: 2 },
                { id: "special-needs", name: "Special Needs\n(ذوي الاحتياجات الخاصة)", type: "service", level: 2 },
                { id: "family-awareness", name: "Family Awareness\n(التوعية الأسرية)", type: "service", level: 2 },
                { id: "children-services", name: "Children Services\n(خدمة الأطفال)", type: "service", level: 2 },
                { id: "teen-services", name: "Teen Services\n(خدمة المراهقين)", type: "service", level: 2 },
                { id: "survivor-services", name: "Survivor Services\n(خدمة الناجيات)", type: "service", level: 2 },
                { id: "psychological-support", name: "Psychological Support\n(الدعم النفسي)", type: "service", level: 2 },
                { id: "small-projects", name: "Small Projects\n(المشروعات الصغيرة)", type: "service", level: 2 },

                // Join/Donate sub-pages
                { id: "volunteer", name: "Volunteer\n(تطوع)", type: "service", level: 2 },
                { id: "financial-donation", name: "Financial Donation\n(تبرع مالي)", type: "service", level: 2 },
                { id: "in-kind-donation", name: "In-Kind Donation\n(تبرع عيني)", type: "service", level: 2 },
                { id: "monthly-donation", name: "Monthly Donation\n(تبرع شهري)", type: "service", level: 2 },
                { id: "partnership", name: "Partnership\n(شراكة)", type: "service", level: 2 },
                { id: "job-application", name: "Job Application\n(طلب وظيفة)", type: "service", level: 2 },

                // Admin/Backend
                { id: "news-admin", name: "News Admin\nPanel", type: "admin", level: 2 },
                { id: "form-processing", name: "Form Processing\nSystem", type: "admin", level: 2 },
                { id: "newsletter", name: "Newsletter\nSystem", type: "admin", level: 2 },

                // Databases
                { id: "news-db", name: "News\nDatabase", type: "database", level: 3 },
                { id: "contact-db", name: "Contact Forms\nDatabase", type: "database", level: 3 },
                { id: "donation-db", name: "Donation Forms\nDatabase", type: "database", level: 3 },
                { id: "newsletter-db", name: "Newsletter\nDatabase", type: "database", level: 3 },

                // Static assets
                { id: "css", name: "CSS Styles\n(Bootstrap, Custom)", type: "static", level: 3 },
                { id: "js", name: "JavaScript\n(jQuery, Plugins)", type: "static", level: 3 },
                { id: "images", name: "Images\n(Gallery, Assets)", type: "static", level: 3 },
                { id: "fonts", name: "Fonts\n(FontAwesome, Icons)", type: "static", level: 3 }
            ],
            links: [
                // Homepage to main sections
                { source: "index.php", target: "about" },
                { source: "index.php", target: "services" },
                { source: "index.php", target: "area-work" },
                { source: "index.php", target: "news" },
                { source: "index.php", target: "join-donate" },
                { source: "index.php", target: "contact" },

                // Services connections
                { source: "services", target: "health-center" },
                { source: "services", target: "health-services" },
                { source: "services", target: "poverty-services" },
                { source: "services", target: "special-needs" },
                { source: "services", target: "family-awareness" },
                { source: "services", target: "children-services" },
                { source: "services", target: "teen-services" },
                { source: "services", target: "survivor-services" },
                { source: "services", target: "psychological-support" },
                { source: "services", target: "small-projects" },

                // Join/Donate connections
                { source: "join-donate", target: "volunteer" },
                { source: "join-donate", target: "financial-donation" },
                { source: "join-donate", target: "in-kind-donation" },
                { source: "join-donate", target: "monthly-donation" },
                { source: "join-donate", target: "partnership" },
                { source: "join-donate", target: "job-application" },

                // Admin connections
                { source: "news", target: "news-admin" },
                { source: "contact", target: "form-processing" },
                { source: "join-donate", target: "form-processing" },
                { source: "index.php", target: "newsletter" },

                // Database connections
                { source: "news-admin", target: "news-db" },
                { source: "form-processing", target: "contact-db" },
                { source: "form-processing", target: "donation-db" },
                { source: "newsletter", target: "newsletter-db" },

                // Static asset connections
                { source: "index.php", target: "css" },
                { source: "index.php", target: "js" },
                { source: "index.php", target: "images" },
                { source: "index.php", target: "fonts" }
            ]
        };

        // Set up the SVG
        const width = 1460;
        const height = 900;
        const svg = d3.select("#graph").append("svg")
            .attr("width", width)
            .attr("height", height);

        // Add gradients for nodes
        const defs = svg.append("defs");

        // Homepage gradient
        const homepageGradient = defs.append("linearGradient")
            .attr("id", "homepage-gradient")
            .attr("gradientUnits", "objectBoundingBox")
            .attr("x1", "0%").attr("y1", "0%")
            .attr("x2", "100%").attr("y2", "100%");
        homepageGradient.append("stop").attr("offset", "0%").attr("stop-color", "#ff6b6b");
        homepageGradient.append("stop").attr("offset", "100%").attr("stop-color", "#ee5a24");

        // Main section gradient
        const mainGradient = defs.append("linearGradient")
            .attr("id", "main-gradient")
            .attr("gradientUnits", "objectBoundingBox")
            .attr("x1", "0%").attr("y1", "0%")
            .attr("x2", "100%").attr("y2", "100%");
        mainGradient.append("stop").attr("offset", "0%").attr("stop-color", "#4834d4");
        mainGradient.append("stop").attr("offset", "100%").attr("stop-color", "#686de0");

        // Service gradient
        const serviceGradient = defs.append("linearGradient")
            .attr("id", "service-gradient")
            .attr("gradientUnits", "objectBoundingBox")
            .attr("x1", "0%").attr("y1", "0%")
            .attr("x2", "100%").attr("y2", "100%");
        serviceGradient.append("stop").attr("offset", "0%").attr("stop-color", "#00d2d3");
        serviceGradient.append("stop").attr("offset", "100%").attr("stop-color", "#01a3a4");

        // Admin gradient
        const adminGradient = defs.append("linearGradient")
            .attr("id", "admin-gradient")
            .attr("gradientUnits", "objectBoundingBox")
            .attr("x1", "0%").attr("y1", "0%")
            .attr("x2", "100%").attr("y2", "100%");
        adminGradient.append("stop").attr("offset", "0%").attr("stop-color", "#ff9ff3");
        adminGradient.append("stop").attr("offset", "100%").attr("stop-color", "#f368e0");

        // Database gradient
        const databaseGradient = defs.append("linearGradient")
            .attr("id", "database-gradient")
            .attr("gradientUnits", "objectBoundingBox")
            .attr("x1", "0%").attr("y1", "0%")
            .attr("x2", "100%").attr("y2", "100%");
        databaseGradient.append("stop").attr("offset", "0%").attr("stop-color", "#feca57");
        databaseGradient.append("stop").attr("offset", "100%").attr("stop-color", "#ff9ff3");

        // Static gradient
        const staticGradient = defs.append("linearGradient")
            .attr("id", "static-gradient")
            .attr("gradientUnits", "objectBoundingBox")
            .attr("x1", "0%").attr("y1", "0%")
            .attr("x2", "100%").attr("y2", "100%");
        staticGradient.append("stop").attr("offset", "0%").attr("stop-color", "#a4b0be");
        staticGradient.append("stop").attr("offset", "100%").attr("stop-color", "#747d8c");

        // Create force simulation
        const simulation = d3.forceSimulation(data.nodes)
            .force("link", d3.forceLink(data.links).id(d => d.id).distance(120))
            .force("charge", d3.forceManyBody().strength(-400))
            .force("center", d3.forceCenter(width / 2, height / 2))
            .force("collision", d3.forceCollide().radius(60));

        // Create links
        const link = svg.append("g")
            .selectAll("line")
            .data(data.links)
            .enter().append("line")
            .attr("class", "link");

        // Create nodes
        const node = svg.append("g")
            .selectAll("g")
            .data(data.nodes)
            .enter().append("g")
            .attr("class", "node")
            .call(d3.drag()
                .on("start", dragstarted)
                .on("drag", dragged)
                .on("end", dragended));

        // Add circles to nodes with gradients
        node.append("circle")
            .attr("r", d => d.type === "homepage" ? 30 : d.type === "main-section" ? 25 : 18)
            .attr("fill", d => `url(#${d.type}-gradient)`)
            .attr("stroke", d => {
                const colors = {
                    "homepage": "#c44569",
                    "main-section": "#30336b",
                    "service": "#006266",
                    "admin": "#b83dba",
                    "database": "#ff6348",
                    "static": "#57606f"
                };
                return colors[d.type];
            })
            .attr("stroke-width", 3);

        // Add text to nodes
        node.append("text")
            .attr("class", "node-text")
            .attr("dy", "0.35em")
            .style("font-size", d => d.type === "homepage" ? "11px" : d.type === "main-section" ? "10px" : "9px")
            .text(d => d.name);

        // Add tooltip functionality
        const tooltip = d3.select("#tooltip");

        node.on("mouseover", function(event, d) {
            tooltip.transition()
                .duration(200)
                .style("opacity", .9);
            tooltip.html(getTooltipContent(d))
                .style("left", (event.pageX + 10) + "px")
                .style("top", (event.pageY - 28) + "px");
        })
        .on("mouseout", function(d) {
            tooltip.transition()
                .duration(500)
                .style("opacity", 0);
        });

        // Tooltip content function
        function getTooltipContent(d) {
            const descriptions = {
                "index.php": "Main homepage with navigation, slider, and overview",
                "about": "Organization information, mission, and team",
                "services": "Complete list of all services offered",
                "area-work": "Geographic regions where organization operates",
                "news": "Latest news and updates with admin management",
                "join-donate": "Volunteer and donation opportunities",
                "contact": "Contact information and forms",
                "health-center": "Primary healthcare services and facilities",
                "news-admin": "Administrative panel for managing news content",
                "form-processing": "Backend system for handling form submissions",
                "newsletter": "Email subscription and newsletter management"
            };
            return descriptions[d.id] || `${d.name} - Part of the website structure`;
        }

        // Update positions on simulation tick
        simulation.on("tick", () => {
            link
                .attr("x1", d => d.source.x)
                .attr("y1", d => d.source.y)
                .attr("x2", d => d.target.x)
                .attr("y2", d => d.target.y);

            node
                .attr("transform", d => `translate(${d.x},${d.y})`);
        });

        // Drag functions
        function dragstarted(event, d) {
            if (!event.active) simulation.alphaTarget(0.3).restart();
            d.fx = d.x;
            d.fy = d.y;
        }

        function dragged(event, d) {
            d.fx = event.x;
            d.fy = event.y;
        }

        function dragended(event, d) {
            if (!event.active) simulation.alphaTarget(0);
            d.fx = null;
            d.fy = null;
        }

        // Control functions
        let animationEnabled = true;

        function resetLayout() {
            data.nodes.forEach(d => {
                d.fx = null;
                d.fy = null;
            });
            simulation.alpha(1).restart();
        }

        function centerGraph() {
            const bounds = svg.node().getBBox();
            const fullWidth = bounds.width;
            const fullHeight = bounds.height;
            const centerX = bounds.x + fullWidth / 2;
            const centerY = bounds.y + fullHeight / 2;

            const scale = 0.8 / Math.max(fullWidth / width, fullHeight / height);
            const translate = [width / 2 - scale * centerX, height / 2 - scale * centerY];

            svg.transition()
                .duration(750)
                .call(d3.zoom().transform, d3.zoomIdentity.translate(translate[0], translate[1]).scale(scale));
        }

        function toggleAnimation() {
            animationEnabled = !animationEnabled;
            if (animationEnabled) {
                simulation.restart();
            } else {
                simulation.stop();
            }
        }

        // Add zoom functionality
        const zoom = d3.zoom()
            .scaleExtent([0.1, 4])
            .on("zoom", function(event) {
                svg.selectAll("g").attr("transform", event.transform);
            });

        svg.call(zoom);

        // Add subtle animation to links
        setInterval(() => {
            if (animationEnabled) {
                link.style("stroke-dasharray", "5,5")
                    .style("stroke-dashoffset", Math.random() * 10);
            }
        }, 2000);
    </script>
</body>
</html>
