<?php
// Validate required fields
$required = [
    'firstName', 'lastName', 'email', 'phone',
    'donationType', 'description', 'pickupAddress',
    'preferredDate', 'preferredTime', 'terms'
];
foreach ($required as $field) {
    if (empty($_POST[$field])) {
        die('يرجى تعبئة جميع الحقول المطلوبة.');
    }
}

// Sanitize input
$firstName = htmlspecialchars($_POST['firstName']);
$lastName = htmlspecialchars($_POST['lastName']);
$email = filter_var($_POST['email'], FILTER_VALIDATE_EMAIL);
$phone = htmlspecialchars($_POST['phone']);
$donationType = htmlspecialchars($_POST['donationType']);
$description = htmlspecialchars($_POST['description']);
$pickupAddress = htmlspecialchars($_POST['pickupAddress']);
$preferredDate = htmlspecialchars($_POST['preferredDate']);
$preferredTime = htmlspecialchars($_POST['preferredTime']);

// Store in SQLite database
try {
    $dbPath = __DIR__ . '/../join_donate_databases/donate_forms.db';
    $db = new PDO('sqlite:' . $dbPath);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Create table if not exists
    $db->exec("CREATE TABLE IF NOT EXISTS in_kind_donation (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        firstName TEXT,
        lastName TEXT,
        email TEXT,
        phone TEXT,
        donationType TEXT,
        description TEXT,
        pickupAddress TEXT,
        preferredDate TEXT,
        preferredTime TEXT,
        submitted_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )");

    // Insert data
    $stmt = $db->prepare("INSERT INTO in_kind_donation 
        (firstName, lastName, email, phone, donationType, description, pickupAddress, preferredDate, preferredTime)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
    $stmt->execute([
        $firstName, $lastName, $email, $phone, $donationType, $description, $pickupAddress, $preferredDate, $preferredTime
    ]);
} catch (PDOException $e) {
    die('خطأ في حفظ البيانات: ' . $e->getMessage());
}

// Send email and show message
$to = '<EMAIL>';
$subject = 'طلب تبرع عيني جديد';
$message = "
اسم المتبرع: $firstName $lastName
البريد الإلكتروني: $email
رقم الهاتف: $phone
نوع التبرع: $donationType
وصف التبرع: $description
عنوان الاستلام: $pickupAddress
التاريخ المفضل للاستلام: $preferredDate
الوقت المفضل للاستلام: $preferredTime
";
$headers = "From: $email\r\nContent-Type: text/plain; charset=utf-8";

$mailSuccess = mail($to, $subject, $message, $headers);

// Dynamic style based on mail result
$bg = $mailSuccess ? '#f8fff8' : '#fff3cd';
$border = $mailSuccess ? '#c3e6cb' : '#ffeeba';
?>
<style>
.inkind-success-container {
    max-width: 650px;
    margin: 40px auto;
    padding: 30px 20px;
    background: <?php echo $bg; ?>;
    border: 1.5px solid <?php echo $border; ?>;
    border-radius: 12px;
    text-align: center;
    font-family: 'Cairo', sans-serif;
    box-shadow: 0 2px 12px #e6f2e6;
    direction: rtl;
}
.inkind-success-container h2 { 
    color: <?php echo $mailSuccess ? '#28a745' : '#856404'; ?>;
    margin-bottom: 10px;
 }
.inkind-success-container p { font-size: 1.1em; margin-bottom: 18px; }
.inkind-success-btns button {
    margin: 5px 10px; padding: 10px 25px; border: none; border-radius: 5px;
    cursor: pointer; font-size: 1em; transition: background 0.2s;
}
.inkind-success-btns .back-btn { background: #007bff; color: #fff; }
.inkind-success-btns .view-btn { background: #17a2b8; color: #fff; }
.inkind-success-btns .pdf-btn { background: #ffc107; color: #212529; }
.inkind-form-details {
    display: none; text-align: right; direction: rtl; background: #fff;
    border: 1px solid #eee; padding: 24px 18px; border-radius: 8px;
    margin-top: 24px; font-size: 1.08em; box-shadow: 0 1px 8px #f3f3f3;
}
.inkind-form-details h3 {
    color: #17a2b8; margin-bottom: 18px; font-size: 1.2em;
    border-bottom: 1px solid #e0e0e0; padding-bottom: 8px;
}
.inkind-form-details p { margin: 8px 0; line-height: 1.7; word-break: break-word; }
.inkind-form-details strong { color: #007bff; min-width: 110px; display: inline-block; }
@media print {
    body * { visibility: hidden !important; }
    #inkind-details-print, #inkind-details-print * { visibility: visible !important; }
    #inkind-details-print {
        position: absolute; left: 0; top: 0; width: 100vw; background: #fff;
        box-shadow: none; border: none; padding: 0;
    }
    .inkind-success-btns, .inkind-success-container h2, .inkind-success-container p { display: none !important; }
    #inkind-details, #inkind-details * { display: block !important; visibility: visible !important; }
}
</style>
<div class="inkind-success-container">
    <h2>
        <?php echo $mailSuccess ? 'تم استلام طلب التبرع العيني' : 'تم حفظ طلبك'; ?>
    </h2>
    <p>
        <?php echo $mailSuccess ? 'شكراً لمساهمتك في دعم مشاريعنا الإنسانية!' : 'لكن حدث خطأ أثناء إرسال البريد الإلكتروني.'; ?>
    </p>
    <div class="inkind-success-btns">
        <button class="back-btn" onclick="window.location.href='../join_donate'">رجوع</button>
        <button class="view-btn" onclick="document.getElementById('inkind-details').style.display='block';">عرض التفاصيل</button>
        <button class="pdf-btn" onclick="printInkindDetails()">حفظ كملف PDF</button>
    </div>
    <div id="inkind-details" class="inkind-form-details">
        <div id="inkind-details-print">
            <h3>تفاصيل التبرع العيني:</h3>
            <p><strong>الاسم:</strong> <?php echo htmlspecialchars($firstName . ' ' . $lastName); ?></p>
            <p><strong>البريد الإلكتروني:</strong> <?php echo htmlspecialchars($email); ?></p>
            <p><strong>رقم الهاتف:</strong> <?php echo htmlspecialchars($phone); ?></p>
            <p><strong>نوع التبرع:</strong> <?php echo htmlspecialchars($donationType); ?></p>
            <p><strong>وصف التبرع:</strong> <?php echo nl2br(htmlspecialchars($description)); ?></p>
            <p><strong>عنوان الاستلام:</strong> <?php echo nl2br(htmlspecialchars($pickupAddress)); ?></p>
            <p><strong>التاريخ المفضل للاستلام:</strong> <?php echo htmlspecialchars($preferredDate); ?></p>
            <p><strong>الوقت المفضل للاستلام:</strong> <?php echo htmlspecialchars($preferredTime); ?></p>
        </div>
    </div>
</div>
<script>
function printInkindDetails() {
    var details = document.getElementById('inkind-details');
    var wasHidden = details.style.display === 'none' || window.getComputedStyle(details).display === 'none';
    details.style.display = 'block';

    function afterPrint() {
        if (wasHidden) details.style.display = 'none';
        window.removeEventListener('afterprint', afterPrint);
    }
    window.addEventListener('afterprint', afterPrint);

    // Add a short delay to ensure the section is rendered before printing
    setTimeout(function() {
        window.print();
    }, 150);
}
</script>