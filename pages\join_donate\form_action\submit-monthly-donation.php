<?php
// Validate required fields
$required = ['duration', 'startDate', 'firstName', 'lastName', 'email', 'phone', 'cardNumber', 'expiryDate', 'cvv', 'terms'];
foreach ($required as $field) {
    if (empty($_POST[$field])) {
        die('يرجى تعبئة جميع الحقول المطلوبة.');
    }
}

// Validate donation amount
$donationAmount = 0;
if (!empty($_POST['donationAmount'])) {
    $donationAmount = intval($_POST['donationAmount']);
}
if (!empty($_POST['customAmount'])) {
    $customAmount = intval($_POST['customAmount']);
    if ($customAmount > 0) $donationAmount = $customAmount;
}
if ($donationAmount <= 0) {
    die('يرجى اختيار أو إدخال قيمة تبرع صحيحة.');
}

// Sanitize input
$duration = htmlspecialchars($_POST['duration']);
$startDate = htmlspecialchars($_POST['startDate']);
$firstName = htmlspecialchars($_POST['firstName']);
$lastName = htmlspecialchars($_POST['lastName']);
$email = filter_var($_POST['email'], FILTER_VALIDATE_EMAIL);
$phone = htmlspecialchars($_POST['phone']);
// For demo: store only last 4 digits of card
$cardNumber = htmlspecialchars(substr(preg_replace('/\D/', '', $_POST['cardNumber']), -4));
$expiryDate = htmlspecialchars($_POST['expiryDate']);
$cvv = htmlspecialchars($_POST['cvv']);

// Store in SQLite database
try {
    $dbPath = __DIR__ . '/../join_donate_databases/donate_forms.db';
    $db = new PDO('sqlite:' . $dbPath);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Create table if not exists
    $db->exec("CREATE TABLE IF NOT EXISTS monthly_donation (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        donationAmount INTEGER,
        duration TEXT,
        startDate TEXT,
        firstName TEXT,
        lastName TEXT,
        email TEXT,
        phone TEXT,
        cardLast4 TEXT,
        expiryDate TEXT,
        submitted_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )");

    // Insert data
    $stmt = $db->prepare("INSERT INTO monthly_donation 
        (donationAmount, duration, startDate, firstName, lastName, email, phone, cardLast4, expiryDate)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
    $stmt->execute([
        $donationAmount, $duration, $startDate, $firstName, $lastName, $email, $phone, $cardNumber, $expiryDate
    ]);
} catch (PDOException $e) {
    die('خطأ في حفظ البيانات: ' . $e->getMessage());
}

// Confirmation message
?>
<style>
.monthly-success-container {
    max-width: 650px;
    margin: 40px auto;
    padding: 30px 20px;
    background: #f8fff8;
    border: 1.5px solid #c3e6cb;
    border-radius: 12px;
    text-align: center;
    font-family: 'Cairo', sans-serif;
    box-shadow: 0 2px 12px #e6f2e6;
    direction: rtl;
}
.monthly-success-container h2 { color: #28a745; margin-bottom: 10px; }
.monthly-success-container p { font-size: 1.1em; margin-bottom: 18px; }
.monthly-success-btns button {
    margin: 5px 10px; padding: 10px 25px; border: none; border-radius: 5px;
    cursor: pointer; font-size: 1em; transition: background 0.2s;
}
.monthly-success-btns .back-btn { background: #007bff; color: #fff; }
.monthly-success-btns .view-btn { background: #17a2b8; color: #fff; }
.monthly-success-btns .pdf-btn { background: #ffc107; color: #212529; }
.monthly-form-details {
    display: none; text-align: right; direction: rtl; background: #fff;
    border: 1px solid #eee; padding: 24px 18px; border-radius: 8px;
    margin-top: 24px; font-size: 1.08em; box-shadow: 0 1px 8px #f3f3f3;
}
.monthly-form-details h3 {
    color: #17a2b8; margin-bottom: 18px; font-size: 1.2em;
    border-bottom: 1px solid #e0e0e0; padding-bottom: 8px;
}
.monthly-form-details p { margin: 8px 0; line-height: 1.7; word-break: break-word; }
.monthly-form-details strong { color: #007bff; min-width: 110px; display: inline-block; }
@media print {
    body * { visibility: hidden !important; }
    #monthly-details, #monthly-details * { visibility: visible !important; display: block !important; }
    #monthly-details-print, #monthly-details-print * { visibility: visible !important; }
    #monthly-details-print {
        position: absolute; left: 0; top: 0; width: 100vw; background: #fff;
        box-shadow: none; border: none; padding: 0;
    }
    .monthly-success-btns, .monthly-success-container h2, .monthly-success-container p { display: none !important; }
}
</style>
<div class="monthly-success-container">
    <h2>تم استلام طلب التبرع الشهري</h2>
    <p>شكراً لمساهمتك المستمرة في دعم مشاريعنا الإنسانية!</p>
    <div class="monthly-success-btns">
        <button class="back-btn" onclick="window.location.href='../join_donate'">رجوع</button>
        <button class="view-btn" onclick="document.getElementById('monthly-details').style.display='block';">عرض التفاصيل</button>
        <button class="pdf-btn" onclick="printMonthlyDetails()">حفظ كملف PDF</button>
    </div>
    <div id="monthly-details" class="monthly-form-details">
        <div id="monthly-details-print">
            <h3>تفاصيل التبرع الشهري:</h3>
            <p><strong>قيمة التبرع الشهري:</strong> <?php echo htmlspecialchars($donationAmount); ?> $</p>
            <p><strong>مدة التبرع:</strong> <?php echo htmlspecialchars($duration); ?></p>
            <p><strong>تاريخ البدء:</strong> <?php echo htmlspecialchars($startDate); ?></p>
            <p><strong>الاسم:</strong> <?php echo htmlspecialchars($firstName . ' ' . $lastName); ?></p>
            <p><strong>البريد الإلكتروني:</strong> <?php echo htmlspecialchars($email); ?></p>
            <p><strong>رقم الهاتف:</strong> <?php echo htmlspecialchars($phone); ?></p>
            <p><strong>آخر 4 أرقام من البطاقة:</strong> <?php echo htmlspecialchars($cardNumber); ?></p>
            <p><strong>تاريخ الانتهاء:</strong> <?php echo htmlspecialchars($expiryDate); ?></p>
        </div>
    </div>
</div>
<script>
function printMonthlyDetails() {
    var details = document.getElementById('monthly-details');
    var wasHidden = details.style.display === 'none' || window.getComputedStyle(details).display === 'none';
    details.style.display = 'block';

    function afterPrint() {
        if (wasHidden) details.style.display = 'none';
        window.removeEventListener('afterprint', afterPrint);
    }
    window.addEventListener('afterprint', afterPrint);

    // For some browsers, a slight delay helps ensure rendering
    setTimeout(function() {
        window.print();
    }, 100);
}
</script>