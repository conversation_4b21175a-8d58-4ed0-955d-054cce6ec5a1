<?php
// This script generates a hashed password. Replace 'your_admin_password' with your desired password.
$plainPassword = 'hwo2000'; // Replace with your desired password
$hashedPassword = password_hash($plainPassword, PASSWORD_BCRYPT);
echo "Hashed Password: " . $hashedPassword;
echo " for the plain password " . $plainPassword;
// Hashed Password: $2y$10$1ZEZ.3TTcKRAqL0LCNHtqeD/ziq9S.0olHBuG0sfowB.bX2Z0f2ai
$oldhash = $hashedPassword;
$istrue = password_verify($plainPassword, $oldhash);
echo "<br>Is Password Valid: " . ($istrue ? 'true' : 'false'); // Is Password Valid: true
?>

<!--
INSERT INTO admin (username, password) 
VALUES ('admin', '$2y$10$Eg8j.ryJcJPsmzbU1Yaug.iZUcfudTNr4qPLS2wDJSJ3zUnc/kOTe');
-->

<!--
UPDATE admin
SET username = 'admin', password = '$2y$10$Eg8j.ryJcJPsmzbU1Yaug.iZUcfudTNr4qPLS2wDJSJ3zUnc/kOTe'
WHERE id = 3;
-->