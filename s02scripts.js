/*----------------------------------------------------------------
---------------------------Testimonials--------------------------
------------------------------------------------------------------*/

document.querySelectorAll('.news-column').forEach(column => {
    const bodyColumns = column.querySelectorAll('.body-news-column');
    let currentIndex = 0;

    function updateVisibility(direction) {
        bodyColumns.forEach((col, index) => {
            if (index === currentIndex) {
                col.style.transform = 'translateX(0)';
                col.style.zIndex = 1;
            } else {
                if (direction === 'prev' && index === (currentIndex + 1) % bodyColumns.length) {
                    col.style.transform = 'translateX(100%)';
                    col.style.zIndex = 0;
                } else if (direction === 'next' && index === (currentIndex - 1 + bodyColumns.length) % bodyColumns.length) {
                    col.style.transform = 'translateX(-100%)';
                    col.style.zIndex = 0;
                } else {
                    col.style.transform = 'translateX(100%)';
                    col.style.zIndex = 0;
                }
            }
        });

        setTimeout(() => {
            bodyColumns.forEach((col, index) => {
                if (index !== currentIndex) {
                    col.classList.remove('active');
                } else {
                    col.classList.add('active');
                }
            });
        }, 500);
    }

    column.querySelector('.prev-button').addEventListener('click', () => {
        currentIndex = (currentIndex > 0) ? currentIndex - 1 : bodyColumns.length - 1;
        updateVisibility('prev');
    });

    column.querySelector('.next-button').addEventListener('click', () => {
        currentIndex = (currentIndex < bodyColumns.length - 1) ? currentIndex + 1 : 0;
        updateVisibility('next');
    });

    updateVisibility();
});

/*----------------------------------------------------------------
--------------------------------END-------------------------------
------------------------------------------------------------------*/





/*----------------------------------------------------------------
-------------------------------Goals------------------------------
------------------------------------------------------------------*/

document.addEventListener('DOMContentLoaded', function() {
    const goalItems = document.querySelectorAll('.goal-item');

    goalItems.forEach(item => {
        const description = item.querySelector('.goal-description');
        description.style.overflow = 'hidden';
        description.style.height = '0';
        description.style.transition = 'height 0.7s ease';
    });

    goalItems.forEach(item => {
        item.addEventListener('click', function() {
            const description = this.querySelector('.goal-description');
            const isExpanded = description.style.height !== '0px' && description.style.height !== '0';

            goalItems.forEach(i => {
                const desc = i.querySelector('.goal-description');
                if(desc.style.height === 'auto') {
                    desc.style.height = desc.scrollHeight + 'px';
                    desc.offsetHeight; 
                }
                desc.style.height = '0';
                i.classList.remove('expanded');
            });

            if (!isExpanded) {
                description.style.display = 'block';
                const fullHeight = description.scrollHeight;
                description.style.height = fullHeight + 'px';
                this.classList.add('expanded');

                description.addEventListener('transitionend', function handler(e) {
                    if(e.propertyName === 'height') {
                        description.style.height = 'auto';
                        description.removeEventListener('transitionend', handler);
                    }
                });
            }
        });
    });
});

/*----------------------------------------------------------------
--------------------------------END-------------------------------
------------------------------------------------------------------*/





/*----------------------------------------------------------------
---------------------------Our Work Gallery-----------------------
------------------------------------------------------------------*/

let imageList = [
    "1.webp", "2.webp", "3.webp", "4.webp", "5.webp",
    "6.webp", "7.webp", "8.webp", "9.webp", "10.webp",
    "11.webp", "12.webp", "13.webp", "14.webp", "15.webp",
    "16.webp", "17.webp", "18.webp", "19.webp", "20.webp",
    "21.webp", "22.webp", "23.webp", "24.webp", "25.webp",
    "26.webp", "27.webp", "28.webp", "29.webp", "30.webp",
    "31.webp", "32.webp", "33.webp", "34.webp", "35.webp",
    "36.webp", "37.webp", "38.webp", "39.webp", "40.webp",
    "41.webp", "42.webp", "43.webp", "44.webp", "45.webp",
    "46.webp", "47.webp", "48.webp", "49.webp", "50.webp",
    "51.webp", "52.webp", "53.webp", "54.webp", "55.webp",
    "56.webp", "57.webp", "58.webp", "59.webp", "60.webp",
    "61.webp", "62.webp", "63.webp", "64.webp", "65.webp",
    "66.webp", "67.webp", "68.webp", "69.webp", "70.webp",
    "71.webp", "72.webp", "73.webp", "74.webp", "75.webp",
    "76.webp", "77.webp", "78.webp", "79.webp", "80.webp",
    "81.webp", "82.webp", "83.webp", "84.webp", "85.webp",
    "86.webp", "87.webp", "88.webp", "89.webp", "90.webp",
    "91.webp", "92.webp", "93.webp", "94.webp", "95.webp",
    "96.webp", "97.webp", "98.webp", "99.webp", "100.webp",
    "101.webp", "102.webp", "103.webp", "104.webp", "105.webp",
    "106.webp", "107.webp", "108.webp", "109.webp", "110.webp",
    "111.webp", "112.webp", "113.webp", "114.webp", "115.webp",
    "116.webp", "117.webp", "118.webp", "119.webp", "120.webp",
    "121.webp", "122.webp", "123.webp", "124.webp", "125.webp",
    "126.webp", "127.webp", "128.webp", "129.webp", "130.webp",
    "131.webp", "132.webp", "133.webp", "134.webp", "135.webp",
    "136.webp", "137.webp", "138.webp", "139.webp", "140.webp",
    "141.webp", "142.webp", "143.webp", "144.webp", "145.webp",
    "146.webp", "147.webp", "148.webp", "149.webp", "150.webp",
    "151.webp", "152.webp", "153.webp", "154.webp", "155.webp",
    "156.webp", "157.webp", "158.webp", "159.webp", "160.webp",
    "161.webp", "162.webp", "163.webp", "164.webp", "165.webp",
    "166.webp", "167.webp", "168.webp", "169.webp", "170.webp",
    "171.webp", "172.webp"
];

// Randomize the array
imageList.sort(() => Math.random() - 0.5);

// List of container selectors
const containerSelectors = [
    '.our-work-gallery-testimonials',
    '.gallery-testimonial-slider'
];

document.addEventListener("DOMContentLoaded", function() {
    containerSelectors.forEach(selector => {
        const container = document.querySelector(selector);
        if (container) {
            container.innerHTML = ''; // Clear any existing content

            // Create all containers up front
            const imgElements = [];
            imageList.forEach((filename, idx) => {
                const div = document.createElement('div');
                div.className = 'single-testimonial';
                const img = document.createElement('img');
                img.alt = '#';
                if (idx < 3) {
                    img.src = '/img/gallery/' + filename;
                }
                div.appendChild(img);
                container.appendChild(div);
                imgElements.push({img, filename, idx});
            });

            // Wait for first 3 images to load, then lazy-load the rest
            let loaded = 0;
            imgElements.slice(0, 3).forEach(({img}) => {
                img.onload = img.onerror = function() {
                    loaded++;
                    if (loaded === 3) {
                        // Hide preloader for gallery
                        document.querySelectorAll('.preloader').forEach(preloader => {
                            preloader.classList.add('preloader-deactivate');
                        });
                        // Now lazy-load the rest
                        setTimeout(() => {
                            imgElements.slice(3).forEach(({img, filename}) => {
                                img.src = '/img/gallery/' + filename;
                            });
                        }, 100);
                    }
                };
            });
        }
    });
});

/*----------------------------------------------------------------
--------------------------------END-------------------------------
------------------------------------------------------------------*/



/*----------------------------------------------------------------
-------------------------------Mail-------------------------------
------------------------------------------------------------------*/

document.getElementById('newsletter-form').addEventListener('submit', function(e) {
    e.preventDefault();
    var form = this;
    var formData = new FormData(form);
    var params = new URLSearchParams(formData).toString();
    var email = form.email.value;

    fetch(form.action + '?' + params)
        .then(response => response.text())
        .then(data => {
            showNewsletterModal(data, email);
            form.reset();
        })
        .catch(error => {
            showNewsletterModal('حدث خطأ أثناء الاشتراك. حاول مرة أخرى.', email);
        });
});

function showNewsletterModal(message, email) {
    document.getElementById('newsletter-modal-message').innerHTML = message;
    document.getElementById('newsletter-modal').style.display = 'flex';
    // Set unsubscribe link
    var unsub = document.getElementById('newsletter-modal-unsubscribe');
    unsub.style.display = email ? 'inline' : 'none';
    unsub.setAttribute('data-email', email);
}

document.getElementById('newsletter-modal-close').onclick = function() {
    document.getElementById('newsletter-modal').style.display = 'none';
};
document.getElementById('newsletter-modal-backdrop').onclick = function() {
    document.getElementById('newsletter-modal').style.display = 'none';
};

// Add Unsubscribe Confirmation Modal
(function() {
    // Create modal HTML
    const confirmModal = document.createElement('div');
    confirmModal.id = 'unsubscribe-confirm-modal';
    confirmModal.style.display = 'none';
    confirmModal.innerHTML = `
        <div id="unsubscribe-confirm-backdrop" style="position:fixed;left:0;top:0;width:100vw;height:100vh;background:rgba(0,0,0,0.5);z-index:10000;"></div>
        <div id="unsubscribe-confirm-content" style="position:fixed;z-index:10001;left:50%;top:50%;transform:translate(-50%,-50%);background:#fff;padding:32px 24px 24px 24px;border-radius:12px;min-width:320px;max-width:90vw;box-shadow:0 8px 32px rgba(0,0,0,0.2);text-align:center;">
            <div style="font-size:1.2em;margin-bottom:16px;">هل أنت متأكد أنك تريد إلغاء الاشتراك من النشرة البريدية؟</div>
            <div style="display:flex;gap:16px;justify-content:center;">
                <button id="unsubscribe-confirm-yes" class="btn btn-danger" style="padding: 10px; background: #dc3545;">نعم، إلغاء الاشتراك</button>
                <button id="unsubscribe-confirm-no" class="btn btn-secondary" style="padding: 10px; width: 100px;">لا</button>
            </div>
        </div>
    `;
    document.body.appendChild(confirmModal);

    // Show confirmation modal
    document.getElementById('newsletter-modal-unsubscribe').onclick = function(e) {
        e.preventDefault();
        const email = this.getAttribute('data-email');
        confirmModal.style.display = 'flex';
        confirmModal.setAttribute('data-email', email);
    };

    // Hide confirmation modal
    document.getElementById('unsubscribe-confirm-no').onclick = function() {
        confirmModal.style.display = 'none';
    };
    document.getElementById('unsubscribe-confirm-backdrop').onclick = function() {
        confirmModal.style.display = 'none';
    };

    // Handle unsubscribe
    document.getElementById('unsubscribe-confirm-yes').onclick = function() {
        const email = confirmModal.getAttribute('data-email');
        fetch('/mail/unsubscribe?email=' + encodeURIComponent(email))
            .then(response => response.text())
            .then(data => {
                confirmModal.style.display = 'none';
                document.getElementById('newsletter-modal-message').innerHTML = data;
                document.getElementById('newsletter-modal-unsubscribe').style.display = 'none';
            })
            .catch(() => {
                confirmModal.style.display = 'none';
                document.getElementById('newsletter-modal-message').innerHTML = 'حدث خطأ أثناء إلغاء الاشتراك.';
            });
    };
})();

/*----------------------------------------------------------------
--------------------------------END-------------------------------
------------------------------------------------------------------*/



/*----------------------------------------------------------------
---------------------------Search Functionality------------------
------------------------------------------------------------------*/

document.addEventListener('DOMContentLoaded', function() {
    // Check if there's a search parameter in the URL
    const urlParams = new URLSearchParams(window.location.search);
    const searchQuery = urlParams.get('search');
    
    if (searchQuery && searchQuery.trim() !== '') {
        performSearch(searchQuery);
    }
});

function performSearch(query) {
    // Normalize the search query (lowercase, trim)
    query = query.toLowerCase().trim();
    
    // Get all searchable content
    const searchableElements = document.querySelectorAll('h1, h2, h3, h4, h5, p, .single-what-Services, .news-content');
    const results = [];
    let totalMatches = 0;
    
    // Search through the elements
    searchableElements.forEach(element => {
        const originalText = element.innerHTML;
        const text = element.textContent.toLowerCase();
        
        if (text.includes(query)) {
            // Highlight the matching text
            const highlightedText = originalText.replace(
                new RegExp(query, 'gi'),
                match => `<span class="search-highlight">${match}</span>`
            );
            
            element.innerHTML = highlightedText;
            totalMatches++;
            
            // Store the element for navigation
            results.push(element);
        }
    });
    
    // Create search navigation popup
    if (totalMatches > 0) {
        createSearchNavigator(results, query, totalMatches);
    } else {
        showNoResultsMessage(query);
    }
}

function createSearchNavigator(results, query, totalMatches) {
    // Create the navigator element
    const navigator = document.createElement('div');
    navigator.id = 'search-navigator';
    navigator.className = 'search-navigator';
    
    // Add CSS for the navigator
    const style = document.createElement('style');
    style.textContent = `
        .search-highlight {
            background-color: #ffeb3b;
            padding: 2px 0;
        }
        .search-navigator {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            padding: 12px 16px;
            z-index: 9999;
            direction: rtl;
            max-width: 320px;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        .search-navigator-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .search-navigator-title {
            font-weight: bold;
            font-size: 14px;
        }
        .search-navigator-close {
            cursor: pointer;
            font-size: 18px;
            color: #666;
        }
        .search-navigator-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .search-navigator-button {
            background: #1A76D1;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 6px 12px;
            cursor: pointer;
        }
        .search-navigator-counter {
            font-size: 14px;
        }
    `;
    document.head.appendChild(style);
    
    // Create navigator content
    navigator.innerHTML = `
        <div class="search-navigator-header">
            <div class="search-navigator-title">نتائج البحث: "${query}"</div>
            <div class="search-navigator-close">&times;</div>
        </div>
        <div class="search-navigator-info">تم العثور على ${totalMatches} نتيجة</div>
        <div class="search-navigator-controls">
            <button class="search-navigator-button prev-result">السابق</button>
            <div class="search-navigator-counter">1 / ${results.length}</div>
            <button class="search-navigator-button next-result">التالي</button>
        </div>
    `;
    
    document.body.appendChild(navigator);
    
    // Set up navigation
    let currentIndex = 0;
    
    // Scroll to first result
    scrollToElement(results[currentIndex]);
    
    // Set up event listeners
    navigator.querySelector('.search-navigator-close').addEventListener('click', function() {
        navigator.remove();
        // Remove highlights
        document.querySelectorAll('.search-highlight').forEach(el => {
            el.outerHTML = el.innerHTML;
        });
    });
    
    navigator.querySelector('.prev-result').addEventListener('click', function() {
        currentIndex = (currentIndex > 0) ? currentIndex - 1 : results.length - 1;
        scrollToElement(results[currentIndex]);
        updateCounter();
    });
    
    navigator.querySelector('.next-result').addEventListener('click', function() {
        currentIndex = (currentIndex < results.length - 1) ? currentIndex + 1 : 0;
        scrollToElement(results[currentIndex]);
        updateCounter();
    });
    
    function updateCounter() {
        navigator.querySelector('.search-navigator-counter').textContent = `${currentIndex + 1} / ${results.length}`;
    }
}

function scrollToElement(element) {
    // Scroll the element into view
    element.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
    });
    
    // Add a temporary focus effect
    element.style.transition = 'outline 0.3s ease';
    element.style.outline = '3px solid #1A76D1';
    
    setTimeout(() => {
        element.style.outline = 'none';
    }, 1500);
}

function showNoResultsMessage(query) {
    // Create a small notification for no results
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        padding: 16px;
        z-index: 9999;
        direction: rtl;
        max-width: 300px;
    `;
    
    notification.innerHTML = `
        <div style="margin-bottom: 8px; font-weight: bold;">لم يتم العثور على نتائج</div>
        <div style="margin-bottom: 12px;">لم يتم العثور على "${query}" في هذه الصفحة</div>
        <button id="close-search-notification" style="background: #1A76D1; color: white; border: none; border-radius: 4px; padding: 6px 12px; cursor: pointer;">إغلاق</button>
    `;
    
    document.body.appendChild(notification);
    
    document.getElementById('close-search-notification').addEventListener('click', function() {
        notification.remove();
    });
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (document.body.contains(notification)) {
            notification.remove();
        }
    }, 5000);
}

/*----------------------------------------------------------------
--------------------------------END-------------------------------
------------------------------------------------------------------*/
