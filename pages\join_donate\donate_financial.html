<!DOCTYPE html>
<html lang="ar" dir="rtl">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>تبرع مالي - منظمة العمل المقدس الدولية</title>

        <!-- Favicon -->
        <link rel="icon" type="image/png" href="/img/favicon.png">

        <!-- Google Fonts -->
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700&display=swap" rel="stylesheet">
        
        <!-- CSS Files -->
        <link rel="stylesheet" href="/css/bootstrap.min.css">
        <link rel="stylesheet" href="/css/icofont.css">
        <link rel="stylesheet" href="/style.css">
        <link rel="stylesheet" href="/css/responsive.css">
        <link rel="stylesheet" href="/pages/join_donate/forms.css">

        <style>
            #donateUnavailableModal {
                display: none;
                position: fixed;
                top: 0; left: 0;
                width: 100vw; height: 100vh;
                background: rgba(0,0,0,0.45);
                z-index: 9999;
                transition: background 0.3s;
            }
            #donateUnavailableModal .modal-content {
                background: #fff;
                max-width: 400px;
                margin: 120px auto;
                padding: 30px 20px 20px 20px;
                border-radius: 14px;
                text-align: center;
                box-shadow: 0 2px 16px #0002;
                position: relative;
                animation: popupAppear 0.4s;
            }
            #donateUnavailableModal h4 {
                color: #c00;
                margin-bottom: 18px;
                font-size: 1.4em;
                font-weight: bold;
                letter-spacing: 1px;
            }
            #donateUnavailableModal p {
                font-size: 1.1em;
                color: #333;
                margin-bottom: 18px;
            }
            #donateUnavailableModal a {
                display: inline-block;
                margin: 18px 0 0 0;
                color: #fff;
                background: #1A76D1;
                padding: 10px 24px;
                border-radius: 5px;
                text-decoration: none;
                font-weight: 500;
                font-size: 1.05em;
                transition: background 0.2s;
            }
            #donateUnavailableModal a:hover {
                background: #0056b3;
            }
            #donateUnavailableModal button {
                margin-right: 24px;
                margin-left: 24px;
                padding: 8px 22px;
                background: #888;
                color: #fff;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                font-size: 1em;
                transition: background 0.2s;
            }
            #donateUnavailableModal button:hover {
                background: #2C2D3F;
            }
            @keyframes popupAppear {
                from { transform: scale(0.8); opacity: 0; }
                to   { transform: scale(1); opacity: 1; }
            }
        </style>
    </head>
    <body>
        <!-- Header Area -->
        <!-- [Previous Header Code] -->

        <!-- Donation Form Section -->
        <section class="volunteer-form section">
            <div class="container">
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-12">
                        <div class="form-container">
                            <div class="section-title">
                                <h2>تبرع مالي</h2>
                                <p>تبرعك يساعدنا في تنفيذ مشاريعنا الإنسانية ودعم المحتاجين. اختر قيمة التبرع المناسبة لك أو أدخل قيمة مخصصة.</p>
                            </div>

                            <form class="donation-form" id="donationForm" method="POST" action="form_action/submit-donation">
                                <!-- Donation Amount -->
                                <div class="form-section">
                                    <h3>قيمة التبرع</h3>
                                    <div class="donation-amounts">
                                        <div class="row">
                                            <div class="col-lg-3 col-md-6">
                                                <div class="amount-option">
                                                    <input type="radio" id="amount50" name="donationAmount" value="50">
                                                    <label for="amount50">$50</label>
                                                </div>
                                            </div>
                                            <div class="col-lg-3 col-md-6">
                                                <div class="amount-option">
                                                    <input type="radio" id="amount100" name="donationAmount" value="100">
                                                    <label for="amount100">$100</label>
                                                </div>
                                            </div>
                                            <div class="col-lg-3 col-md-6">
                                                <div class="amount-option">
                                                    <input type="radio" id="amount200" name="donationAmount" value="200">
                                                    <label for="amount200">$200</label>
                                                </div>
                                            </div>
                                            <div class="col-lg-3 col-md-6">
                                                <div class="amount-option">
                                                    <input type="radio" id="amount500" name="donationAmount" value="500">
                                                    <label for="amount500">$500</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="custom-amount">
                                            <div class="form-group">
                                                <label for="customAmount">قيمة مخصصة ($)*</label>
                                                <input type="number" id="customAmount" name="customAmount" class="form-control" min="1">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Donation Purpose -->
                                <div class="form-section">
                                    <h3>الغرض من التبرع</h3>
                                    <div class="form-group">
                                        <label for="donationPurpose">اختر الغرض من التبرع*</label>
                                        <select id="donationPurpose" name="donationPurpose" class="form-control" required>
                                            <option value="">اختر الغرض من التبرع</option>
                                            <option value="emergency">الإغاثة الطارئة</option>
                                            <option value="education">دعم التعليم</option>
                                            <option value="health">الرعاية الصحية</option>
                                            <option value="food">الأمن الغذائي</option>
                                            <option value="general">دعم عام</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- Donor Information -->
                                <div class="form-section">
                                    <h3>معلومات المتبرع</h3>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="firstName">الاسم الأول*</label>
                                                <input type="text" id="firstName" name="firstName" class="form-control" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="lastName">اسم العائلة*</label>
                                                <input type="text" id="lastName" name="lastName" class="form-control" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="email">البريد الإلكتروني*</label>
                                                <input type="email" id="email" name="email" class="form-control" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="phone">رقم الهاتف*</label>
                                                <input type="tel" id="phone" name="phone" class="form-control" required>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Payment Information -->
                                <div class="form-section">
                                    <h3>معلومات الدفع</h3>
                                    <div class="form-group">
                                        <label for="cardNumber">رقم البطاقة*</label>
                                        <input type="text" id="cardNumber" name="cardNumber" class="form-control" required>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="expiryDate">تاريخ الانتهاء*</label>
                                                <input type="text" id="expiryDate" name="expiryDate" placeholder="MM/YY" class="form-control" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="cvv">رمز الأمان (CVV)*</label>
                                                <input type="text" id="cvv" name="cvv" class="form-control" required>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Terms and Submit -->
                                <div class="form-section">
                                    <div class="form-group">
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input" id="terms" name="terms" required>
                                            <label class="custom-control-label" for="terms">أوافق على الشروط والأحكام*</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group submit-group">
                                    <button type="submit" class="btn">تأكيد التبرع</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Modal Popup -->
        <div id="donateUnavailableModal">
          <div class="modal-content">
            <h4>عذراً</h4>
            <p>ميزة التبرع المالي عن طريق الاستمارة غير متاحة حالياً.<br>يرجى التواصل معنا لإتمام التبرع.</p>
            <a href="/pages/join_donate/donation_request?type=financial">تواصل معنا</a>
            <br>
            <button onclick="history.back();">إغلاق</button>
          </div>
        </div>

        <!-- Footer Area -->
        <!-- [Previous Footer Code] -->

        <!-- Scripts -->
        <script src="/js/jquery.min.js"></script>
        <script src="/js/bootstrap.min.js"></script>
        <script src="/pages/join_donate/forms.js"></script>
        <script>
            // Show the modal immediately on page load
            window.addEventListener('DOMContentLoaded', function() {
                document.getElementById('donateUnavailableModal').style.display = 'block';
            });
            // Prevent form submission just in case
            document.getElementById('donationForm').addEventListener('submit', function(e) {
                e.preventDefault();
                document.getElementById('donateUnavailableModal').style.display = 'block';
            });
        </script>
    </body>
</html>
