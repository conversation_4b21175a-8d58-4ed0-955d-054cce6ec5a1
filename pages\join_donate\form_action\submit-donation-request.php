<?php
// Validate required fields
$required = ['donationType', 'name', 'email', 'phone', 'message'];
foreach ($required as $field) {
    if (empty($_POST[$field])) {
        die('يرجى تعبئة جميع الحقول المطلوبة.');
    }
}

// Sanitize input
$donationType = htmlspecialchars($_POST['donationType']);
$name = htmlspecialchars($_POST['name']);
$email = filter_var($_POST['email'], FILTER_VALIDATE_EMAIL);
$phone = htmlspecialchars($_POST['phone']);
$messageText = htmlspecialchars($_POST['message']);

// Store in SQLite database
try {
    $dbPath = __DIR__ . '/../join_donate_databases/donate_forms.db';
    $db = new PDO('sqlite:' . $dbPath);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Create table if not exists
    $db->exec("CREATE TABLE IF NOT EXISTS donation_request (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        donationType TEXT,
        name TEXT,
        email TEXT,
        phone TEXT,
        message TEXT,
        submitted_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )");

    // Insert data
    $stmt = $db->prepare("INSERT INTO donation_request 
        (donationType, name, email, phone, message)
        VALUES (?, ?, ?, ?, ?)");
    $stmt->execute([
        $donationType, $name, $email, $phone, $messageText
    ]);
} catch (PDOException $e) {
    die('خطأ في حفظ البيانات: ' . $e->getMessage());
}

// Send email notification
$to = '<EMAIL>';
$subject = 'طلب تبرع جديد';
$message = "
نوع التبرع: $donationType
الاسم: $name
البريد الإلكتروني: $email
رقم الهاتف: $phone
الرسالة/تفاصيل التبرع:
$messageText
";
$headers = "From: $email\r\nContent-Type: text/plain; charset=utf-8";
$mailSuccess = mail($to, $subject, $message, $headers);

// Dynamic style based on mail result
$bg = $mailSuccess ? '#f8fff8' : '#fff3cd';
$border = $mailSuccess ? '#c3e6cb' : '#ffeeba';
?>
<div class="donation-request-success" style="max-width:650px;margin:0 auto;padding:30px 20px;background:<?php echo $bg; ?>;border:1.5px solid <?php echo $border; ?>;border-radius:12px;text-align:center;font-family:'Cairo',sans-serif;box-shadow:0 2px 12px #e6f2e6;direction:rtl;">
    <h2 style="color:<?php echo $mailSuccess ? '#28a745' : '#856404'; ?>;margin-bottom:10px;">
        <?php echo $mailSuccess ? 'تم استلام طلب التبرع' : 'تم حفظ طلبك'; ?>
    </h2>
    <p style="font-size:1.1em;margin-bottom:18px;">
        <?php echo $mailSuccess ? 'شكراً لتواصلك معنا! سنراجع طلبك ونتواصل معك قريباً.' : 'لكن حدث خطأ أثناء إرسال البريد الإلكتروني.'; ?>
    </p>
    <div class="donation-request-btns" style="margin-bottom:18px;">
        <button class="back-btn" onclick="window.location.href='/pages/join_donate/join_donate'" style="margin:5px 10px;padding:10px 25px;border:none;border-radius:5px;cursor:pointer;font-size:1em;background:#007bff;color:#fff;">رجوع</button>
        <button class="view-btn" onclick="document.getElementById('donation-request-details').style.display='block';" style="margin:5px 10px;padding:10px 25px;border:none;border-radius:5px;cursor:pointer;font-size:1em;background:#17a2b8;color:#fff;">عرض التفاصيل</button>
        <button class="pdf-btn" onclick="printDonationRequest()" style="margin:5px 10px;padding:10px 25px;border:none;border-radius:5px;cursor:pointer;font-size:1em;background:#ffc107;color:#212529;">حفظ كملف PDF</button>
    </div>
    <div id="donation-request-details" class="donation-request-details" style="display:none;text-align:right;direction:rtl;background:#fff;border:1px solid #eee;padding:24px 18px;border-radius:8px;margin-top:24px;font-size:1.08em;box-shadow:0 1px 8px #f3f3f3;">
        <div id="donation-request-print">
            <h3 style="color:#17a2b8;margin-bottom:18px;font-size:1.2em;border-bottom:1px solid #e0e0e0;padding-bottom:8px;">تفاصيل طلب التبرع:</h3>
            <p><strong style="color:#007bff;min-width:110px;display:inline-block;">نوع التبرع:</strong> <?php echo htmlspecialchars($donationType); ?></p>
            <p><strong style="color:#007bff;min-width:110px;display:inline-block;">الاسم:</strong> <?php echo htmlspecialchars($name); ?></p>
            <p><strong style="color:#007bff;min-width:110px;display:inline-block;">البريد الإلكتروني:</strong> <?php echo htmlspecialchars($email); ?></p>
            <p><strong style="color:#007bff;min-width:110px;display:inline-block;">رقم الهاتف:</strong> <?php echo htmlspecialchars($phone); ?></p>
            <p><strong style="color:#007bff;min-width:110px;display:inline-block;">الرسالة/تفاصيل التبرع:</strong> <?php echo nl2br(htmlspecialchars($messageText)); ?></p>
        </div>
    </div>
</div>
