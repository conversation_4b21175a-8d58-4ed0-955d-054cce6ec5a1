<?php
session_start();
if (!isset($_SESSION['admin_id'])) {
    header("Location: login");
    exit;
}

// Connect to the SQLite database.
$dbPath = __DIR__ . '/../db/news.db';
try {
    $pdo = new PDO("sqlite:" . $dbPath);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->exec("PRAGMA foreign_keys = ON");
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Retrieve filter and sort options from the form submission.
$sort_by = isset($_GET['sort_by']) ? $_GET['sort_by'] : 'id';
$order = isset($_GET['order']) ? $_GET['order'] : 'DESC';
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : '';
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : '';
$display = isset($_GET['display']) ? $_GET['display'] : '';
$show_on_home = isset($_GET['show_on_home']) ? $_GET['show_on_home'] : '';
$coming_news = isset($_GET['coming_news']) ? $_GET['coming_news'] : '';

// Build the SQL query with filters and sorting.
$query = "SELECT * FROM news WHERE 1=1";
$params = [];

if ($date_from) {
    $query .= " AND news_date >= :date_from";
    $params[':date_from'] = $date_from;
}
if ($date_to) {
    $query .= " AND news_date <= :date_to";
    $params[':date_to'] = $date_to;
}
if ($display !== '') {
    $query .= " AND display = :display";
    $params[':display'] = $display;
}
if ($show_on_home !== '') {
    $query .= " AND show_on_home = :show_on_home";
    $params[':show_on_home'] = $show_on_home;
}
if ($coming_news !== '') {
    $query .= " AND coming_news = :coming_news";
    $params[':coming_news'] = $coming_news;
}

$query .= " ORDER BY $sort_by $order";

$stmt = $pdo->prepare($query);
$stmt->execute($params);
$newsList = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Admin Dashboard</title>
  <link rel="stylesheet" href="../css/news_managing.css">
  <script src="/js/jquery.min.js"></script>
  <style>
    .modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.5); /* semi-transparent backdrop */
      display: none;
      align-items: center;
      justify-content: center;
      z-index: 9999;
    }
    .modal-dialog {
      margin: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
    }
    .modal-content {
      padding: 10px;
      background: #fff;
      border-radius: 8px;
      width: 90%;
      max-width: 400px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.3);
    }
    .modal-header {
      border-bottom: 1px solid #ddd;
      padding: 10px;
    }
    .modal-title {
      margin: 0;
      font-size: 24px;
      font-weight: bold;
      text-align: center;
      color: #333;
    }
    .modal-body {
      padding: 20px;
      font-size: 16px;
      color: #555;
    }
    .modal-footer {
      border-top: 1px solid #ddd;
      padding: 10px;
      text-align: right;
    }
    .btn {
      padding: 6px 12px;
      margin: 5px;
      border: none;
      border-radius: 5px;
      background-color: #ddd;
      color: #000;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    .btn:hover {
      color: #fff;
      background-color: #333;
    }
    .yes-delete {
      color: #fff;
      font-weight: bold;
      background-color: #e74c3c;
    }
    .yes-delete:hover {
      background-color: #f00;
      font-weight: bolder;
    }
  </style>
</head>
<body>
  <div class="dashboard-container">
    <h1>Welcome <?php echo htmlspecialchars($_SESSION['username']); ?> to the dashboard</h1>
    <p>Be careful, any change to the database can't be undone.</p><br>
    <p><a class="add-news-button" href="../tools/manage_news/news_add">Add News</a></p>
    <?php if (isset($_GET['success'])): ?>
      <p class="alert" style="color: #fff; font-width: bold;"><?php echo htmlspecialchars($_GET['success']); ?></p>
    <?php endif; ?>

    <!-- Filter and Sort Form -->
    <form class="filter-sort-dashboard" method="GET" action="dashboard">
      <div class="sorting">
        <label for="sort_by">Sort by: &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</label>
        <select name="sort_by" id="sort_by">
          <option value="id" <?php echo $sort_by == 'id' ? 'selected' : ''; ?>>ID</option>
          <option value="news_date" <?php echo $sort_by == 'news_date' ? 'selected' : ''; ?>>Date</option>
          <option value="title" <?php echo $sort_by == 'title' ? 'selected' : ''; ?>>Title</option>
        </select>
        <label for="order">Direction:</label>
        <select name="order" id="order">
          <option value="ASC" <?php echo $order == 'ASC' ? 'selected' : ''; ?>>Ascending</option>
          <option value="DESC" <?php echo $order == 'DESC' ? 'selected' : ''; ?>>Descending</option>
        </select>
      </div> 

      <div class="date-filter">
        <label for="date_from">Date from:</label>
        <input type="date" name="date_from" id="date_from" value="<?php echo htmlspecialchars($date_from); ?>">
        <label for="date_to">Date to:</label>
        <input type="date" name="date_to" id="date_to" value="<?php echo htmlspecialchars($date_to); ?>">
      </div>
      
      <div class="filter-options">
        <label for="display">Display:</label>
        <select name="display" id="display">
          <option value="" <?php echo $display === '' ? 'selected' : ''; ?>>All</option>
          <option value="1" <?php echo $display === '1' ? 'selected' : ''; ?>>Yes</option>
          <option value="0" <?php echo $display === '0' ? 'selected' : ''; ?>>No</option>
        </select>
        <label for="show_on_home">Show on Home:</label>
        <select name="show_on_home" id="show_on_home">
          <option value="" <?php echo $show_on_home === '' ? 'selected' : ''; ?>>All</option>
          <option value="1" <?php echo $show_on_home === '1' ? 'selected' : ''; ?>>Yes</option>
          <option value="0" <?php echo $show_on_home === '0' ? 'selected' : ''; ?>>No</option>
        </select>
        <label for="coming_news">Coming News:</label>
        <select name="coming_news" id="coming_news">
          <option value="" <?php echo $coming_news === '' ? 'selected' : ''; ?>>All</option>
          <option value="1" <?php echo $coming_news === '1' ? 'selected' : ''; ?>>Yes</option>
          <option value="0" <?php echo $coming_news === '0' ? 'selected' : ''; ?>>No</option>
        </select>
      </div>
      
      <button type="submit">Apply Filters</button>
    </form>
    <br>

    <table border="1" cellpadding="10" cellspacing="0">
      <thead>
        <tr>
          <th>ID</th>
          <th>Title</th>
          <th>Date</th>
          <th>Display</th>
          <th>Show on Home</th>
          <th>Coming News</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
      <?php if ($newsList): ?>
        <?php foreach ($newsList as $news): ?>
          <tr>
            <td class="tdid"><?php echo htmlspecialchars($news['id']); ?></td>
            <td><?php echo htmlspecialchars($news['title']); ?></td>
            <td class="tdmid tddate"><?php echo htmlspecialchars($news['news_date']); ?></td>
            <td class="tdmid tdynd"><?php echo $news['display'] ? 'Yes' : 'No'; ?></td>
            <td class="tdmid tdynh"><?php echo $news['show_on_home'] ? 'Yes' : 'No'; ?></td>
            <td class="tdmid tdync"><?php echo $news['coming_news'] ? 'Yes' : 'No'; ?></td>
            <td class="tdbutton">
              <a href="../tools/manage_news/news_edit?id=<?php echo $news['id']; ?>">Edit</a>
              |
              <a class="adelete delete-btn" href="../tools/manage_news/news_delete?id=<?php echo $news['id']; ?>">Delete</a>
            </td>
          </tr>
        <?php endforeach; ?>
      <?php else: ?>
          <tr>
              <td colspan="7">No news articles found.</td>
          </tr>
      <?php endif; ?>
      </tbody>
    </table>
    <p><a class="logout-button" href="logout">Logout</a></p>
  </div>

  <div class="modal" id="deleteModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Warning</h5>
        </div>
        <div class="modal-body">
          <p>Are you sure you want to delete this news article?</p>
        </div>
        <div class="modal-footer">
          <button type="button" id="confirmDelete" class="btn yes-delete">Yes, Delete</button>
          <button type="button" id="cancelDelete" class="btn no-cancel">Cancel</button>
        </div>
      </div>
    </div>
  </div>

  <script>
  // Simple modal handler using jQuery
  jQuery(document).ready(function($){
    var deleteUrl = '';
    $('.delete-btn').on('click', function(e){
      e.preventDefault();
      deleteUrl = $(this).attr('href');
      $('#deleteModal').fadeIn();
    });
    
    $('#cancelDelete').on('click', function(){
      $('#deleteModal').fadeOut();
      deleteUrl = '';
    });
    
    $('#confirmDelete').on('click', function(){
      window.location.href = deleteUrl;
    });
    
    // Optional: hide the modal if clicking outside the dialog
    $(document).on('click', function(e) {
      if($(e.target).closest('.modal-content').length === 0 && $(e.target).is('#deleteModal')){
        $('#deleteModal').fadeOut();
        deleteUrl = '';
      }
    });
  });
  </script>
</body>
</html>