<?php
// Basic validation
$required = ['firstName', 'lastName', 'email', 'phone', 'birthDate', 'nationality', 'volunteerArea', 'availability', 'motivation', 'terms'];
foreach ($required as $field) {
    if (empty($_POST[$field])) {
        die('يرجى تعبئة جميع الحقول المطلوبة.');
    }
}

// Sanitize input
$firstName = htmlspecialchars($_POST['firstName']);
$lastName = htmlspecialchars($_POST['lastName']);
$email = filter_var($_POST['email'], FILTER_VALIDATE_EMAIL);
$phone = htmlspecialchars($_POST['phone']);
$birthDate = htmlspecialchars($_POST['birthDate']);
$nationality = htmlspecialchars($_POST['nationality']);
$volunteerArea = htmlspecialchars($_POST['volunteerArea']);
$availability = htmlspecialchars($_POST['availability']);
$experience = htmlspecialchars($_POST['experience'] ?? '');
$motivation = htmlspecialchars($_POST['motivation']);
$skills = htmlspecialchars($_POST['skills'] ?? '');
$languages = htmlspecialchars($_POST['languages'] ?? '');

// Store in SQLite database
try {
    $dbPath = __DIR__ . '/../join_donate_databases/join_forms.db';
    $db = new PDO('sqlite:' . $dbPath);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Create table if not exists
    $db->exec("CREATE TABLE IF NOT EXISTS volunteer (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        firstName TEXT,
        lastName TEXT,
        email TEXT,
        phone TEXT,
        birthDate TEXT,
        nationality TEXT,
        volunteerArea TEXT,
        availability TEXT,
        experience TEXT,
        motivation TEXT,
        skills TEXT,
        languages TEXT,
        submitted_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )");

    // Insert data
    $stmt = $db->prepare("INSERT INTO volunteer 
        (firstName, lastName, email, phone, birthDate, nationality, volunteerArea, availability, experience, motivation, skills, languages)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    $stmt->execute([
        $firstName, $lastName, $email, $phone, $birthDate, $nationality,
        $volunteerArea, $availability, $experience, $motivation, $skills, $languages
    ]);
} catch (PDOException $e) {
    die('خطأ في حفظ البيانات: ' . $e->getMessage());
}

function showSuccessMessage($firstName, $lastName, $email, $phone, $birthDate, $nationality, $volunteerArea, $availability, $experience, $motivation, $skills, $languages, $mailSuccess = true) {
    $bg = $mailSuccess ? '#f8fff8' : '#fff3cd';
    $border = $mailSuccess ? '#c3e6cb' : '#ffeeba';
    ?>
    <style>
    .vol-success-container {
        max-width: 650px;
        margin: 40px auto;
        padding: 30px 20px;
        background: <?php echo $bg; ?>;
        border: 1.5px solid <?php echo $border; ?>;
        border-radius: 12px;
        text-align: center;
        font-family: 'Cairo', sans-serif;
        box-shadow: 0 2px 12px #e6f2e6;
        direction: rtl;
    }
    .vol-success-container h2 {
        color: <?php echo $mailSuccess ? '#28a745' : '#856404'; ?>;
        margin-bottom: 10px;
    }
    .vol-success-container p {
        font-size: 1.1em;
        margin-bottom: 18px;
    }
    .vol-success-btns button {
        margin: 5px 10px;
        padding: 10px 25px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 1em;
        transition: background 0.2s;
    }
    .vol-success-btns .back-btn { background: #007bff; color: #fff; }
    .vol-success-btns .view-btn { background: #17a2b8; color: #fff; }
    .vol-success-btns .pdf-btn { background: #ffc107; color: #212529; }
    .vol-form-details {
        display: none;
        text-align: right;
        direction: rtl;
        background: #fff;
        border: 1px solid #eee;
        padding: 24px 18px;
        border-radius: 8px;
        margin-top: 24px;
        font-size: 1.08em;
        box-shadow: 0 1px 8px #f3f3f3;
    }
    .vol-form-details h3 {
        color: #17a2b8;
        margin-bottom: 18px;
        font-size: 1.2em;
        border-bottom: 1px solid #e0e0e0;
        padding-bottom: 8px;
    }
    .vol-form-details p {
        margin: 8px 0;
        line-height: 1.7;
    }
    .vol-form-details strong {
        color: #007bff;
        min-width: 110px;
        display: inline-block;
    }
    @media print {
        body * { visibility: hidden !important; }
        #form-details-print, #form-details-print * { visibility: visible !important; }
        #form-details-print {
            position: absolute;
            left: 0; top: 0; width: 100vw; background: #fff;
            box-shadow: none; border: none; padding: 0;
        }
    }
    </style>
    <div class="vol-success-container">
        <h2>
            <?php echo $mailSuccess ? 'تم إرسال طلبك بنجاح' : 'تم حفظ طلبك'; ?>
        </h2>
        <p>
            <?php echo $mailSuccess ? 'شكراً لانضمامك إلينا!' : 'لكن حدث خطأ أثناء إرسال البريد الإلكتروني.'; ?>
        </p>
        <div class="vol-success-btns">
            <button class="back-btn" onclick="window.location.href='../join_donate'">رجوع</button>
            <button class="view-btn" onclick="document.getElementById('form-details').style.display='block';">عرض طلبي</button>
            <button class="pdf-btn" onclick="printFormDetails()">حفظ كملف PDF</button>
        </div>
        <div id="form-details" class="vol-form-details">
            <div id="form-details-print">
                <h3>معلومات الطلب:</h3>
                <p><strong>الاسم:</strong> <?php echo htmlspecialchars($firstName . ' ' . $lastName); ?></p>
                <p><strong>البريد الإلكتروني:</strong> <?php echo htmlspecialchars($email); ?></p>
                <p><strong>رقم الهاتف:</strong> <?php echo htmlspecialchars($phone); ?></p>
                <p><strong>تاريخ الميلاد:</strong> <?php echo htmlspecialchars($birthDate); ?></p>
                <p><strong>الجنسية:</strong> <?php echo htmlspecialchars($nationality); ?></p>
                <p><strong>مجال التطوع:</strong> <?php echo htmlspecialchars($volunteerArea); ?></p>
                <p><strong>الوقت المتاح:</strong> <?php echo htmlspecialchars($availability); ?></p>
                <p><strong>الخبرات السابقة:</strong> <?php echo nl2br(htmlspecialchars($experience)); ?></p>
                <p><strong>الدافع للتطوع:</strong> <?php echo nl2br(htmlspecialchars($motivation)); ?></p>
                <p><strong>المهارات:</strong> <?php echo nl2br(htmlspecialchars($skills)); ?></p>
                <p><strong>اللغات:</strong> <?php echo htmlspecialchars($languages); ?></p>
            </div>
        </div>
    </div>
    <script>
    function printFormDetails() {
        var details = document.getElementById('form-details');
        details.style.display = 'block';
        window.print();
    }
    </script>
    <?php
}

$to = '<EMAIL>';
$subject = 'طلب تطوع جديد';
$message = "
الاسم: $firstName $lastName
البريد الإلكتروني: $email
رقم الهاتف: $phone
تاريخ الميلاد: $birthDate
الجنسية: $nationality
مجال التطوع: $volunteerArea
الوقت المتاح: $availability
الخبرات السابقة: $experience
الدافع للتطوع: $motivation
المهارات: $skills
اللغات: $languages
";
$headers = "From: $email\r\nContent-Type: text/plain; charset=utf-8";

if (mail($to, $subject, $message, $headers)) {
    showSuccessMessage($firstName, $lastName, $email, $phone, $birthDate, $nationality, $volunteerArea, $availability, $experience, $motivation, $skills, $languages, true);
} else {
    showSuccessMessage($firstName, $lastName, $email, $phone, $birthDate, $nationality, $volunteerArea, $availability, $experience, $motivation, $skills, $languages, false);
}
?>