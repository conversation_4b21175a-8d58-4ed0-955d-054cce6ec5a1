<?php
session_start();
if (!isset($_SESSION['admin_id'])) {
    header("Location: login");
    exit;
}

// Connect to the SQLite database.
$dbPath = __DIR__ . '/../../db/news.db';
try {
    $pdo = new PDO('sqlite:' . $dbPath);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->exec("PRAGMA foreign_keys = ON");
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Include helper function for image conversion.
include_once '../conversion/image_converter';

// Process form submission.
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Retrieve and sanitize form inputs.
    $title     = trim($_POST['title']);
    $content   = trim($_POST['content']);
    $news_date = trim($_POST['news_date']); // Using a date input.

    // Retrieve checkbox values (if unchecked, set to 0).
    $display     = isset($_POST['display']) ? 1 : 0;
    $show_on_home = isset($_POST['show_on_home']) ? 1 : 0;
    $coming_news  = isset($_POST['coming_news']) ? 1 : 0;
    
    // Updated SQL insert statement with new columns.
    $stmt = $pdo->prepare("INSERT INTO news (title, content, news_date, author_id, display, show_on_home, coming_news) VALUES (:title, :content, :news_date, :author_id, :display, :show_on_home, :coming_news)");
    $stmt->bindParam(':title', $title);
    $stmt->bindParam(':content', $content);
    $stmt->bindParam(':news_date', $news_date);
    $stmt->bindParam(':author_id', $_SESSION['admin_id']);
    $stmt->bindParam(':display', $display);
    $stmt->bindParam(':show_on_home', $show_on_home);
    $stmt->bindParam(':coming_news', $coming_news);
    
    if ($stmt->execute()) {
        // Retrieve the id of the newly inserted news article.
        $news_id = $pdo->lastInsertId();
        
        // Handle image uploads if provided.
        // The form must have enctype="multipart/form-data"
        if (!empty($_FILES['images']['name'][0])) {
            // Loop through each uploaded image.
            foreach ($_FILES['images']['tmp_name'] as $index => $tmpName) {
                // Call the helper function to convert to WebP, rename and save the image.
                $convertedImagePath = convertAndSaveImage($tmpName, $news_date, $title, $index + 1);
                
                // If image conversion was successful, insert image record.
                if ($convertedImagePath !== false) {
                    $stmtImage = $pdo->prepare("INSERT INTO news_images (news_id, image_path, sequence) VALUES (:news_id, :image_path, :sequence)");
                    $stmtImage->bindParam(':news_id', $news_id);
                    $stmtImage->bindParam(':image_path', $convertedImagePath);
                    $stmtImage->bindParam(':sequence', $index);
                    $stmtImage->execute();
                }
            }
        }
        
        // Redirect to the dashboard with success message.
        header("Location: ../../admin/dashboard?success=News+added+successfully");
        exit;
    } else {
        $error = "Failed to add news. Please try again.";
    }
} else {
    $error = "";
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Add News</title>
  <link rel="stylesheet" href="../../css/news_managing.css">
  <link rel="stylesheet" href="../../../../css/font-awesome.min.css">
</head>
<body>
  <div class="add-news-container">
    <h1>Add News Article</h1>
    <?php if (!empty($error)): ?>
      <p class="alert"><?php echo htmlspecialchars($error); ?></p>
    <?php endif; ?>
    <!-- The form must support file uploads via enctype="multipart/form-data" -->
    <form method="POST" action="news_add" enctype="multipart/form-data">
      <label for="title">Title:</label>
      <input type="text" name="title" id="title" required /><br><br>
      
      <label for="news_date">Publication Date:</label>
      <input type="date" name="news_date" id="news_date" required /><br><br>
      
      <label for="content">Content:</label><br>
      <textarea name="content" id="content" placeholder="Write your news content here..."></textarea><br><br>
      
      <!-- Button to add image placeholder and corresponding file input -->
      <button class="add-button" type="button" id="addImageBtn">Add Image Placeholder</button>
      <div class="imageInput">
        <div id="imageInputsContainer"></div>
      </div>
      <hr>
      <!-- Replace the original checkboxes with the new styled markup -->
      <div class="ks-cboxtags">
        <ul class="ks-cboxtags">
          <li>
            <input type="checkbox" name="display" id="display" value="1" checked>
            <label for="display">Display News</label>
          </li>
          <li>
            <input type="checkbox" name="show_on_home" id="show_on_home" value="1">
            <label for="show_on_home">Show on Home Page</label>
          </li>
          <li>
            <input type="checkbox" name="coming_news" id="coming_news" value="1">
            <label for="coming_news">Mark as Coming News</label>
          </li>
        </ul>
      </div>
      <hr>
      <button type="submit">Add News</button>
    </form>
    <p><a href="../../admin/dashboard">Back to Dashboard</a></p>
  </div>
  <script src="../../js/news_managing.js"></script>
  
  <script>
    (function(){
      const contentTextarea = document.getElementById('content');
      const imageInputsContainer = document.getElementById('imageInputsContainer');
      // Regex to match the stable placeholder: {img:UID}
      const placeholderRegex = /{img:([^}]+)}/g;

      // Generate a unique ID (you may adjust the method as needed)
      function generateUID() {
        return Date.now() + Math.random().toString(36).substr(2, 5);
      }

      // Create a file input block for a given stable UID.
      // (The visible label will be updated later to be sequential.)
      function createInputBlock(uid) {
        const wrapper = document.createElement('div');
        wrapper.className = 'image-input-wrapper';
        // Store the stable id in a data attribute.
        wrapper.dataset.uid = uid;  

        const label = document.createElement('label');
        // Initially, show a temporary label; later sync will update it to "{imgX}".
        label.innerText = '{img?}';

        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.name = 'images[]';
        fileInput.required = true;

        // Remove button removes this block and its corresponding token from the textarea.
        const removeBtn = document.createElement('button');
        removeBtn.type = 'button';
        removeBtn.innerText = 'Remove';
        removeBtn.addEventListener('click', function(){
          wrapper.remove();
          const regex = new RegExp(`{img:${uid}}\\n?`, 'g');
          contentTextarea.value = contentTextarea.value.replace(regex, '');
          syncImageInputs();
        });

        wrapper.appendChild(label);
        wrapper.appendChild(document.createElement('br'));
        wrapper.appendChild(fileInput);
        wrapper.appendChild(document.createElement('br'));
        wrapper.appendChild(removeBtn);
        wrapper.appendChild(document.createElement('br'));
        return wrapper;
      }

      // Sync file input blocks with the placeholders in the textarea.
      // This routine:
      //   1. Reads the stable UIDs from the textarea (which are in the form "{img:UID}")
      //   2. Creates any missing file input blocks (preserving any chosen files)
      //   3. Removes blocks whose UID no longer appears in the text
      //   4. Reorders the blocks to match the order of UIDs in the text
      //   5. Updates each block's visible label to sequential numbers (like {img1}, {img2}, …)
      function syncImageInputs() {
        let uids = [];
        let match;
        // Extract UIDs (in order) from the textarea.
        while ((match = placeholderRegex.exec(contentTextarea.value)) !== null) {
          uids.push(match[1]);
        }

        // Ensure a block exists for each UID.
        uids.forEach(uid => {
          let block = imageInputsContainer.querySelector(`[data-uid="${uid}"]`);
          if (!block) {
            block = createInputBlock(uid);
            imageInputsContainer.appendChild(block);
          }
        });

        // Remove any blocks whose UID is no longer in the textarea.
        Array.from(imageInputsContainer.children).forEach(block => {
          if (!uids.includes(block.dataset.uid)) {
            imageInputsContainer.removeChild(block);
          }
        });

        // Reorder blocks to match the order in the textarea.
        uids.forEach(uid => {
          const block = imageInputsContainer.querySelector(`[data-uid="${uid}"]`);
          if (block) {
            imageInputsContainer.appendChild(block);
          }
        });

        // Update display labels according to the new sequential order.
        let counter = 1;
        Array.from(imageInputsContainer.children).forEach(block => {
          const label = block.querySelector('label');
          if (label) {
            label.innerText = `{img${counter}}`;
          }
          counter++;
        });
      }

      // When adding a new placeholder, insert it at the cursor's position.
      const addImageBtn = document.getElementById('addImageBtn');
      addImageBtn.addEventListener('click', function(){
        // Generate a new stable UID.
        const uid = generateUID();
        // Build the token with the stable UID.
        const newPlaceholder = `{img:${uid}}`;
        const start = contentTextarea.selectionStart;
        const end = contentTextarea.selectionEnd;
        const textBefore = contentTextarea.value.substring(0, start);
        const textAfter = contentTextarea.value.substring(end);
        // Insert the new placeholder token.
        if (textBefore === "") {
          contentTextarea.value = textBefore + newPlaceholder + "\n" + textAfter;
        } 
        else {
          contentTextarea.value = textBefore + "\n"  + newPlaceholder + "\n" + textAfter;
        }
        // Place the cursor after the inserted token.
        const newPos = start + newPlaceholder.length + 1;
        contentTextarea.selectionStart = contentTextarea.selectionEnd = newPos;
        syncImageInputs();
      });

      // Re-sync when the textarea content changes manually.
      contentTextarea.addEventListener('input', function(){
        syncImageInputs();
      });

      // Initial sync on page load.
      syncImageInputs();
    })();
  </script>
</body>
</html>