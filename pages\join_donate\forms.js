/*--------------------------------------------------------------------------------------------------------------------------------------------
----------------------------------------------------------------donate-financial--------------------------------------------------------------
--------------------------------------------------------------------------------------------------------------------------------------------*/


//-------------------------------------------------------------selecting the amount-----------------------------------------------------------
document.addEventListener('DOMContentLoaded', function() {
    // Handle donation amount selection
    const amountOptions = document.querySelectorAll('.amount-option');
    const customAmountInput = document.getElementById('customAmount');

    amountOptions.forEach(option => {
        option.addEventListener('click', function() {
            // Remove selected class from all options
            amountOptions.forEach(opt => {
                opt.classList.remove('selected');
                opt.querySelector('input[type="radio"]').checked = false;
            });

            // Select clicked option
            this.classList.add('selected');
            this.querySelector('input[type="radio"]').checked = true;

            // Clear custom amount when preset amount is selected
            if (customAmountInput) {
                customAmountInput.value = '';
            }
        });
    });

    // Handle custom amount input
    if (customAmountInput) {
        customAmountInput.addEventListener('input', function() {
            // If custom amount is entered, unselect preset amounts
            if (this.value) {
                amountOptions.forEach(opt => {
                    opt.classList.remove('selected');
                    opt.querySelector('input[type="radio"]').checked = false;
                });
            }
        });
    }

    // Format card number input
    const cardNumberInput = document.getElementById('cardNumber');
    if (cardNumberInput) {
        cardNumberInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            value = value.replace(/(.{4})/g, '$1 ').trim();
            e.target.value = value;
        });
    }

    // Format expiry date input
    const expiryDateInput = document.getElementById('expiryDate');
    if (expiryDateInput) {
        expiryDateInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length >= 2) {
                value = value.slice(0, 2) + '/' + value.slice(2, 4);
            }
            e.target.value = value;
        });
    }

    // Limit CVV to 3 or 4 digits
    const cvvInput = document.getElementById('cvv');
    if (cvvInput) {
        cvvInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            e.target.value = value.slice(0, 4);
        });
    }
});

/*--------------------------------------------------------------------------------------------------------------------------------------------
----------------------------------------------------------------donate-financial--------------------------------------------------------------
--------------------------------------------------------------------------------------------------------------------------------------------*/