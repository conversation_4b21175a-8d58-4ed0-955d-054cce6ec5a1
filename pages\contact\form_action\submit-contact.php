<?php
// Validate required fields
$required = ['name', 'subject', 'message'];
foreach ($required as $field) {
    if (empty($_POST[$field])) {
        die('يرجى تعبئة جميع الحقول المطلوبة.');
    }
}

// Sanitize input
$name = htmlspecialchars($_POST['name']);
$email = filter_var($_POST['email'], FILTER_VALIDATE_EMAIL);
$phone = htmlspecialchars($_POST['phone']);
$subject = htmlspecialchars($_POST['subject']);
$messageText = htmlspecialchars($_POST['message']);

// Store in SQLite database
try {
    $dbPath = __DIR__ . '/../contact_database/contact_forms.db';
    $db = new PDO('sqlite:' . $dbPath);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Create table if not exists
    $db->exec("CREATE TABLE IF NOT EXISTS contact_requests (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT,
        email TEXT,
        phone TEXT,
        subject TEXT,
        message TEXT,
        submitted_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )");

    // Insert data
    $stmt = $db->prepare("INSERT INTO contact_requests 
        (name, email, phone, subject, message)
        VALUES (?, ?, ?, ?, ?)");
    $stmt->execute([
        $name, $email, $phone, $subject, $messageText
    ]);
} catch (PDOException $e) {
    die('خطأ في حفظ البيانات: ' . $e->getMessage());
}

// Send email notification
$to = '<EMAIL>';
$emailSubject = 'رسالة جديدة من نموذج الاتصال';
$message = "
الاسم: $name
البريد الإلكتروني: $email
رقم الهاتف: $phone
الموضوع: $subject
الرسالة:
$messageText
";
$headers = "From: $email\r\nContent-Type: text/plain; charset=utf-8";
$mailSuccess =true; // mail($to, $emailSubject, $message, $headers);

// Dynamic style based on mail result
$bg = $mailSuccess ? '#f8fff8' : '#fff3cd';
$border = $mailSuccess ? '#c3e6cb' : '#ffeeba';
?>
<div class="contact-request-success"
     style="
        max-width:800px;
        min-height:auto;
        margin:0 auto;
        padding:10px 10px;
        background:<?php echo $bg; ?>;
        border:1.5px solid <?php echo $border; ?>;
        border-radius:14px;
        text-align:center;
        font-family:'Cairo',sans-serif;
        box-shadow:0 2px 12px #e6f2e6;
        direction:rtl;
        display:flex;
        flex-direction:column;
        justify-content:center;
        align-items:center;
     ">
    <h2 style="color:<?php echo $mailSuccess ? '#28a745' : '#856404'; ?>;margin-bottom:10px;margin-top:0;">
        <?php echo $mailSuccess ? 'تم إرسال الرسالة بنجاح' : 'تم حفظ الرسالة'; ?>
    </h2>
    <p style="font-size:1.1em;margin-bottom:18px;">
        <?php echo $mailSuccess ? 'شكراً لتواصلك معنا! سنقوم بالرد عليك في أقرب وقت ممكن.' : 'لكن حدث خطأ أثناء إرسال البريد الإلكتروني.'; ?>
    </p>
    <div class="contact-request-btns" style="margin-bottom:18px;">
        <button class="back-btn" onclick="document.getElementById('contactRequestModal').style.display='none';" style="margin:5px 10px;padding:10px 25px;border:none;border-radius:5px;cursor:pointer;font-size:1em;background:#007bff;color:#fff;">رجوع</button>
        <button class="view-btn" onclick="document.getElementById('contact-request-details').style.display='block';" style="margin:5px 10px;padding:10px 25px;border:none;border-radius:5px;cursor:pointer;font-size:1em;background:#17a2b8;color:#fff;">عرض التفاصيل</button>
        <button class="pdf-btn" onclick="printContactRequest()" style="margin:5px 10px;padding:10px 25px;border:none;border-radius:5px;cursor:pointer;font-size:1em;background:#ffc107;color:#212529;">حفظ كملف PDF</button>
    </div>
    <div id="contact-request-details" class="contact-request-details" style="display:none;text-align:right;direction:rtl;background:#fff;border:1px solid #eee;padding:24px 18px;border-radius:8px;margin-top:24px;font-size:1.08em;box-shadow:0 1px 8px #f3f3f3;">
        <div id="contact-request-print">
            <h3 style="color:#17a2b8;margin-bottom:18px;font-size:1.2em;border-bottom:1px solid #e0e0e0;padding-bottom:8px;">تفاصيل الرسالة:</h3>
            <p><strong style="color:#007bff;min-width:110px;display:inline-block;">الاسم:</strong> <?php echo htmlspecialchars($name); ?></p>
            <p><strong style="color:#007bff;min-width:110px;display:inline-block;">البريد الإلكتروني:</strong> <?php echo htmlspecialchars($email); ?></p>
            <p><strong style="color:#007bff;min-width:110px;display:inline-block;">رقم الهاتف:</strong> <?php echo htmlspecialchars($phone); ?></p>
            <p><strong style="color:#007bff;min-width:110px;display:inline-block;">الموضوع:</strong> <?php echo htmlspecialchars($subject); ?></p>
            <p><strong style="color:#007bff;min-width:110px;display:inline-block;">الرسالة:</strong> <?php echo nl2br(htmlspecialchars($messageText)); ?></p>
        </div>
    </div>
</div>
