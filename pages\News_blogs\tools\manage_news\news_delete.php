<?php
session_start();
if (!isset($_SESSION['admin_id'])) {
    header("Location: login");
    exit;
}

if (!isset($_GET['id'])) {
    die("News ID not specified.");
}

$news_id = $_GET['id'];
$dbPath = __DIR__ . '/../../db/news.db';

try {
    $pdo = new PDO("sqlite:" . $dbPath);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->exec("PRAGMA foreign_keys = ON");

    // Delete the news record. Associated images will be deleted via cascade.
    $stmt = $pdo->prepare("DELETE FROM news WHERE id = :id");
    $stmt->bindParam(':id', $news_id);
    $stmt->execute();

    header("Location: ../../admin/dashboard?success=News+deleted+successfully");
    exit;
} catch (PDOException $e) {
    die("Error deleting news: " . $e->getMessage());
}
?>