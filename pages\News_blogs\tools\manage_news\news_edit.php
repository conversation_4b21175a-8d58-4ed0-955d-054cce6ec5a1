<?php
session_start();
if (!isset($_SESSION['admin_id'])) {
    header("Location: login");
    exit;
}

if (!isset($_GET['id'])) {
    die("No news id provided.");
}

$dbPath = __DIR__ . '/../../db/news.db';
try {
    $pdo = new PDO('sqlite:' . $dbPath);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->exec("PRAGMA foreign_keys = ON");
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

$newsId = $_GET['id'];
$stmt = $pdo->prepare("SELECT * FROM news WHERE id = :id");
$stmt->bindParam(':id', $newsId);
$stmt->execute();
$news = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$news) {
    die("News article not found.");
}

// ... already fetched $news
$oldTitle = $news['title'];
$oldDate  = $news['news_date'];

$stmtImg = $pdo->prepare("SELECT * FROM news_images WHERE news_id = :news_id ORDER BY sequence ASC");
$stmtImg->bindParam(':news_id', $newsId);
$stmtImg->execute();
$newsImages = $stmtImg->fetchAll(PDO::FETCH_ASSOC);

$error = "";
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Retrieve and sanitize form inputs.
    $title     = trim($_POST['title']);
    $content   = trim($_POST['content']);
    $news_date = trim($_POST['news_date']);

    // Retrieve checkbox values.
    $display     = isset($_POST['display']) ? 1 : 0;
    $show_on_home = isset($_POST['show_on_home']) ? 1 : 0;
    $coming_news  = isset($_POST['coming_news']) ? 1 : 0;
    
    $stmtUpdate = $pdo->prepare("UPDATE news SET title = :title, content = :content, news_date = :news_date, display = :display, show_on_home = :show_on_home, coming_news = :coming_news WHERE id = :id");
    $stmtUpdate->bindParam(':title', $title);
    $stmtUpdate->bindParam(':content', $content);
    $stmtUpdate->bindParam(':news_date', $news_date);
    $stmtUpdate->bindParam(':display', $display);
    $stmtUpdate->bindParam(':show_on_home', $show_on_home);
    $stmtUpdate->bindParam(':coming_news', $coming_news);
    $stmtUpdate->bindParam(':id', $newsId);

    if ($stmtUpdate->execute()) {
        // Process deletion of images
        if (isset($_POST['delete_images']) && is_array($_POST['delete_images'])) {
            foreach ($_POST['delete_images'] as $deleteId) {
                // Get image record to find file path
                $stmtDel = $pdo->prepare("SELECT image_path FROM news_images WHERE id = :id");
                $stmtDel->bindParam(':id', $deleteId);
                $stmtDel->execute();
                $imgRec = $stmtDel->fetch(PDO::FETCH_ASSOC);
                if ($imgRec) {
                    // Build full file path; adjust the relative prefix as needed.
                    $filePath = __DIR__ . '/../../' . $imgRec['image_path'];
                    if(file_exists($filePath)) {
                        unlink($filePath);
                    }
                    // Delete the image record from the database.
                    $stmtDelRec = $pdo->prepare("DELETE FROM news_images WHERE id = :id");
                    $stmtDelRec->bindParam(':id', $deleteId);
                    $stmtDelRec->execute();
                }
            }
        }
        // Include helper for image conversion (if adding new images)
        include_once '../conversion/image_converter';
        if (!empty($_FILES['images']['name'][0])) {
            foreach ($_FILES['images']['tmp_name'] as $index => $tmpName) {
                $convertedImagePath = convertAndSaveImage($tmpName, $news_date, $title, $index + 1);
                if ($convertedImagePath !== false) {
                    $stmtImage = $pdo->prepare("INSERT INTO news_images (news_id, image_path, sequence) VALUES (:news_id, :image_path, :sequence)");
                    $stmtImage->bindParam(':news_id', $newsId);
                    $stmtImage->bindParam(':image_path', $convertedImagePath);
                    $seq = $index + 1;
                    $stmtImage->bindParam(':sequence', $seq);
                    $stmtImage->execute();
                }
            }
        }
        
        if ($oldTitle !== $title || $oldDate !== $news_date) {
            // Re-fetch the existing images (ordered by sequence)
            $stmtReImg = $pdo->prepare("SELECT * FROM news_images WHERE news_id = :news_id ORDER BY sequence ASC");
            $stmtReImg->bindParam(':news_id', $newsId);
            $stmtReImg->execute();
            $existingImages = $stmtReImg->fetchAll(PDO::FETCH_ASSOC);

            foreach ($existingImages as $img) {
                // Build the full path for the current file and destination folder.
                $oldFullPath = __DIR__ . '/../../' . $img['image_path'];
                $destinationFolder = __DIR__ . '/../../img/';

                // Sanitize new title.
                $sanitizedTitle = preg_replace("/[^a-zA-Z0-9]/", "", $title);

                // Create a new file name: news_date_newtitle_sequence.webp.
                // Note: Ensure the sequence in the DB matches your naming convention.
                $newFileName = $news_date . "_" . strtolower($sanitizedTitle) . "_" . $img['sequence'] . ".webp";
                $newFullPath = $destinationFolder . $newFileName;

                // Rename the file if it exists.
                if (file_exists($oldFullPath)) {
                    rename($oldFullPath, $newFullPath);
                }

                // Update the database record with the new relative path.
                $relativePath = "img/" . $newFileName;
                $stmtUpdateImg = $pdo->prepare("UPDATE news_images SET image_path = :image_path WHERE id = :id");
                $stmtUpdateImg->bindParam(':image_path', $relativePath);
                $stmtUpdateImg->bindParam(':id', $img['id']);
                $stmtUpdateImg->execute();
            }
        }
        
        header("Location: ../../admin/dashboard?success=News+updated+successfully");
        exit;
    } else {
        $error = "Failed to update news. Please try again.";
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Edit News</title>
  <link rel="stylesheet" href="../../css/news_managing.css">
  <link rel="stylesheet" href="../../../../css/font-awesome.min.css">
</head>
<body>
  <div class="add-news-container">
    <h1>Edit News Article</h1>
    <?php if (!empty($error)): ?>
      <p class="alert"><?php echo htmlspecialchars($error); ?></p>
    <?php endif; ?>
    <form method="POST" action="news_edit?id=<?php echo urlencode($newsId); ?>" enctype="multipart/form-data">
      <label for="title">Title:</label>
      <input type="text" name="title" id="title" value="<?php echo htmlspecialchars($news['title']); ?>" required /><br><br>
      
      <label for="news_date">Publication Date:</label>
      <input type="date" name="news_date" id="news_date" value="<?php echo htmlspecialchars($news['news_date']); ?>" required /><br><br>
      
      <label for="content">Content:</label><br>
      <textarea name="content" id="content" placeholder="Write your news content here..."><?php echo htmlspecialchars($news['content']); ?></textarea><br><br>

      <!-- Button to add image placeholder and corresponding file inputs -->
      <button class="add-button" type="button" id="addImageBtn">Add Image Placeholder</button>
      <div class="imageInput">
        <div id="imageInputsContainer"></div>
      </div>

      <!-- Existing Images -->
      <div class="existing-images">
        <h3>Current Images</h3>
        <?php if($newsImages): ?>
          <?php foreach($newsImages as $img): ?>
            <div class="existing-image">
              <!-- Moved checkbox above the image -->
              <input type="checkbox" name="delete_images[]" value="<?php echo $img['id']; ?>">
              <img src="../../<?php echo htmlspecialchars($img['image_path']); ?>" alt="News Image" style="max-width:150px;"><br>
            </div>
          <?php endforeach; ?>
          <label style="color: red;">Warning: Selected Images Will Be Deleted After Updating.</label>
        <?php else: ?>
          <p>No images available.</p>
        <?php endif; ?>
      </div>
      <hr>
      <div class="ks-cboxtags">
        <ul class="ks-cboxtags">
          <li>
            <input type="checkbox" name="display" id="display" value="1" <?php echo ($news['display']) ? 'checked' : ''; ?>>
            <label for="display">Display News</label>
          </li>
          <li>
            <input type="checkbox" name="show_on_home" id="show_on_home" value="1" <?php echo ($news['show_on_home']) ? 'checked' : ''; ?>>
            <label for="show_on_home">Show on Home Page</label>
          </li>
          <li>
            <input type="checkbox" name="coming_news" id="coming_news" value="1" <?php echo ($news['coming_news']) ? 'checked' : ''; ?>>
            <label for="coming_news">Mark as Coming News</label>
          </li>
        </ul>
      </div>
      <hr>
      <button type="submit">Update News</button>
    </form>
    <p><a href="../../admin/dashboard">Back to Dashboard</a></p>
  </div>
  <script src="../../js/news_managing.js"></script>
  <script>
    (function(){
      const contentTextarea = document.getElementById('content');
      const imageInputsContainer = document.getElementById('imageInputsContainer');
      const placeholderRegex = /{img:([^}]+)}/g;

      function generateUID() {
          return Date.now() + Math.random().toString(36).substr(2, 5);
      }

      function createInputBlock(uid) {
          const wrapper = document.createElement('div');
          wrapper.className = 'image-input-wrapper';
          wrapper.dataset.uid = uid;

          const label = document.createElement('label');
          label.innerText = '{img?}';

          const fileInput = document.createElement('input');
          fileInput.type = 'file';
          fileInput.name = 'images[]';

          const removeBtn = document.createElement('button');
          removeBtn.type = 'button';
          removeBtn.innerText = 'Remove';
          removeBtn.addEventListener('click', function(){
              wrapper.remove();
              const regex = new RegExp(`{img:${uid}}\\n?`, 'g');
              contentTextarea.value = contentTextarea.value.replace(regex, '');
              syncImageInputs();
          });

          wrapper.appendChild(label);
          wrapper.appendChild(document.createElement('br'));
          wrapper.appendChild(fileInput);
          wrapper.appendChild(document.createElement('br'));
          wrapper.appendChild(removeBtn);
          wrapper.appendChild(document.createElement('br'));
          return wrapper;
      }

      function syncImageInputs() {
          let uids = [];
          let match;
          while ((match = placeholderRegex.exec(contentTextarea.value)) !== null) {
              uids.push(match[1]);
          }
          uids.forEach(uid => {
              let block = imageInputsContainer.querySelector(`[data-uid="${uid}"]`);
              if (!block) {
                  block = createInputBlock(uid);
                  imageInputsContainer.appendChild(block);
              }
          });
          Array.from(imageInputsContainer.children).forEach(block => {
              if (!uids.includes(block.dataset.uid)) {
                  imageInputsContainer.removeChild(block);
              }
          });
          uids.forEach(uid => {
              const block = imageInputsContainer.querySelector(`[data-uid="${uid}"]`);
              if (block) {
                  imageInputsContainer.appendChild(block);
              }
          });
          let counter = 1;
          Array.from(imageInputsContainer.children).forEach(block => {
              const label = block.querySelector('label');
              if (label) {
                  label.innerText = `{img${counter}}`;
              }
              counter++;
          });
      }

      const addImageBtn = document.getElementById('addImageBtn');
      addImageBtn.addEventListener('click', function(){
          const uid = generateUID();
          const newPlaceholder = `{img:${uid}}`;
          const start = contentTextarea.selectionStart;
          const end = contentTextarea.selectionEnd;
          const textBefore = contentTextarea.value.substring(0, start);
          const textAfter = contentTextarea.value.substring(end);
          if (textBefore === "") {
              contentTextarea.value = textBefore + newPlaceholder + "\n" + textAfter;
          } else {
              contentTextarea.value = textBefore + "\n" + newPlaceholder + "\n" + textAfter;
          }
          const newPos = start + newPlaceholder.length + 1;
          contentTextarea.selectionStart = contentTextarea.selectionEnd = newPos;
          syncImageInputs();
      });

      contentTextarea.addEventListener('input', function(){
          syncImageInputs();
      });

      syncImageInputs();
    })();
    
    // Add event listener to toggle checkbox when image is clicked
    document.addEventListener('DOMContentLoaded', function() {
      document.querySelectorAll('.existing-image img').forEach(img => {
        img.addEventListener('click', function() {
          const checkbox = this.parentElement.querySelector('input[type="checkbox"]');
          if (checkbox) {
            checkbox.checked = !checkbox.checked;
            checkbox.dispatchEvent(new Event('change'));
          }
        });
      });
    });
  </script>
</body>
</html>