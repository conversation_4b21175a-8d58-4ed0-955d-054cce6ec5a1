/*----------------------------------------------------------------
-----------------------------login---------------------------
------------------------------------------------------------------*/

body {
    font-family: 'Poppins', sans-serif;
    background: #f0f2f5;
    margin: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
}

form {
    background: #ffffff;
    padding: 40px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    width: 300px;
}

form label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #333;
}

form input {
    width: 100%;
    padding: 12px;
    margin-bottom: 20px;
    border: 1px solid #ccc;
    border-radius: 4px;
    transition: border-color 0.3s ease;
}

form input:focus {
    border-color: #1A76D1;
    outline: none;
}

form button {
    width: 100%;
    padding: 12px;
    background: #1A76D1;
    color: #fff;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    transition: background 0.3s ease;
}

form button:hover {
    background: #2C2D3F;
}

/* Alert styling for error messages */
.alert {
    padding: 10px;
    background-color: #e74c3c;
    color: #fff;
    margin-bottom: 20px;
    border-radius: 4px;
    text-align: center;
}

/*----------------------------------------------------------------
--------------------------------END-------------------------------
------------------------------------------------------------------*/





/*----------------------------------------------------------------
---------------------------dashboard-------------------------
------------------------------------------------------------------*/

.dashboard-container {
    padding: 20px;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    width: 80%;
    margin: 40px 0;
    text-align: center;
}
.dashboard-container h1 {
    margin-bottom: 10px;
    font-size: 24px;
    color: #1A76D1;
}
.dashboard-container a {
    color: #fff;
    background: #1A76D1;
    padding: 10px 20px;
    text-decoration: none;
    border-radius: 4px;
    transition: background 0.3s ease;
}
.dashboard-container a:hover {
    background: #2C2D3F;
}

.dashboard-container .add-news-button {
    width: 300px; 
    display: inline-block; 
    text-align: center;
}
.dashboard-container .logout-button {
    background-color: #e74c3c;
    font-weight: bold;
}
.dashboard-container .logout-button:hover {
    background-color: #ff0000;
    text-decoration: underline;
}

/*---------------------table--------------------*/

.dashboard-container table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 40px;
}
.dashboard-container table th, .dashboard-container table td {
    padding: 8px;
    border: 1px solid #fff;
    border-bottom: 1px solid #ccc;
    text-align: left;
}

.dashboard-container table th {
    background: #f0f0f0;
    font-weight: bold;
}

.dashboard-container table tr:hover {
    background: #f0f0f0;
}
/*---------------------maxws--------------------*/
.dashboard-container table .tdmid {
    text-align: center;
}
.dashboard-container table .tdid {
    width: 50px;
}
.dashboard-container table .tddate {
    width: 100px;
}
.dashboard-container table .tdynd {
    width: 50px;
}
.dashboard-container table .tdynh {
    width: 120px;
}
.dashboard-container table .tdync {
    width: 110px;
}

/*--------------------buttons--------------------*/
.dashboard-container table .tdbutton {
    text-align: center;
    width: 200px;
    padding: 0px;
}
.dashboard-container table .tdbutton a {
    color: #fff;
    background: #1A76D1;
    height: 10px;
    line-height: 10px;
    display: inline-block;
    padding: 10px 20px;
    text-decoration: none;
    border-radius: 4px;
    transition: background 0.3s ease;
}
.dashboard-container table .tdbutton a:hover {
    background: #2C2D3F;
}
.dashboard-container table .tdbutton .adelete {
    font-weight: bold;
    background-color: #f88;
}
.dashboard-container table .tdbutton .adelete:hover {
    background-color: #ff0000;
    color: white;
}

/*---------------------Filter-Sort--------------------*/

.dashboard-container .filter-sort-dashboard {
    width: auto;
    padding: 10px 40px;
    margin-top: 30px;
}

.dashboard-container .filter-sort-dashboard .sorting {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    margin-bottom: 10px;
    padding: 5px;
    background-color: #f0f0f0;
    border-radius: 5px;
}

.dashboard-container .filter-sort-dashboard .date-filter {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    margin-bottom: 10px;
    padding: 5px;
    background-color: #f0f0f0;
    border-radius: 5px;
    padding-right: 15px;
}
.dashboard-container .filter-sort-dashboard input[type="date"] {
    width: 125px;
    margin-bottom: 0px;
}

.dashboard-container .filter-sort-dashboard .filter-options {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    margin-bottom: 10px;
    padding: 5px;
    background-color: #f0f0f0;
    border-radius: 5px;
}

.dashboard-container .filter-sort-dashboard select {
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
    margin-right: 10px;
    width: 150px;
    transition: border-color 0.3s ease;
}

.dashboard-container .filter-sort-dashboard button {
    margin-bottom: 0px;
}

/*----------------------------------------------------------------
--------------------------------END-------------------------------
------------------------------------------------------------------*/




/*----------------------------------------------------------------
----------------------------add_news-------------------------
------------------------------------------------------------------*/

.add-news-container {
    padding: 20px;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    width: 800px;
    text-align: center;
}
.add-news-container h1 {
    margin-bottom: 20px;
    font-size: 24px;
    color: #1A76D1;
}
.add-news-container a {
    color: #fff;
    background: #1A76D1;
    padding: 10px 20px;
    text-decoration: none;
    border-radius: 4px;
    transition: background 0.3s ease;
}
.add-news-container a:hover {
    background: #2C2D3F;
}

.add-news-container form {
    background: #ffffff;
    padding: 40px 10%;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    width: 80%;
    margin-bottom: 40px;
}

.add-news-container form input {
    width: 90%;
    padding: 12px;
    margin-bottom: 20px;
    border: 1px solid #ccc;
    border-radius: 4px;
    transition: border-color 0.3s ease;
}
.add-news-container form input[type="date"] {
    width: 50%;
}

.add-news-container form input:focus {
    border-color: #1A76D1;
    outline: none;
}

.add-news-container form textarea {
    width: 90%;
    max-width: 95%;
    min-height: 50px;
    max-height: 200px;
    padding: 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    transition: border-color 0.3s ease;
}

.add-news-container form textarea:focus {
    border-color: #1A76D1;
    outline: none;
}

.add-news-container .image-input-wrapper{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.add-news-container .imageInput label{
    margin-bottom: 0px;
    width: 20%;
    margin: 0px;
}
.add-news-container .imageInput input{
    width: 60%;
    margin: 0px;
}
.add-news-container .imageInput button{
    font-weight: bold;
    width: 15%;
    height: 45px;
    margin: 0px;
    background-color: #f88;
}
.add-news-container .imageInput button:hover{
    background-color: #ff0000;
    color: white;
}

.add-news-container .add-button {
    width: 50%;
    padding: 12px;
    background: #1A76D1;
    color: #fff;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: background 0.3s ease;
}
.add-news-container .add-button:hover {
    background: #2C2D3F;
}

.add-news-container form hr {
    border: 0; 
    height: 1px; 
    background-image: -webkit-linear-gradient(left, #f0f0f0, #1A76D1, #f0f0f0);
    background-image: -moz-linear-gradient(left, #f0f0f0, #1A76D1, #f0f0f0);
    background-image: -ms-linear-gradient(left, #f0f0f0, #1A76D1, #f0f0f0);
    background-image: -o-linear-gradient(left, #f0f0f0, #1A76D1, #f0f0f0); 
    margin-bottom: 20px;
}


/* New styled checkboxes - updated alignment */
.ks-cboxtags {
    width: auto;
    margin: 0 auto;
    padding: 10px;
    text-align: center;
}

.ks-cboxtags li {
    display: inline-block;
}

.ks-cboxtags li label {
    display: inline-block;
    background-color: rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(139, 139, 139, 0.3);
    color: #adadad;
    border-radius: 25px;
    white-space: nowrap;
    margin: 3px 0;
    transition: all 0.2s;
    padding: 8px 12px;
    cursor: pointer;
    position: relative;
}

.ks-cboxtags li label::before {
    display: inline-block;
    font-family: "FontAwesome";
    font-weight: 900;
    font-size: 12px;
    padding: 2px 6px 2px 2px;
    content: "\f067";
    transition: transform 0.3s ease-in-out;
}

.ks-cboxtags li input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
    overflow: hidden;
    clip: rect(0,0,0,0);
}

.ks-cboxtags li input[type="checkbox"]:checked + label::before {
    content: "\f00c";
    transform: rotate(-360deg);
}

.ks-cboxtags li input[type="checkbox"]:checked + label {
    border: 2px solid #55aaff;
    background-color: #1A76D1;
    color: #fff;
}

.ks-cboxtags li input[type="checkbox"]:focus + label {
    border: 2px solid #888;
}


/*-----------------------------------------------------------------
----------------------------END------------------------------------
------------------------------------------------------------------*/




/*----------------------------------------------------------------
----------------------------edit_news------------------------
------------------------------------------------------------------*/

.existing-image input[type="checkbox"] {
    -webkit-appearance: none;
    appearance: none;
    background-color: #fff;
    border: 2px solid #1A76D1;
    border-radius: 4px;
    width: 20px;
    height: 20px;
    cursor: pointer;
    position: absolute;
    vertical-align: middle;
    margin-top: 5px;
    transition: all 0.3s ease;
}

.existing-image input[type="checkbox"]:checked {
    background-color: #1A76D1;
}

.existing-image img {
    cursor: pointer;
}
.existing-image input[type="checkbox"]:checked ~ img {
    border: 5px solid #f88;
    border-radius: 3px;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

.existing-image input[type="checkbox"]:checked::after {
    content: "\f00c";
    font-family: "FontAwesome";
    font-weight: 900;
    color: #fff;
    position: absolute;
    top: 4px;
    left: 4px;
    font-size: 16px;
}

/*-----------------------------------------------------------------
----------------------------END------------------------------------
------------------------------------------------------------------*/




