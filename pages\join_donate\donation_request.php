<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <title>طلب تبرع - منظمة العمل المقدس الدولية</title>
    <link rel="stylesheet" href="/css/bootstrap.min.css">
    <link rel="stylesheet" href="/pages/join_donate/forms.css">
    <style>
        @media print {
            body * { visibility: hidden !important; }

            #donation-request-print {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                margin: 0;
                padding: 20px;
                background: white;
            }
            @page {
                size: auto;
                margin: 20mm;
            }
            
            #print-container,
            #print-container * {
                visibility: visible !important;
                display: block !important;
            }

        }
        
    </style>
</head>
<body>
    <section class="volunteer-form section">
        <div class="container">
            <div class="form-container">
                <div class="section-title">
                    <h2>طلب تبرع</h2>
                    <p>يرجى تعبئة النموذج أدناه لطلب التبرع، وسنقوم بالتواصل معكم قريباً.</p>
                </div>
                <form id="donationRequestForm" method="POST" action="#">
                    <div class="form-group">
                        <label for="donationType">نوع التبرع*</label>
                        <select id="donationType" name="donationType" class="form-control" required>
                            <option value="">اختر نوع التبرع</option>
                            <option value="financial">تبرع مالي</option>
                            <option value="monthly">تبرع شهري</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="name">الاسم الكامل*</label>
                        <input type="text" id="name" name="name" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="email">البريد الإلكتروني*</label>
                        <input type="email" id="email" name="email" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="phone">رقم الهاتف*</label>
                        <input type="tel" id="phone" name="phone" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="message">رسالتك أو تفاصيل التبرع*</label>
                        <textarea id="message" name="message" class="form-control" rows="4" required></textarea>
                    </div>
                    <div class="form-group submit-group">
                        <button type="submit" class="btn btn-primary">إرسال الطلب</button>
                    </div>
                </form>
                <hr style="margin-top: 60px;">
                <button type="button" class="btn btn-secondary" style="background: #888; border: none; padding: 10px 20px; border-radius: 5px; font-size: 16px; cursor: pointer; transition: background-color 0.3s ease, transform 0.2s; display: block; margin: 20px auto;" 
                        onmouseover="this.style.background='#555'; this.style.transform='translateY(0px)'; this.style.boxShadow='0px 4px 8px rgba(0, 0, 0, 0.2)';" 
                        onmouseout="this.style.background='#888';" 
                        onclick="window.location.href='/pages/join_donate/join_donate'">رجوع</button>
            </div>
        </div>
    </section>

    <!-- Modal Popup -->
    <div id="donationRequestModal" style="display:none;position:fixed;top:0;left:0;width:100vw;height:100vh;background:rgba(0,0,0,0.45);z-index:9999; padding:10px;">
        <div id="donationRequestModalContent"
             style="background:#f8fff8;max-width:650px;margin:120px auto;
                    padding:30px 20px 20px 20px;border-radius:14px;text-align:center;
                    box-shadow:0 2px 12px #999;position:relative;direction:rtl;
                    border:1.5px solid #c3e6cb;
                    max-height:80vh; overflow-y:auto;
                    scrollbar-color: #555 rgba(230, 242, 248, 0);">
            <!-- Content will be filled by JS -->
        </div>
    </div>

    <script>
        window.addEventListener('DOMContentLoaded', function() {
            var params = new URLSearchParams(window.location.search);
            var type = params.get('type');
            var select = document.getElementById('donationType');
            if (select && (type === 'financial' || type === 'monthly')) {
                select.value = type;
            }
            document.getElementById('donationRequestForm').addEventListener('submit', function(e) {
                e.preventDefault();
                var form = this;
                var formData = new FormData(form);
                var xhr = new XMLHttpRequest();
                xhr.open('POST', '/pages/join_donate/form_action/submit-donation-request', true);
                xhr.onload = function() {
                    document.getElementById('donationRequestModalContent').innerHTML = xhr.responseText;
                    document.getElementById('donationRequestModal').style.display = 'block';
                };
                xhr.send(formData);
            });
        });
    </script>
    <script>
    function printDonationRequest() {
        var printContent = document.getElementById('donationRequestModalContent');
        var originalContent = document.body.innerHTML;

        document.body.innerHTML = '<div id="print-container">' + printContent.innerHTML + '</div>';
        window.print();
        document.body.innerHTML = originalContent;
    }
    </script>
</body>
</html>