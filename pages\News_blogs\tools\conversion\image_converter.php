<?php
function convertAndSaveImage($tmpPath, $news_date, $title, $seq) {
    // Check if the temporary file path is valid.
    if (empty($tmpPath) || !file_exists($tmpPath)) {
        return false;
    }
    
    // Sanitize title for filename: remove spaces and special characters.
    $sanitizedTitle = preg_replace("/[^a-zA-Z0-9]/", "", $title);
    
    // Define destination folder. Here, we use the parent img folder.
    $destinationFolder = __DIR__ . "/../../img/";
    if (!file_exists($destinationFolder)) {
        mkdir($destinationFolder, 0777, true);
    }
    
    // Create a new filename (e.g., 2025-03-21_todaynews_1.webp).
    $newFileName = $news_date . "_" . strtolower($sanitizedTitle) . "_" . $seq . ".webp";
    $destinationPath = $destinationFolder . $newFileName;
    
    // Get image details to determine the type.
    $imageInfo = getimagesize($tmpPath);
    if ($imageInfo === false) {
        return false;
    }
    
    $mime = $imageInfo['mime'];
    // Load the image depending on its MIME type.
    switch ($mime) {
        case 'image/jpeg':
        case 'image/jpg':
            $image = imagecreatefromjpeg($tmpPath);
            break;
        case 'image/png':
            $image = imagecreatefrompng($tmpPath);
            break;
        case 'image/gif':
            $image = imagecreatefromgif($tmpPath);
            break;
        case 'image/webp':
            $image = imagecreatefromwebp($tmpPath);
            break;
        default:
            // Unsupported image type.
            return false;
    }
    
    if ($image === false) {
        return false;
    }
    
    // Convert and save the image to WebP format.
    $quality = 80;
    if (imagewebp($image, $destinationPath, $quality)) {
        imagedestroy($image);
        // Return relative path.
        return "img/" . $newFileName;
    } else {
        imagedestroy($image);
        return false;
    }
}
?>