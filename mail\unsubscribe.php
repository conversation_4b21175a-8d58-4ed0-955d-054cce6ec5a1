<?php
if (!isset($_GET['email'])) {
    echo "لم يتم إرسال البريد الإلكتروني.";
    exit;
}

$email = trim($_GET['email']);
if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    echo "البريد الإلكتروني غير صحيح.";
    exit;
}

$dbPath = __DIR__ . '/newsletter.db';
try {
    $pdo = new PDO("sqlite:" . $dbPath);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Unsubscribe the email
    $stmt = $pdo->prepare("UPDATE newsletter_emails SET subscribed = 0 WHERE email = :email");
    $stmt->bindParam(':email', $email);
    $stmt->execute();

    if ($stmt->rowCount() > 0) {
        echo "تم إلغاء اشتراكك في النشرة البريدية بنجاح.";
    } else {
        echo "البريد الإلكتروني غير موجود أو تم إلغاء الاشتراك مسبقاً.";
    }
} catch (PDOException $e) {
    echo "حدث خطأ أثناء تحديث الاشتراك: " . $e->getMessage();
    exit;
}
?>