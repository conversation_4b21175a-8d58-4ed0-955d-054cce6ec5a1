<?php
// Validate required fields
$required = ['donationPurpose', 'firstName', 'lastName', 'email', 'phone', 'cardNumber', 'expiryDate', 'cvv', 'terms'];
foreach ($required as $field) {
    if (empty($_POST[$field])) {
        die('يرجى تعبئة جميع الحقول المطلوبة.');
    }
}

// Validate donation amount
$donationAmount = 0;
if (!empty($_POST['donationAmount'])) {
    $donationAmount = intval($_POST['donationAmount']);
}
if (!empty($_POST['customAmount'])) {
    $customAmount = intval($_POST['customAmount']);
    if ($customAmount > 0) $donationAmount = $customAmount;
}
if ($donationAmount <= 0) {
    die('يرجى اختيار أو إدخال قيمة تبرع صحيحة.');
}

// Sanitize input
$donationPurpose = htmlspecialchars($_POST['donationPurpose']);
$firstName = htmlspecialchars($_POST['firstName']);
$lastName = htmlspecialchars($_POST['lastName']);
$email = filter_var($_POST['email'], FILTER_VALIDATE_EMAIL);
$phone = htmlspecialchars($_POST['phone']);
// For demo: store only last 4 digits of card
$cardNumber = htmlspecialchars(substr(preg_replace('/\D/', '', $_POST['cardNumber']), -4));
$expiryDate = htmlspecialchars($_POST['expiryDate']);
$cvv = htmlspecialchars($_POST['cvv']);

// Store in SQLite database
try {
    $dbPath = __DIR__ . '/../join_donate_databases/donate_forms.db';
    $db = new PDO('sqlite:' . $dbPath);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Create table if not exists
    $db->exec("CREATE TABLE IF NOT EXISTS donate_financial (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        donationAmount INTEGER,
        donationPurpose TEXT,
        firstName TEXT,
        lastName TEXT,
        email TEXT,
        phone TEXT,
        cardLast4 TEXT,
        expiryDate TEXT,
        submitted_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )");

    // Insert data
    $stmt = $db->prepare("INSERT INTO donate_financial 
        (donationAmount, donationPurpose, firstName, lastName, email, phone, cardLast4, expiryDate)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
    $stmt->execute([
        $donationAmount, $donationPurpose, $firstName, $lastName, $email, $phone, $cardNumber, $expiryDate
    ]);
} catch (PDOException $e) {
    die('خطأ في حفظ البيانات: ' . $e->getMessage());
}

// Confirmation message
?>
<style>
.donate-success-container {
    max-width: 650px;
    margin: 40px auto;
    padding: 30px 20px;
    background: #f8fff8;
    border: 1.5px solid #c3e6cb;
    border-radius: 12px;
    text-align: center;
    font-family: 'Cairo', sans-serif;
    box-shadow: 0 2px 12px #e6f2e6;
    direction: rtl;
}
.donate-success-container h2 { color: #28a745; margin-bottom: 10px; }
.donate-success-container p { font-size: 1.1em; margin-bottom: 18px; }
.donate-success-btns button {
    margin: 5px 10px; padding: 10px 25px; border: none; border-radius: 5px;
    cursor: pointer; font-size: 1em; transition: background 0.2s;
}
.donate-success-btns .back-btn { background: #007bff; color: #fff; }
.donate-success-btns .view-btn { background: #17a2b8; color: #fff; }
.donate-success-btns .pdf-btn { background: #ffc107; color: #212529; }
.donate-form-details {
    display: none; text-align: right; direction: rtl; background: #fff;
    border: 1px solid #eee; padding: 24px 18px; border-radius: 8px;
    margin-top: 24px; font-size: 1.08em; box-shadow: 0 1px 8px #f3f3f3;
}
.donate-form-details h3 {
    color: #17a2b8; margin-bottom: 18px; font-size: 1.2em;
    border-bottom: 1px solid #e0e0e0; padding-bottom: 8px;
}
.donate-form-details p { margin: 8px 0; line-height: 1.7; word-break: break-word; }
.donate-form-details strong { color: #007bff; min-width: 110px; display: inline-block; }
@media print {
    body * { visibility: hidden !important; }
    #donate-details, #donate-details * { visibility: visible !important; display: block !important; }
    #donate-details-print, #donate-details-print * { visibility: visible !important; }
    #donate-details-print {
        position: absolute; left: 0; top: 0; width: 100vw; background: #fff;
        box-shadow: none; border: none; padding: 0;
    }
    .donate-success-btns, .donate-success-container h2, .donate-success-container p { display: none !important; }
}
</style>
<div class="donate-success-container">
    <h2>تم استلام تبرعك بنجاح</h2>
    <p>شكراً لمساهمتك في دعم مشاريعنا الإنسانية!</p>
    <div class="donate-success-btns">
        <button class="back-btn" onclick="window.location.href='../join_donate'">رجوع</button>
        <button class="view-btn" onclick="document.getElementById('donate-details').style.display='block';">عرض التفاصيل</button>
        <button class="pdf-btn" onclick="printDonateDetails()">حفظ كملف PDF</button>
    </div>
    <div id="donate-details" class="donate-form-details">
        <div id="donate-details-print">
            <h3>تفاصيل التبرع:</h3>
            <p><strong>قيمة التبرع:</strong> <?php echo htmlspecialchars($donationAmount); ?> $</p>
            <p><strong>الغرض من التبرع:</strong> <?php echo htmlspecialchars($donationPurpose); ?></p>
            <p><strong>الاسم:</strong> <?php echo htmlspecialchars($firstName . ' ' . $lastName); ?></p>
            <p><strong>البريد الإلكتروني:</strong> <?php echo htmlspecialchars($email); ?></p>
            <p><strong>رقم الهاتف:</strong> <?php echo htmlspecialchars($phone); ?></p>
            <p><strong>آخر 4 أرقام من البطاقة:</strong> <?php echo htmlspecialchars($cardNumber); ?></p>
            <p><strong>تاريخ الانتهاء:</strong> <?php echo htmlspecialchars($expiryDate); ?></p>
        </div>
    </div>
</div>
<script>
function printDonateDetails() {
    var details = document.getElementById('donate-details');
    var wasHidden = details.style.display === 'none' || window.getComputedStyle(details).display === 'none';
    details.style.display = 'block';

    function afterPrint() {
        if (wasHidden) details.style.display = 'none';
        window.removeEventListener('afterprint', afterPrint);
    }
    window.addEventListener('afterprint', afterPrint);

    window.print();
}
</script>