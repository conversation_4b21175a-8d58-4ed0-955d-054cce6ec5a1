<?php
if (!isset($_GET['email'])) {
    echo "لم يتم إرسال البريد الإلكتروني.";
    exit;
}

$email = trim($_GET['email']);
if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    echo "البريد الإلكتروني غير صحيح.";
    exit;
}

// Store or update email in SQLite database
$dbPath = __DIR__ . '/newsletter.db';
try {
    $pdo = new PDO("sqlite:" . $dbPath);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    // Create table if not exists
    $pdo->exec("CREATE TABLE IF NOT EXISTS newsletter_emails (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        email TEXT UNIQUE NOT NULL,
        subscribed INTEGER DEFAULT 1,
        subscribed_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )");
    // Try to insert new email
    $stmt = $pdo->prepare("INSERT OR IGNORE INTO newsletter_emails (email, subscribed) VALUES (:email, 1)");
    $stmt->bindParam(':email', $email);
    $stmt->execute();

    // If email already exists, update subscribed status to 1
    $stmtUpdate = $pdo->prepare("UPDATE newsletter_emails SET subscribed = 1, subscribed_at = CURRENT_TIMESTAMP WHERE email = :email");
    $stmtUpdate->bindParam(':email', $email);
    $stmtUpdate->execute();
} catch (PDOException $e) {
    echo "حدث خطأ أثناء حفظ البريد الإلكتروني: " . $e->getMessage();
    exit;
}

// Send notification to admin (replace with your admin email)
$adminEmail = "<EMAIL>";
$subject = "اشتراك جديد في النشرة البريدية";
$message = "تم الاشتراك بالنشرة البريدية بواسطة البريد الإلكتروني: $email";
$headers = "From: <EMAIL>\r\n";

// Send email to admin
// mail($adminEmail, $subject, $message, $headers);

// Optionally, send confirmation to user (uncomment if needed)
// mail($email, "شكراً لاشتراكك في النشرة البريدية", "سعداء بانضمامك إلى نشرتنا الإخبارية!", $headers);

echo "شكراً لاشتراكك في النشرة البريدية!";
?>