/* Enhanced Volunteer Form Section */
.volunteer-form.section {
    position: relative;
    padding: 100px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    overflow: hidden;
}

/* Decorative Background Elements */
.volunteer-form.section::before,
.volunteer-form.section::after {
    content: '';
    position: absolute;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: linear-gradient(45deg, rgba(26, 118, 209, 0.1), rgba(26, 118, 209, 0.05));
    z-index: 1;
}

.volunteer-form.section::before {
    top: -150px;
    right: -150px;
}

.volunteer-form.section::after {
    bottom: -150px;
    left: -150px;
}

/* Container Enhancements */
.volunteer-form .container {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Form Container Styling */
.volunteer-form .form-container {
    background: #ffffff;
    border-radius: 30px;
    padding: 60px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(26, 118, 209, 0.1);
}

/* Decorative Form Container Elements */
.form-container::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 6px;
    background: linear-gradient(90deg, #1A76D1, #2889e4);
}

/* Enhanced Section Title */
.volunteer-form .section-title {
    text-align: center;
    margin-bottom: 60px;
    position: relative;
}

.volunteer-form .section-title h2 {
    font-size: clamp(32px, 5vw, 48px);
    color: #2C2D3F;
    margin-bottom: 25px;
    padding-bottom: 15px;
    font-weight: 700;
    position: relative;
    display: inline-block;
}

.volunteer-form .section-title h2::after {
    content: '';
    position: absolute;
    bottom: -15px;
    right: 50%;
    transform: translateX(50%);
    width: 200%;
    height: 4px;
    background: #1A76D1;
    border-radius: 2px;
}

.volunteer-form .section-title p {
    color: #666;
    font-size: 18px;
    max-width: 800px;
    margin: 20px auto 0;
    line-height: 1.8;
}

/* Responsive Adjustments */
@media (max-width: 991px) {
    .volunteer-form.section {
        padding: 80px 0;
    }

    .volunteer-form .form-container {
        padding: 40px 30px;
    }
}

@media (max-width: 768px) {
    .volunteer-form.section {
        padding: 60px 0;
    }

    .volunteer-form .form-container {
        padding: 30px 20px;
        border-radius: 20px;
    }

    .volunteer-form .section-title {
        margin-bottom: 40px;
    }

    .volunteer-form .section-title h2 {
        font-size: clamp(28px, 4vw, 36px);
    }
}

/* Print Styles */
@media print {
    .volunteer-form.section {
        background: none;
        padding: 20px 0;
    }

    .volunteer-form .form-container {
        box-shadow: none;
        border: 1px solid #ddd;
    }

    .volunteer-form.section::before,
    .volunteer-form.section::after {
        display: none;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .volunteer-form.section {
        background: #ffffff;
    }

    .volunteer-form .form-container {
        border: 2px solid #1A76D1;
        box-shadow: none;
    }

    .volunteer-form .section-title h2 {
        text-shadow: none;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .volunteer-form.section *,
    .volunteer-form .form-container,
    .volunteer-form .section-title h2::after {
        transition: none !important;
        animation: none !important;
    }
}

/* Form Section Styles */
.form-section {
    background: #ffffff;
    border-radius: 15px;
    padding: 40px;
    margin-bottom: 40px;
    padding: 30px;
    background: #fafafa;
    border-radius: 15px;
    border: 1px solid #eee;
}

.form-section h3 {
    color: #2C2D3F;
    font-size: 24px;
    margin-bottom: 30px;
    padding-right: 20px;
    position: relative;
    text-align: right;
}

.form-section h3::before {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 25px;
    background: #1A76D1;
    border-radius: 2px;
}

/* Form Group Styles */
.form-group {
    margin-bottom: 25px;
    text-align: right;
}

.form-group label {
    display: block;
    margin-bottom: 10px;
    color: #2C2D3F;
    font-weight: 600;
    font-size: 16px;
    text-align: right;
}

.form-control {
    width: 100%;
    height: 55px;
    padding: 12px 20px;
    font-size: 16px;
    border: 2px solid #eee;
    border-radius: 8px;
    transition: all 0.3s ease;
    text-align: right;
    direction: rtl;
}

.volunteer-form select.form-control:not([size]):not([multiple]) {
    height: auto !important;
}

.form-control:focus {
    border-color: #1A76D1;
    box-shadow: 0 0 0 3px rgba(26, 118, 209, 0.1);
}

textarea.form-control {
    min-height: 150px;
    resize: vertical;
}

/* Select Styles */
select.form-control {
    padding-right: 20px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='%23333' viewBox='0 0 16 16'%3E%3Cpath d='M8 11L3 6h10l-5 5z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: left 15px center;
    background-size: 12px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

/* Checkbox Styles */
.custom-control {
    position: relative;
    padding-right: 35px;
    margin-bottom: 15px;
    text-align: right;
}

.custom-control-input {
    position: absolute;
    right: 0;
    z-index: -1;
    width: 20px;
    height: 20px;
}

.custom-control-label {
    position: relative;
    margin-bottom: 0;
    vertical-align: top;
    cursor: pointer;
}

.custom-control-label::before {
    position: absolute;
    right: -35px;
    top: 0;
    width: 20px;
    height: 20px;
    border: 2px solid #1A76D1;
    border-radius: 4px;
    background-color: #fff;
    content: "";
}

.custom-control-label::after {
    position: absolute;
    right: -35px;
    top: 0;
    width: 20px;
    height: 20px;
    content: "";
}

.custom-control-input:checked ~ .custom-control-label::before {
    background-color: #1A76D1;
}

/* Submit Button Styles */
.submit-group {
    text-align: center;
    margin-top: 40px;
}

.btn {
    display: inline-block;
    padding: 15px 40px;
    font-size: 18px;
    font-weight: 600;
    color: #fff;
    background: linear-gradient(45deg, #1A76D1, #1555a0);
    border: none;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 200px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 7px 14px rgba(26, 118, 209, 0.2);
}

/* Responsive Styles */
@media (max-width: 768px) {
    .form-container {
        padding: 30px 20px;
        margin: 0 15px;
    }

    .form-section {
        padding: 20px;
    }

    .form-section h3 {
        font-size: 20px;
    }

    .form-control {
        height: 50px;
        font-size: 15px;
    }

    .btn {
        width: 100%;
        padding: 14px 20px;
    }
}

/* Row and Column Adjustments */
.row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
}

.col-md-6 {
    padding-right: 15px;
    padding-left: 15px;
}

@media (min-width: 768px) {
    .col-md-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }
}

/* Validation Styles */
.form-control.is-invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23dc3545' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-position: left 20px center;
    padding-left: 50px;
}

.invalid-feedback {
    display: block;
    color: #dc3545;
    font-size: 14px;
    margin-top: 8px;
    text-align: right;
}

/* Animation */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.form-section {
    animation: fadeIn 0.5s ease-out forwards;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    .form-section,
    .form-control,
    .btn {
        animation: none;
        transition: none;
    }
}

/* File Input Styles */
input[type="file"].form-control {
    padding: 8px;
    height: auto;
}

input[type="file"].form-control::-webkit-file-upload-button {
    background: #1A76D1;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    color: white;
    margin-left: 15px;
    cursor: pointer;
}

input[type="file"].form-control::-webkit-file-upload-button:hover {
    background: #1555a0;
}

/* Job Application Specific Styles */
.job-application-form .form-section {
    margin-bottom: 30px;
}

.job-application-form .form-group label {
    color: #2C2D3F;
    font-weight: 600;
}

.job-application-form .custom-control-label {
    font-size: 14px;
    line-height: 1.6;
}

/* Partnership Form Specific Styles */
.partnership-form .form-section {
    margin-bottom: 35px;
}

.partnership-form textarea.form-control {
    min-height: 120px;
}

.partnership-form .section-title p {
    max-width: 900px;
}

.partnership-form .form-group label {
    color: #2C2D3F;
    font-weight: 600;
}

/* Enhanced Description Fields */
.partnership-form textarea#description,
.partnership-form textarea#objectives {
    min-height: 150px;
}

/* Organization Profile Section */
.partnership-form .org-info {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

/* Donation Form Specific Styles */
.donation-amounts {
    margin-bottom: 30px;
}

.amount-option {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 40px;
    text-align: center;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.amount-option:hover {
    border-color: #1A76D1;
    background: #f0f7ff;
}

.amount-option input[type="radio"] {
    display: none;
}

.amount-option label {
    font-size: 24px;
    font-weight: 600;
    color: #2C2D3F;
    margin: 0;
    cursor: pointer;
    /* Make label fill the entire box */
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.amount-option.selected {
    border-color: #1A76D1;
    background: #f0f7ff;
}

.amount-option.selected label {
    color: #1A76D1;
}

.amount-option input[type="radio"]:checked + label {
    color: #1A76D1;
}

.amount-option input[type="radio"]:checked + label::before {
    background: #1A76D1;
}

.custom-amount {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.donation-form .form-section {
    margin-bottom: 40px;
}

/* Payment Information Styles */
.donation-form input#cardNumber,
.donation-form input#expiryDate,
.donation-form input#cvv {
    font-family: monospace;
    letter-spacing: 1px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .amount-option {
        padding: 15px;
    }

    .amount-option label {
        font-size: 20px;
    }
}






