<?php
// Validate required fields
$required = [
    'orgName', 'orgType', 'country', 'contactName', 'position', 'email', 'phone',
    'partnershipType', 'description', 'objectives', 'terms'
];
foreach ($required as $field) {
    if (empty($_POST[$field])) {
        die('يرجى تعبئة جميع الحقول المطلوبة.');
    }
}
if (!isset($_FILES['orgProfile']) || $_FILES['orgProfile']['error'] !== UPLOAD_ERR_OK) {
    die('يرجى رفع الملف التعريفي للمنظمة (PDF).');
}

// Sanitize input
$orgName = htmlspecialchars($_POST['orgName']);
$orgType = htmlspecialchars($_POST['orgType']);
$country = htmlspecialchars($_POST['country']);
$website = htmlspecialchars($_POST['website'] ?? '');
$contactName = htmlspecialchars($_POST['contactName']);
$contactPosition = htmlspecialchars($_POST['position']);
$email = filter_var($_POST['email'], FILTER_VALIDATE_EMAIL);
$phone = htmlspecialchars($_POST['phone']);
$partnershipType = htmlspecialchars($_POST['partnershipType']);
$description = htmlspecialchars($_POST['description']);
$objectives = htmlspecialchars($_POST['objectives']);
$experience = htmlspecialchars($_POST['experience'] ?? '');

// Prepare upload directory
$date = date('Ymd_His');
$dirName = $date . '_' . preg_replace('/\s+/', '_', $orgName);
$uploadDir = __DIR__ . '/../join_donate_databases/uploads/partnership/' . $dirName . '/';
if (!is_dir($uploadDir)) {
    mkdir($uploadDir, 0775, true);
}

// Handle orgProfile upload (required)
$orgProfileFileName = uniqid('profile_') . '_' . basename($_FILES['orgProfile']['name']);
$orgProfileFilePath = $uploadDir . $orgProfileFileName;
if (!move_uploaded_file($_FILES['orgProfile']['tmp_name'], $orgProfileFilePath)) {
    die('حدث خطأ أثناء رفع الملف التعريفي للمنظمة.');
}
$orgProfile = 'join_donate_databases/uploads/partnership/' . $dirName . '/' . $orgProfileFileName;

// Handle proposal upload (optional)
$proposal = '';
if (isset($_FILES['proposal']) && $_FILES['proposal']['error'] === UPLOAD_ERR_OK) {
    $proposalFileName = uniqid('proposal_') . '_' . basename($_FILES['proposal']['name']);
    $proposalFilePath = $uploadDir . $proposalFileName;
    if (move_uploaded_file($_FILES['proposal']['tmp_name'], $proposalFilePath)) {
        $proposal = 'join_donate_databases/uploads/partnership/' . $dirName . '/' . $proposalFileName;
    }
}

// Store in SQLite database
try {
    $dbPath = __DIR__ . '/../join_donate_databases/join_forms.db';
    $db = new PDO('sqlite:' . $dbPath);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Create table if not exists
    $db->exec("CREATE TABLE IF NOT EXISTS partnership (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        orgName TEXT,
        orgType TEXT,
        country TEXT,
        website TEXT,
        contactName TEXT,
        contactPosition TEXT,
        email TEXT,
        phone TEXT,
        partnershipType TEXT,
        description TEXT,
        objectives TEXT,
        experience TEXT,
        orgProfile TEXT,
        proposal TEXT,
        submitted_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )");

    // Insert data
    $stmt = $db->prepare("INSERT INTO partnership 
        (orgName, orgType, country, website, contactName, contactPosition, email, phone, partnershipType, description, objectives, experience, orgProfile, proposal)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    $stmt->execute([
        $orgName, $orgType, $country, $website, $contactName, $contactPosition, $email, $phone,
        $partnershipType, $description, $objectives, $experience, $orgProfile, $proposal
    ]);
} catch (PDOException $e) {
    die('خطأ في حفظ البيانات: ' . $e->getMessage());
}

// Confirmation message
function showSuccessMessage(
    $orgName, $orgType, $country, $website, $contactName, $contactPosition, $email, $phone, $partnershipType, $description, $objectives, $experience, $orgProfile, $proposal, $mailSuccess = true
) {
    $bg = $mailSuccess ? '#f8fff8' : '#fff3cd';
    $border = $mailSuccess ? '#c3e6cb' : '#ffeeba';
    ?>
    <style>
    .part-success-container {
        max-width: 700px;
        margin: 40px auto;
        padding: 30px 20px;
        background: <?php echo $bg; ?>;
        border: 1.5px solid <?php echo $border; ?>;
        border-radius: 12px;
        text-align: center;
        font-family: 'Cairo', sans-serif;
        box-shadow: 0 2px 12px #e6f2e6;
        direction: rtl;
    }
    .part-success-container h2 {
        color: <?php echo $mailSuccess ? '#28a745' : '#856404'; ?>;
        margin-bottom: 10px;
    }
    .part-success-container p { font-size: 1.1em; margin-bottom: 18px; }
    .part-success-btns button {
        margin: 5px 10px; padding: 10px 25px; border: none; border-radius: 5px;
        cursor: pointer; font-size: 1em; transition: background 0.2s;
    }
    .part-success-btns .back-btn { background: #007bff; color: #fff; }
    .part-success-btns .view-btn { background: #17a2b8; color: #fff; }
    .part-success-btns .pdf-btn { background: #ffc107; color: #212529; }
    .part-form-details {
        display: none; text-align: right; direction: rtl; background: #fff;
        border: 1px solid #eee; padding: 24px 18px; border-radius: 8px;
        margin-top: 24px; font-size: 1.08em; box-shadow: 0 1px 8px #f3f3f3;
    }
    .part-form-details h3 {
        color: #17a2b8; margin-bottom: 18px; font-size: 1.2em;
        border-bottom: 1px solid #e0e0e0; padding-bottom: 8px;
    }
    .part-form-details p { margin: 8px 0; line-height: 1.7; word-break: break-word; }
    .part-form-details strong { color: #007bff; min-width: 110px; display: inline-block; }
    .part-form-details a { color: #17a2b8; text-decoration: underline; word-break: break-all; }
    @media print {
        body * { visibility: hidden !important; }
        #part-details, #part-details * { visibility: visible !important; display: block !important; }
        #part-details-print, #part-details-print * { visibility: visible !important; }
        #part-details-print {
            position: absolute; left: 0; top: 0; width: 100vw; background: #fff;
            box-shadow: none; border: none; padding: 0;
        }
        .part-success-btns, .part-success-container h2, .part-success-container p { display: none !important; }
    }
    </style>
    <div class="part-success-container">
        <h2>
            <?php echo $mailSuccess ? 'تم إرسال طلب الشراكة بنجاح' : 'تم حفظ طلبكم'; ?>
        </h2>
        <p>
            <?php echo $mailSuccess ? 'شكراً لتواصلكم معنا! سنراجع طلبكم ونتواصل معكم قريباً.' : 'لكن حدث خطأ أثناء إرسال البريد الإلكتروني.'; ?>
        </p>
        <div class="part-success-btns">
            <button class="back-btn" onclick="window.location.href='../join_donate'">رجوع</button>
            <button class="view-btn" onclick="document.getElementById('part-details').style.display='block';">عرض الطلب</button>
            <button class="pdf-btn" onclick="printPartDetails()">حفظ كملف PDF</button>
        </div>
        <div id="part-details" class="part-form-details">
            <div id="part-details-print">
                <h3>معلومات طلب الشراكة:</h3>
                <p><strong>اسم المنظمة:</strong> <?php echo htmlspecialchars($orgName); ?></p>
                <p><strong>نوع المنظمة:</strong> <?php echo htmlspecialchars($orgType); ?></p>
                <p><strong>بلد المقر:</strong> <?php echo htmlspecialchars($country); ?></p>
                <p><strong>الموقع الإلكتروني:</strong> <?php echo htmlspecialchars($website); ?></p>
                <p><strong>اسم المسؤول:</strong> <?php echo htmlspecialchars($contactName); ?></p>
                <p><strong>المنصب:</strong> <?php echo htmlspecialchars($contactPosition); ?></p>
                <p><strong>البريد الإلكتروني:</strong> <?php echo htmlspecialchars($email); ?></p>
                <p><strong>رقم الهاتف:</strong> <?php echo htmlspecialchars($phone); ?></p>
                <p><strong>نوع الشراكة:</strong> <?php echo htmlspecialchars($partnershipType); ?></p>
                <p><strong>وصف الشراكة:</strong> <?php echo nl2br(htmlspecialchars($description)); ?></p>
                <p><strong>الأهداف:</strong> <?php echo nl2br(htmlspecialchars($objectives)); ?></p>
                <p><strong>الخبرات السابقة:</strong> <?php echo nl2br(htmlspecialchars($experience)); ?></p>
                <p><strong>الملف التعريفي:</strong>
                    <a href="/pages/join_donate/<?php echo htmlspecialchars($orgProfile); ?>" target="_blank">عرض/تحميل</a>
                </p>
                <p><strong>مقترح الشراكة:</strong>
                    <?php if ($proposal) { ?>
                        <a href="/pages/join_donate/<?php echo htmlspecialchars($proposal); ?>" target="_blank">عرض/تحميل</a>
                    <?php } else { echo 'غير متوفر'; } ?>
                </p>
            </div>
        </div>
    </div>
    <script>
    function printPartDetails() {
        var details = document.getElementById('part-details');
        var wasHidden = details.style.display === 'none' || window.getComputedStyle(details).display === 'none';
        details.style.display = 'block';

        // Use afterprint event to restore state
        function afterPrint() {
            if (wasHidden) details.style.display = 'none';
            window.removeEventListener('afterprint', afterPrint);
        }
        window.addEventListener('afterprint', afterPrint);

        window.print();
    }
    </script>
    <?php
}

// Send email and show message
$to = '<EMAIL>';
$subject = 'طلب شراكة جديد';
$message = "
اسم المنظمة: $orgName
نوع المنظمة: $orgType
بلد المقر: $country
الموقع الإلكتروني: $website
اسم المسؤول: $contactName
المنصب: $contactPosition
البريد الإلكتروني: $email
رقم الهاتف: $phone
نوع الشراكة: $partnershipType
وصف الشراكة: $description
الأهداف: $objectives
الخبرات السابقة: $experience
";
$headers = "From: $email\r\nContent-Type: text/plain; charset=utf-8";

if (mail($to, $subject, $message, $headers)) {
    showSuccessMessage($orgName, $orgType, $country, $website, $contactName, $contactPosition, $email, $phone, $partnershipType, $description, $objectives, $experience, $orgProfile, $proposal, true);
} else {
    showSuccessMessage($orgName, $orgType, $country, $website, $contactName, $contactPosition, $email, $phone, $partnershipType, $description, $objectives, $experience, $orgProfile, $proposal, false);
}
?>