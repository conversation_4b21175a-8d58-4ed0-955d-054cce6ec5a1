<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="185.843" height="212.139" viewBox="0 0 185.843 212.139">
<defs>
<g>
<g id="glyph-0-0">
<path d="M 0.40625 -0.140625 L -3.28125 -10.640625 L -2.46875 -10.921875 L -1.015625 -6.796875 L 0.578125 -7.359375 L -0.875 -11.484375 L -0.0625 -11.765625 L 3.625 -1.265625 L 2.8125 -0.984375 L 1.203125 -5.5625 L -0.390625 -5 L 1.21875 -0.421875 Z M 0.40625 -0.140625 "/>
</g>
<g id="glyph-1-0">
<path d="M -1.25 -5.375 C -1.5625 -6.457031 -1.757812 -7.382812 -1.84375 -8.15625 C -1.90625 -8.726562 -1.914062 -9.25 -1.875 -9.71875 C -1.832031 -10.195312 -1.742188 -10.578125 -1.609375 -10.859375 C -1.441406 -11.191406 -1.195312 -11.40625 -0.875 -11.5 C -0.257812 -11.675781 0.363281 -11.328125 1 -10.453125 C 1.632812 -9.585938 2.203125 -8.28125 2.703125 -6.53125 C 3.191406 -4.8125 3.394531 -3.410156 3.3125 -2.328125 C 3.226562 -1.242188 2.882812 -0.617188 2.28125 -0.453125 C 1.675781 -0.273438 1.054688 -0.625 0.421875 -1.5 C -0.210938 -2.375 -0.769531 -3.664062 -1.25 -5.375 Z M -0.4375 -5.671875 C -0.0820312 -4.460938 0.289062 -3.578125 0.6875 -3.015625 C 1.082031 -2.453125 1.441406 -2.21875 1.765625 -2.3125 C 2.097656 -2.40625 2.28125 -2.789062 2.3125 -3.46875 C 2.34375 -4.144531 2.179688 -5.101562 1.828125 -6.34375 C 1.484375 -7.570312 1.125 -8.457031 0.75 -9 C 0.375 -9.539062 0.015625 -9.765625 -0.328125 -9.671875 C -0.660156 -9.566406 -0.847656 -9.179688 -0.890625 -8.515625 C -0.929688 -7.859375 -0.78125 -6.910156 -0.4375 -5.671875 Z M -0.4375 -5.671875 "/>
</g>
<g id="glyph-2-0">
<path d="M 0.4375 -0.09375 L -1.96875 -10.859375 L -1.125 -11.046875 L 0.875 -2.109375 L 2.96875 -2.578125 L 3.375 -0.75 Z M 0.4375 -0.09375 "/>
</g>
<g id="glyph-3-0">
<path d="M 1.515625 -0.265625 L 0.6875 -4.875 L -1.96875 -10.953125 L -0.96875 -11.125 L 0.796875 -6.953125 L 0.984375 -11.484375 L 1.953125 -11.65625 L 1.5625 -5.015625 L 2.375 -0.421875 Z M 1.515625 -0.265625 "/>
</g>
<g id="glyph-4-0">
</g>
<g id="glyph-5-0">
<path d="M 1.03125 -0.09375 L -1 -11.078125 L -0.109375 -11.15625 L 1.234375 -3.609375 L 1.296875 -11.296875 L 2.3125 -11.375 L 3.765625 -3.71875 L 3.71875 -11.515625 L 4.578125 -11.59375 L 4.5625 -0.421875 L 3.65625 -0.328125 L 2.046875 -8.53125 L 1.953125 -0.171875 Z M 1.03125 -0.09375 "/>
</g>
<g id="glyph-6-0">
<path d="M 0.140625 -5.5 C 0.117188 -6.632812 0.160156 -7.585938 0.265625 -8.359375 C 0.347656 -8.929688 0.46875 -9.441406 0.625 -9.890625 C 0.789062 -10.347656 0.96875 -10.6875 1.15625 -10.90625 C 1.414062 -11.207031 1.71875 -11.359375 2.0625 -11.359375 C 2.695312 -11.378906 3.210938 -10.882812 3.609375 -9.875 C 4.003906 -8.863281 4.222656 -7.453125 4.265625 -5.640625 C 4.304688 -3.847656 4.148438 -2.441406 3.796875 -1.421875 C 3.441406 -0.398438 2.953125 0.117188 2.328125 0.140625 C 1.691406 0.148438 1.175781 -0.34375 0.78125 -1.34375 C 0.394531 -2.34375 0.179688 -3.726562 0.140625 -5.5 Z M 1.015625 -5.609375 C 1.046875 -4.347656 1.175781 -3.394531 1.40625 -2.75 C 1.644531 -2.101562 1.9375 -1.78125 2.28125 -1.78125 C 2.625 -1.789062 2.898438 -2.117188 3.109375 -2.765625 C 3.316406 -3.421875 3.40625 -4.394531 3.375 -5.6875 C 3.34375 -6.957031 3.210938 -7.898438 2.984375 -8.515625 C 2.753906 -9.140625 2.460938 -9.445312 2.109375 -9.4375 C 1.753906 -9.4375 1.472656 -9.117188 1.265625 -8.484375 C 1.066406 -7.847656 0.984375 -6.890625 1.015625 -5.609375 Z M 1.015625 -5.609375 "/>
</g>
<g id="glyph-7-0">
<path d="M 0.4375 0.015625 L 0.84375 -11.109375 L 2.65625 -11.046875 C 3.113281 -11.023438 3.441406 -10.910156 3.640625 -10.703125 C 3.835938 -10.503906 3.988281 -10.144531 4.09375 -9.625 C 4.195312 -9.113281 4.238281 -8.53125 4.21875 -7.875 C 4.1875 -7.03125 4.066406 -6.335938 3.859375 -5.796875 C 3.648438 -5.265625 3.351562 -4.929688 2.96875 -4.796875 C 3.144531 -4.515625 3.285156 -4.203125 3.390625 -3.859375 C 3.503906 -3.515625 3.648438 -2.90625 3.828125 -2.03125 L 4.265625 0.15625 L 3.234375 0.125 L 2.703125 -2.328125 C 2.515625 -3.203125 2.382812 -3.75 2.3125 -3.96875 C 2.238281 -4.195312 2.15625 -4.351562 2.0625 -4.4375 C 1.976562 -4.53125 1.835938 -4.582031 1.640625 -4.59375 L 1.46875 -4.59375 L 1.296875 0.046875 Z M 1.53125 -6.375 L 2.171875 -6.34375 C 2.578125 -6.332031 2.832031 -6.367188 2.9375 -6.453125 C 3.050781 -6.546875 3.140625 -6.703125 3.203125 -6.921875 C 3.265625 -7.140625 3.300781 -7.414062 3.3125 -7.75 C 3.332031 -8.125 3.304688 -8.425781 3.234375 -8.65625 C 3.171875 -8.882812 3.066406 -9.035156 2.921875 -9.109375 C 2.859375 -9.140625 2.65625 -9.160156 2.3125 -9.171875 L 1.640625 -9.203125 Z M 1.53125 -6.375 "/>
</g>
<g id="glyph-8-0">
<path d="M 0.4375 0.046875 L 1.484375 -11.03125 L 2.359375 -10.953125 L 1.890625 -6.03125 L 4.078125 -10.796875 L 5.234375 -10.6875 L 3.21875 -6.515625 L 4.265625 0.40625 L 3.15625 0.296875 L 2.46875 -5.015625 L 1.625 -3.21875 L 1.3125 0.125 Z M 0.4375 0.046875 "/>
</g>
<g id="glyph-9-0">
</g>
<g id="glyph-10-0">
<path d="M 1.234375 -5.375 C 1.429688 -6.488281 1.660156 -7.410156 1.921875 -8.140625 C 2.097656 -8.679688 2.3125 -9.160156 2.5625 -9.578125 C 2.8125 -9.992188 3.054688 -10.285156 3.296875 -10.453125 C 3.597656 -10.703125 3.921875 -10.796875 4.265625 -10.734375 C 4.890625 -10.617188 5.296875 -10.03125 5.484375 -8.96875 C 5.679688 -7.90625 5.617188 -6.476562 5.296875 -4.6875 C 4.984375 -2.925781 4.550781 -1.578125 4 -0.640625 C 3.457031 0.296875 2.878906 0.707031 2.265625 0.59375 C 1.640625 0.476562 1.234375 -0.101562 1.046875 -1.15625 C 0.859375 -2.21875 0.921875 -3.625 1.234375 -5.375 Z M 2.109375 -5.28125 C 1.878906 -4.039062 1.816406 -3.082031 1.921875 -2.40625 C 2.023438 -1.726562 2.25 -1.359375 2.59375 -1.296875 C 2.925781 -1.234375 3.257812 -1.5 3.59375 -2.09375 C 3.925781 -2.695312 4.207031 -3.632812 4.4375 -4.90625 C 4.65625 -6.164062 4.710938 -7.117188 4.609375 -7.765625 C 4.503906 -8.421875 4.28125 -8.78125 3.9375 -8.84375 C 3.582031 -8.90625 3.242188 -8.644531 2.921875 -8.0625 C 2.597656 -7.476562 2.328125 -6.550781 2.109375 -5.28125 Z M 2.109375 -5.28125 "/>
</g>
<g id="glyph-11-0">
<path d="M 0.421875 0.109375 L 3.03125 -10.703125 L 4.796875 -10.28125 C 5.242188 -10.175781 5.539062 -10.003906 5.6875 -9.765625 C 5.84375 -9.523438 5.921875 -9.140625 5.921875 -8.609375 C 5.921875 -8.085938 5.84375 -7.507812 5.6875 -6.875 C 5.488281 -6.050781 5.234375 -5.398438 4.921875 -4.921875 C 4.617188 -4.441406 4.269531 -4.171875 3.875 -4.109375 C 3.976562 -3.796875 4.050781 -3.457031 4.09375 -3.09375 C 4.132812 -2.738281 4.15625 -2.117188 4.15625 -1.234375 L 4.140625 1 L 3.140625 0.765625 L 3.09375 -1.75 C 3.09375 -2.632812 3.078125 -3.191406 3.046875 -3.421875 C 3.023438 -3.660156 2.976562 -3.832031 2.90625 -3.9375 C 2.84375 -4.039062 2.710938 -4.117188 2.515625 -4.171875 L 2.359375 -4.21875 L 1.265625 0.296875 Z M 2.765625 -5.953125 L 3.390625 -5.796875 C 3.773438 -5.703125 4.03125 -5.6875 4.15625 -5.75 C 4.289062 -5.8125 4.410156 -5.941406 4.515625 -6.140625 C 4.628906 -6.335938 4.722656 -6.597656 4.796875 -6.921875 C 4.890625 -7.285156 4.925781 -7.585938 4.90625 -7.828125 C 4.882812 -8.078125 4.804688 -8.25 4.671875 -8.34375 C 4.617188 -8.382812 4.421875 -8.441406 4.078125 -8.515625 L 3.4375 -8.6875 Z M 2.765625 -5.953125 "/>
</g>
<g id="glyph-12-0">
<path d="M 3.515625 -3.203125 L 4.0625 -5 L 5.828125 -4.453125 L 4.53125 -0.21875 C 4.226562 0.164062 3.859375 0.472656 3.421875 0.703125 C 2.992188 0.929688 2.613281 0.992188 2.28125 0.890625 C 1.851562 0.753906 1.550781 0.410156 1.375 -0.140625 C 1.207031 -0.703125 1.175781 -1.441406 1.28125 -2.359375 C 1.382812 -3.285156 1.59375 -4.257812 1.90625 -5.28125 C 2.25 -6.382812 2.644531 -7.335938 3.09375 -8.140625 C 3.539062 -8.953125 4 -9.535156 4.46875 -9.890625 C 4.84375 -10.148438 5.222656 -10.222656 5.609375 -10.109375 C 6.109375 -9.953125 6.414062 -9.550781 6.53125 -8.90625 C 6.65625 -8.269531 6.609375 -7.453125 6.390625 -6.453125 L 5.453125 -6.3125 C 5.554688 -6.851562 5.570312 -7.296875 5.5 -7.640625 C 5.425781 -7.992188 5.273438 -8.207031 5.046875 -8.28125 C 4.691406 -8.382812 4.316406 -8.171875 3.921875 -7.640625 C 3.523438 -7.117188 3.148438 -6.269531 2.796875 -5.09375 C 2.410156 -3.84375 2.226562 -2.863281 2.25 -2.15625 C 2.269531 -1.457031 2.453125 -1.054688 2.796875 -0.953125 C 2.972656 -0.898438 3.171875 -0.9375 3.390625 -1.0625 C 3.617188 -1.1875 3.832031 -1.351562 4.03125 -1.5625 L 4.453125 -2.90625 Z M 3.515625 -3.203125 "/>
</g>
<g id="glyph-13-0">
<path d="M 4.015625 1.5 L 3.125 1.171875 L 3.671875 -1.328125 L 2.078125 -1.9375 L 0.84375 0.3125 L 0 0 L 5.453125 -9.84375 L 6.3125 -9.515625 Z M 4.0625 -3.1875 L 4.953125 -7.25 L 2.984375 -3.59375 Z M 4.0625 -3.1875 "/>
</g>
<g id="glyph-14-0">
<path d="M 0.40625 0.171875 L 4.90625 -10 L 5.671875 -9.65625 L 4.25 -2.171875 L 7.25 -8.953125 L 8 -8.625 L 3.5 1.546875 L 2.703125 1.1875 L 4.0625 -6.125 L 1.125 0.5 Z M 0.40625 0.171875 "/>
</g>
<g id="glyph-15-0">
<path d="M 0.359375 0.171875 L 5.265625 -9.8125 L 6.046875 -9.421875 L 1.140625 0.5625 Z M 0.359375 0.171875 "/>
</g>
<g id="glyph-16-0">
<path d="M 0.0625 0.03125 L 1.015625 -1.765625 L 6.40625 -7.078125 L 4.65625 -8.015625 L 5.546875 -9.65625 L 8.296875 -8.171875 L 7.46875 -6.640625 L 1.875 -1.125 L 4 0.015625 L 3.109375 1.671875 Z M 0.0625 0.03125 "/>
</g>
<g id="glyph-17-0">
<path d="M 3.65625 2.21875 L 2.859375 1.734375 L 3.84375 -0.609375 L 2.390625 -1.5 L 0.78125 0.46875 L 0 0 L 7.203125 -8.640625 L 7.96875 -8.171875 Z M 4.578125 -2.375 L 6.234375 -6.203125 L 3.59375 -2.96875 Z M 4.578125 -2.375 "/>
</g>
<g id="glyph-18-0">
<path d="M 1.15625 0.78125 L 6.328125 -6.890625 L 5.28125 -7.609375 L 6.328125 -9.15625 L 9.140625 -7.25 L 8.09375 -5.703125 L 7.03125 -6.40625 L 1.859375 1.265625 Z M 1.15625 0.78125 "/>
</g>
<g id="glyph-19-0">
<path d="M 0.328125 0.234375 L 6.875 -8.765625 L 7.5625 -8.25 L 1.015625 0.75 Z M 0.328125 0.234375 "/>
</g>
<g id="glyph-20-0">
<path d="M 3.625 -4.140625 C 4.332031 -5.023438 4.972656 -5.726562 5.546875 -6.25 C 5.972656 -6.632812 6.394531 -6.953125 6.8125 -7.203125 C 7.226562 -7.453125 7.578125 -7.597656 7.859375 -7.640625 C 8.242188 -7.703125 8.570312 -7.628906 8.84375 -7.421875 C 9.34375 -7.023438 9.421875 -6.3125 9.078125 -5.28125 C 8.742188 -4.25 8.015625 -3.023438 6.890625 -1.609375 C 5.773438 -0.203125 4.753906 0.773438 3.828125 1.328125 C 2.910156 1.890625 2.207031 1.976562 1.71875 1.59375 C 1.21875 1.195312 1.132812 0.488281 1.46875 -0.53125 C 1.800781 -1.550781 2.519531 -2.753906 3.625 -4.140625 Z M 4.359375 -3.671875 C 3.578125 -2.679688 3.070312 -1.859375 2.84375 -1.203125 C 2.613281 -0.554688 2.632812 -0.125 2.90625 0.09375 C 3.175781 0.300781 3.59375 0.222656 4.15625 -0.140625 C 4.726562 -0.515625 5.414062 -1.207031 6.21875 -2.21875 C 7.007812 -3.21875 7.515625 -4.023438 7.734375 -4.640625 C 7.960938 -5.265625 7.941406 -5.6875 7.671875 -5.90625 C 7.390625 -6.125 6.96875 -6.054688 6.40625 -5.703125 C 5.84375 -5.347656 5.160156 -4.671875 4.359375 -3.671875 Z M 4.359375 -3.671875 "/>
</g>
<g id="glyph-21-0">
<path d="M 0.328125 0.296875 L 7.75 -7.984375 L 8.375 -7.421875 L 4.71875 -0.734375 L 9.671875 -6.265625 L 10.265625 -5.71875 L 2.84375 2.5625 L 2.203125 1.96875 L 5.765625 -4.5625 L 0.921875 0.828125 Z M 0.328125 0.296875 "/>
</g>
<g id="glyph-22-0">
</g>
<g id="glyph-23-0">
<path d="M 0.296875 0.3125 L 8.328125 -7.390625 L 10.359375 -5.28125 L 9 -3.984375 L 7.5625 -5.46875 L 5.65625 -3.640625 L 6.890625 -2.359375 L 5.546875 -1.0625 L 4.3125 -2.34375 L 0.890625 0.9375 Z M 0.296875 0.3125 "/>
</g>
<g id="glyph-24-0">
<path d="M 4.34375 -3.375 C 5.207031 -4.113281 5.96875 -4.6875 6.625 -5.09375 C 7.125 -5.382812 7.597656 -5.609375 8.046875 -5.765625 C 8.492188 -5.929688 8.867188 -6.015625 9.171875 -6.015625 C 9.566406 -6.003906 9.875 -5.867188 10.09375 -5.609375 C 10.507812 -5.117188 10.453125 -4.398438 9.921875 -3.453125 C 9.398438 -2.515625 8.445312 -1.457031 7.0625 -0.28125 C 5.707031 0.882812 4.519531 1.65625 3.5 2.03125 C 2.488281 2.414062 1.78125 2.367188 1.375 1.890625 C 0.957031 1.410156 1.007812 0.703125 1.53125 -0.234375 C 2.0625 -1.171875 3 -2.21875 4.34375 -3.375 Z M 4.984375 -2.765625 C 4.023438 -1.953125 3.367188 -1.242188 3.015625 -0.640625 C 2.671875 -0.046875 2.609375 0.378906 2.828125 0.640625 C 3.046875 0.898438 3.472656 0.898438 4.109375 0.640625 C 4.742188 0.390625 5.550781 -0.15625 6.53125 -1 C 7.5 -1.820312 8.148438 -2.515625 8.484375 -3.078125 C 8.816406 -3.648438 8.867188 -4.070312 8.640625 -4.34375 C 8.410156 -4.613281 7.984375 -4.628906 7.359375 -4.390625 C 6.742188 -4.148438 5.953125 -3.609375 4.984375 -2.765625 Z M 4.984375 -2.765625 "/>
</g>
<g id="glyph-25-0">
<path d="M 0.265625 0.34375 L 9.125 -6.375 L 10.21875 -4.921875 C 10.5 -4.554688 10.617188 -4.234375 10.578125 -3.953125 C 10.546875 -3.671875 10.363281 -3.328125 10.03125 -2.921875 C 9.707031 -2.515625 9.285156 -2.113281 8.765625 -1.71875 C 8.085938 -1.207031 7.472656 -0.863281 6.921875 -0.6875 C 6.378906 -0.507812 5.9375 -0.523438 5.59375 -0.734375 C 5.488281 -0.421875 5.335938 -0.113281 5.140625 0.1875 C 4.941406 0.488281 4.5625 0.984375 4 1.671875 L 2.578125 3.390625 L 1.953125 2.578125 L 3.5 0.609375 C 4.0625 -0.0859375 4.40625 -0.535156 4.53125 -0.734375 C 4.664062 -0.929688 4.738281 -1.09375 4.75 -1.21875 C 4.757812 -1.34375 4.707031 -1.484375 4.59375 -1.640625 L 4.484375 -1.78125 L 0.78125 1.03125 Z M 5.890625 -2.84375 L 6.28125 -2.328125 C 6.53125 -2.003906 6.722656 -1.828125 6.859375 -1.796875 C 7.003906 -1.773438 7.175781 -1.804688 7.375 -1.890625 C 7.582031 -1.984375 7.820312 -2.128906 8.09375 -2.328125 C 8.382812 -2.554688 8.597656 -2.769531 8.734375 -2.96875 C 8.878906 -3.164062 8.929688 -3.34375 8.890625 -3.5 C 8.878906 -3.570312 8.769531 -3.75 8.5625 -4.03125 L 8.15625 -4.5625 Z M 5.890625 -2.84375 "/>
</g>
<g id="glyph-26-0">
</g>
<g id="glyph-27-0">
<path d="M 9.609375 -5.625 L 10.453125 -4.3125 C 10.640625 -4.019531 10.726562 -3.753906 10.71875 -3.515625 C 10.6875 -3.203125 10.507812 -2.835938 10.1875 -2.421875 C 9.875 -2.015625 9.441406 -1.578125 8.890625 -1.109375 C 8.335938 -0.640625 7.625 -0.125 6.75 0.4375 C 5.976562 0.925781 5.296875 1.3125 4.703125 1.59375 C 3.953125 1.9375 3.316406 2.160156 2.796875 2.265625 C 2.378906 2.347656 2.019531 2.34375 1.71875 2.25 C 1.476562 2.164062 1.273438 1.988281 1.109375 1.71875 L 0.234375 0.375 Z M 8.5 -3.890625 L 2.28125 0.078125 L 2.625 0.609375 C 2.75 0.816406 2.875 0.945312 3 1 C 3.15625 1.0625 3.34375 1.070312 3.5625 1.03125 C 3.789062 1 4.128906 0.875 4.578125 0.65625 C 5.035156 0.4375 5.628906 0.09375 6.359375 -0.375 C 7.078125 -0.832031 7.613281 -1.210938 7.96875 -1.515625 C 8.332031 -1.816406 8.59375 -2.082031 8.75 -2.3125 C 8.914062 -2.539062 8.992188 -2.738281 8.984375 -2.90625 C 8.984375 -3.050781 8.890625 -3.273438 8.703125 -3.578125 Z M 8.5 -3.890625 "/>
</g>
<g id="glyph-28-0">
<path d="M 0.21875 0.375 L 9.90625 -5.09375 L 11.453125 -2.34375 L 9.828125 -1.421875 L 8.703125 -3.421875 L 6.546875 -2.203125 L 7.59375 -0.328125 L 5.953125 0.59375 L 4.90625 -1.28125 L 2.28125 0.203125 L 3.4375 2.28125 L 1.796875 3.203125 Z M 0.21875 0.375 "/>
</g>
<g id="glyph-29-0">
<path d="M 0.671875 1.359375 L 9.96875 -4.9375 L 10.390625 -4.09375 L 3.484375 0.53125 L 11.328125 -2.203125 L 11.734375 -1.375 L 1.078125 2.1875 Z M 0.671875 1.359375 "/>
</g>
<g id="glyph-30-0">
<path d="M 0.171875 0.40625 L 10.390625 -3.984375 L 11.640625 -1.09375 L 9.921875 -0.359375 L 9.015625 -2.46875 L 6.75 -1.484375 L 7.59375 0.484375 L 5.859375 1.21875 L 5.015625 -0.75 L 2.234375 0.453125 L 3.171875 2.640625 L 1.453125 3.375 Z M 0.171875 0.40625 "/>
</g>
<g id="glyph-31-0">
<path d="M 0.15625 0.421875 L 10.5 -3.40625 L 10.796875 -2.59375 L 2.21875 0.578125 L 2.96875 2.578125 L 1.203125 3.234375 Z M 0.15625 0.421875 "/>
</g>
<g id="glyph-32-0">
<path d="M 5.328125 -1.359375 C 6.421875 -1.703125 7.347656 -1.921875 8.109375 -2.015625 C 8.679688 -2.097656 9.207031 -2.117188 9.6875 -2.078125 C 10.164062 -2.046875 10.535156 -1.972656 10.796875 -1.859375 C 11.160156 -1.691406 11.394531 -1.445312 11.5 -1.125 C 11.675781 -0.507812 11.335938 0.125 10.484375 0.78125 C 9.628906 1.4375 8.332031 2.035156 6.59375 2.578125 C 4.882812 3.097656 3.488281 3.328125 2.40625 3.265625 C 1.320312 3.210938 0.691406 2.882812 0.515625 2.28125 C 0.328125 1.675781 0.660156 1.046875 1.515625 0.390625 C 2.367188 -0.265625 3.640625 -0.847656 5.328125 -1.359375 Z M 5.65625 -0.546875 C 4.457031 -0.171875 3.578125 0.210938 3.015625 0.609375 C 2.460938 1.015625 2.238281 1.378906 2.34375 1.703125 C 2.445312 2.035156 2.835938 2.210938 3.515625 2.234375 C 4.203125 2.253906 5.160156 2.070312 6.390625 1.6875 C 7.609375 1.320312 8.484375 0.941406 9.015625 0.546875 C 9.546875 0.160156 9.757812 -0.203125 9.65625 -0.546875 C 9.550781 -0.878906 9.164062 -1.054688 8.5 -1.078125 C 7.832031 -1.097656 6.882812 -0.921875 5.65625 -0.546875 Z M 5.65625 -0.546875 "/>
</g>
<g id="glyph-33-0">
<path d="M 0.109375 0.421875 L 10.921875 -2.21875 L 11.25 -0.875 C 11.363281 -0.375 11.390625 -0.03125 11.328125 0.15625 C 11.222656 0.425781 10.914062 0.707031 10.40625 1 C 9.894531 1.300781 9.210938 1.554688 8.359375 1.765625 C 7.703125 1.921875 7.132812 2.007812 6.65625 2.03125 C 6.175781 2.050781 5.789062 2.019531 5.5 1.9375 C 5.207031 1.863281 5.003906 1.765625 4.890625 1.640625 C 4.734375 1.460938 4.609375 1.1875 4.515625 0.8125 L 4.390625 0.265625 L 0.3125 1.265625 Z M 9.296875 -0.9375 L 6.234375 -0.1875 L 6.34375 0.265625 C 6.414062 0.585938 6.519531 0.789062 6.65625 0.875 C 6.800781 0.957031 7.003906 1.003906 7.265625 1.015625 C 7.523438 1.023438 7.8125 0.988281 8.125 0.90625 C 8.519531 0.8125 8.832031 0.6875 9.0625 0.53125 C 9.289062 0.375 9.421875 0.226562 9.453125 0.09375 C 9.484375 -0.03125 9.460938 -0.242188 9.390625 -0.546875 Z M 9.296875 -0.9375 "/>
</g>
<g id="glyph-34-0">
<path d="M 0.078125 0.421875 L 11.03125 -1.5625 L 11.25 -0.3125 L 3.90625 1.8125 L 11.53125 1.21875 L 11.765625 2.46875 L 0.8125 4.453125 L 0.671875 3.671875 L 9.296875 2.109375 L 0.515625 2.84375 L 0.375 2.03125 L 8.84375 -0.359375 L 0.21875 1.203125 Z M 0.078125 0.421875 "/>
</g>
<g id="glyph-35-0">
<path d="M 0.046875 0.4375 L 11.09375 -0.890625 L 11.46875 2.234375 L 9.609375 2.46875 L 9.34375 0.1875 L 6.890625 0.46875 L 7.140625 2.59375 L 5.28125 2.828125 L 5.03125 0.703125 L 2.015625 1.0625 L 2.296875 3.421875 L 0.4375 3.640625 Z M 0.046875 0.4375 "/>
</g>
<g id="glyph-36-0">
<path d="M 0.03125 0.4375 L 11.140625 -0.265625 L 11.1875 0.578125 L 3.890625 2.78125 L 11.296875 2.3125 L 11.359375 3.125 L 0.25 3.828125 L 0.1875 2.953125 L 7.3125 0.765625 L 0.078125 1.234375 Z M 0.03125 0.4375 "/>
</g>
<g id="glyph-37-0">
<path d="M 0.015625 1.390625 L 9.265625 1.234375 L 9.25 -0.03125 L 11.125 -0.0625 L 11.1875 3.328125 L 9.3125 3.359375 L 9.28125 2.09375 L 0.03125 2.25 Z M 0.015625 1.390625 "/>
</g>
<g id="glyph-38-0">
<path d="M 6.5 6.71875 L 5.6875 5.875 C 4.625 6.320312 3.800781 6.523438 3.21875 6.484375 C 2.539062 6.429688 1.734375 6.050781 0.796875 5.34375 C 0.617188 5.21875 0.421875 5.039062 0.203125 4.8125 C -0.421875 4.164062 -0.832031 3.53125 -1.03125 2.90625 C -1.300781 2.113281 -1.160156 1.445312 -0.609375 0.90625 C -0.160156 0.476562 0.648438 0.0820312 1.828125 -0.28125 C 3.117188 -0.6875 3.953125 -0.691406 4.328125 -0.296875 L 4.109375 -0.078125 C 4.117188 -0.0976562 3.785156 -0.0625 3.109375 0.03125 C 2.441406 0.125 1.941406 0.332031 1.609375 0.65625 C 1.160156 1.09375 1.019531 1.613281 1.1875 2.21875 C 1.332031 2.726562 1.65625 3.238281 2.15625 3.75 C 2.46875 4.070312 2.878906 4.425781 3.390625 4.8125 C 4.085938 5.320312 4.515625 5.5 4.671875 5.34375 C 5.023438 5.007812 5.363281 4.046875 5.6875 2.453125 L 7.90625 2.0625 C 7.800781 2.519531 7.703125 2.96875 7.609375 3.40625 C 7.503906 3.945312 7.488281 4.421875 7.5625 4.828125 C 7.851562 5.128906 8 5.28125 8 5.28125 Z M 6.5 6.71875 "/>
</g>
<g id="glyph-39-0">
<path d="M 5.890625 1.609375 C 5.628906 1.929688 5.285156 2.207031 4.859375 2.4375 C 4.484375 2.644531 4.09375 2.859375 3.6875 3.078125 L 1.34375 1.125 C 1.363281 1.03125 1.367188 0.90625 1.359375 0.75 C 1.265625 0.75 1.117188 0.753906 0.921875 0.765625 L -0.359375 -0.296875 L 0.96875 -1.890625 L 2.4375 -0.65625 C 2.550781 -0.789062 2.765625 -0.960938 3.078125 -1.171875 C 4.097656 -1.578125 4.847656 -1.679688 5.328125 -1.484375 L 5.25 -1.375 C 5.144531 -1.25 4.832031 -1.035156 4.3125 -0.734375 C 3.800781 -0.441406 3.472656 -0.207031 3.328125 -0.03125 C 3.316406 -0.0078125 3.320312 0.03125 3.34375 0.09375 L 4.453125 1 C 4.503906 0.988281 4.539062 0.96875 4.5625 0.9375 C 4.613281 0.875 4.675781 0.78125 4.75 0.65625 C 4.820312 0.53125 4.878906 0.4375 4.921875 0.375 C 5.066406 0.207031 5.457031 0.0703125 6.09375 -0.03125 C 6.625 -0.125 7.039062 -0.160156 7.34375 -0.140625 Z M 5.890625 1.609375 "/>
</g>
<g id="glyph-40-0">
<path d="M 7.5 5.03125 L 5.65625 3.796875 C 5.5625 3.722656 5.550781 3.507812 5.625 3.15625 C 5.6875 2.945312 5.742188 2.726562 5.796875 2.5 C 5.671875 2.5 5.429688 2.59375 5.078125 2.78125 C 4.734375 2.96875 4.515625 3.03125 4.421875 2.96875 L 0.890625 0.59375 C 0.785156 0.53125 0.726562 0.421875 0.71875 0.265625 C 0.71875 0.109375 0.753906 -0.0195312 0.828125 -0.125 C 1.085938 -0.507812 1.503906 -0.960938 2.078125 -1.484375 C 2.773438 -2.148438 3.253906 -2.40625 3.515625 -2.25 C 3.453125 -2.144531 3.347656 -2 3.203125 -1.8125 C 3.054688 -1.632812 2.945312 -1.5 2.875 -1.40625 C 2.644531 -1.050781 2.609375 -0.816406 2.765625 -0.703125 L 6.078125 1.515625 L 6.171875 1.390625 L 7.21875 -1.6875 L 9.359375 -2.828125 C 9.335938 -2.742188 9.015625 -1.757812 8.390625 0.125 C 7.898438 1.65625 7.707031 2.519531 7.8125 2.71875 L 8.65625 3.296875 Z M 7.5 5.03125 "/>
</g>
<g id="glyph-41-0">
<path d="M 9.078125 -4.046875 C 8.484375 -3.722656 7.898438 -3.539062 7.328125 -3.5 L 6.1875 -5.015625 C 5.832031 -4.953125 5.445312 -4.847656 5.03125 -4.703125 C 4.65625 -5.003906 4.253906 -5.539062 3.828125 -6.3125 C 4.441406 -6.550781 5.015625 -6.738281 5.546875 -6.875 C 6.003906 -6.039062 6.335938 -5.566406 6.546875 -5.453125 C 6.617188 -5.421875 6.835938 -5.441406 7.203125 -5.515625 C 7.429688 -5.578125 7.664062 -5.644531 7.90625 -5.71875 Z M 6.625 3.4375 L -0.453125 -0.234375 L 0.5 -2.078125 L 2.15625 -1.21875 C 2.226562 -1.351562 2.363281 -1.523438 2.5625 -1.734375 C 3.125 -2.085938 3.9375 -2.351562 5 -2.53125 C 6.144531 -2.726562 7 -2.6875 7.5625 -2.40625 C 7.945312 -2.207031 8.128906 -1.851562 8.109375 -1.34375 C 8.085938 -0.957031 7.96875 -0.550781 7.75 -0.125 C 7.601562 0.164062 7.132812 0.519531 6.34375 0.9375 L 7.578125 1.59375 Z M 5.75 0.1875 C 5.863281 -0.03125 5.890625 -0.285156 5.828125 -0.578125 C 5.765625 -0.867188 5.625 -1.070312 5.40625 -1.1875 C 5.257812 -1.257812 4.960938 -1.300781 4.515625 -1.3125 C 4.078125 -1.332031 3.832031 -1.285156 3.78125 -1.171875 C 3.6875 -0.992188 3.78125 -0.769531 4.0625 -0.5 C 4.28125 -0.25 4.507812 -0.0625 4.75 0.0625 C 5.3125 0.351562 5.644531 0.394531 5.75 0.1875 Z M 5.75 0.1875 "/>
</g>
<g id="glyph-42-0">
<path d="M 5.015625 1 C 4.828125 1.457031 4.34375 1.953125 3.5625 2.484375 C 3.488281 2.273438 3.347656 1.96875 3.140625 1.5625 C 2.992188 1.3125 2.597656 1.054688 1.953125 0.796875 L -0.375 -0.15625 L 0.40625 -2.078125 L 2.28125 -1.3125 C 2.34375 -1.34375 2.40625 -1.4375 2.46875 -1.59375 C 2.570312 -1.84375 2.75 -2.507812 3 -3.59375 C 3.257812 -4.675781 3.382812 -5.320312 3.375 -5.53125 C 3.363281 -5.582031 3.296875 -5.707031 3.171875 -5.90625 C 3.046875 -6.101562 2.992188 -6.234375 3.015625 -6.296875 C 3.066406 -6.429688 3.410156 -6.707031 4.046875 -7.125 C 4.691406 -7.550781 5.113281 -7.757812 5.3125 -7.75 L 5.21875 -7.578125 C 5.101562 -7.347656 5.070312 -7.128906 5.125 -6.921875 C 5.175781 -6.765625 5.425781 -6.410156 5.875 -5.859375 C 6.257812 -5.359375 6.550781 -5.015625 6.75 -4.828125 C 6.75 -4.828125 6.34375 -4.613281 5.53125 -4.1875 C 4.894531 -3.863281 4.554688 -3.648438 4.515625 -3.546875 C 4.148438 -2.648438 3.925781 -1.867188 3.84375 -1.203125 C 3.820312 -1.023438 4.039062 -0.691406 4.5 -0.203125 C 4.96875 0.285156 5.140625 0.6875 5.015625 1 Z M 5.015625 1 "/>
</g>
<g id="glyph-43-0">
<path d="M 4.03125 -1.921875 C 3.632812 -0.691406 2.925781 0.210938 1.90625 0.796875 L 2.953125 -5.296875 C 3.410156 -5.878906 4.144531 -6.507812 5.15625 -7.1875 C 4.75 -4.726562 4.375 -2.972656 4.03125 -1.921875 Z M 4.03125 -1.921875 "/>
</g>
<g id="glyph-44-0">
</g>
<g id="glyph-45-0">
<path d="M 9.15625 1.609375 L 7.828125 1.375 C 7.816406 1.382812 7.773438 1.40625 7.703125 1.4375 C 6.578125 3.03125 4.96875 3.644531 2.875 3.28125 C 2.019531 3.125 1.304688 2.785156 0.734375 2.265625 C 0.078125 1.691406 -0.1875 1.054688 -0.0625 0.359375 C 0.0507812 -0.285156 0.390625 -0.976562 0.953125 -1.71875 C 1.515625 -2.46875 2.132812 -3.050781 2.8125 -3.46875 C 2.8125 -3.425781 2.800781 -3.332031 2.78125 -3.1875 C 2.769531 -3.050781 2.757812 -2.972656 2.75 -2.953125 C 2.769531 -3.015625 2.613281 -2.863281 2.28125 -2.5 C 1.945312 -2.132812 1.734375 -1.695312 1.640625 -1.1875 C 1.410156 0.113281 2.238281 0.929688 4.125 1.265625 C 4.5 1.328125 4.957031 1.359375 5.5 1.359375 C 6.113281 1.335938 6.519531 1.257812 6.71875 1.125 L 7.109375 -5.984375 C 7.128906 -6.117188 7.394531 -6.4375 7.90625 -6.9375 C 8.382812 -7.394531 8.710938 -7.6875 8.890625 -7.8125 C 8.921875 -7.238281 8.921875 -6.695312 8.890625 -6.1875 C 8.773438 -5.0625 8.628906 -3.304688 8.453125 -0.921875 C 8.453125 -0.722656 8.503906 -0.613281 8.609375 -0.59375 L 9.515625 -0.4375 Z M 9.15625 1.609375 "/>
</g>
<g id="glyph-46-0">
<path d="M 7.9375 0.453125 L 6.453125 0.359375 C 6.367188 0.429688 6.257812 0.585938 6.125 0.828125 C 6 1.078125 5.859375 1.226562 5.703125 1.28125 L 1.9375 -0.984375 C 1.863281 -0.953125 1.742188 -0.738281 1.578125 -0.34375 C 1.429688 -0.0625 1.234375 0.0703125 0.984375 0.0625 L -0.40625 -0.015625 L -0.296875 -2.09375 L 1.78125 -1.984375 C 1.882812 -2.035156 2.054688 -2.253906 2.296875 -2.640625 C 2.535156 -3.035156 2.726562 -3.289062 2.875 -3.40625 C 3.238281 -3.726562 3.691406 -4 4.234375 -4.21875 C 4.773438 -4.4375 5.257812 -4.53125 5.6875 -4.5 C 5.90625 -4.488281 6.050781 -4.453125 6.125 -4.390625 C 6.195312 -4.328125 6.234375 -4.210938 6.234375 -4.046875 C 6.191406 -3.617188 6.28125 -3.148438 6.5 -2.640625 C 6.738281 -2.015625 7.066406 -1.6875 7.484375 -1.65625 L 8.046875 -1.625 Z M 4.984375 -1.859375 L 5 -2.25 C 5.019531 -2.539062 4.835938 -2.695312 4.453125 -2.71875 C 4.148438 -2.726562 3.910156 -2.675781 3.734375 -2.5625 C 3.929688 -2.425781 4.132812 -2.289062 4.34375 -2.15625 C 4.625 -1.96875 4.835938 -1.867188 4.984375 -1.859375 Z M 4.984375 -1.859375 "/>
</g>
<g id="glyph-47-0">
<path d="M 6.90625 -0.296875 L 4.375 -0.1875 C 4.25 -0.394531 4.039062 -0.703125 3.75 -1.109375 C 3.625 -0.890625 3.425781 -0.566406 3.15625 -0.140625 L -0.4375 0.015625 L -0.53125 -2.0625 L 2.84375 -2.203125 C 2.40625 -2.742188 1.867188 -3.003906 1.234375 -2.984375 C 1.203125 -2.984375 0.960938 -2.941406 0.515625 -2.859375 C 0.929688 -3.835938 1.253906 -4.457031 1.484375 -4.71875 C 1.898438 -5.175781 2.578125 -5.425781 3.515625 -5.46875 C 4.078125 -5.5 4.644531 -5.347656 5.21875 -5.015625 C 5.820312 -4.640625 6.132812 -4.191406 6.15625 -3.671875 C 6.15625 -3.492188 6.082031 -3.269531 5.9375 -3 C 5.800781 -2.769531 5.664062 -2.535156 5.53125 -2.296875 C 5.613281 -2.316406 6.039062 -2.34375 6.8125 -2.375 Z M 6.90625 -0.296875 "/>
</g>
<g id="glyph-48-0">
<path d="M 3.609375 -7.515625 L 2.296875 -5.421875 L 2.84375 -2.921875 C 2.894531 -2.765625 2.890625 -2.351562 2.828125 -1.6875 C 2.765625 -1.03125 2.695312 -0.640625 2.625 -0.515625 C 2.59375 -0.472656 2.488281 -0.398438 2.3125 -0.296875 L -0.34375 0.046875 L -0.609375 -2.015625 L 1.484375 -2.28125 C 1.523438 -2.351562 1.539062 -2.425781 1.53125 -2.5 C 1.488281 -2.851562 1.3125 -3.535156 1 -4.546875 C 0.695312 -5.566406 0.46875 -6.210938 0.3125 -6.484375 C 0.101562 -6.597656 -0.125 -6.765625 -0.375 -6.984375 C 0.0078125 -8.078125 0.390625 -8.863281 0.765625 -9.34375 L 0.890625 -9.265625 C 0.929688 -9.023438 0.992188 -8.828125 1.078125 -8.671875 C 1.171875 -8.515625 1.5625 -8.296875 2.25 -8.015625 C 2.769531 -7.785156 3.222656 -7.617188 3.609375 -7.515625 Z M 3.609375 -7.515625 "/>
</g>
<g id="glyph-49-0">
<path d="M 2.578125 -3.640625 C 2.828125 -2.378906 2.65625 -1.242188 2.0625 -0.234375 L 0 -6.0625 C 0.101562 -6.800781 0.429688 -7.710938 0.984375 -8.796875 C 1.828125 -6.441406 2.359375 -4.722656 2.578125 -3.640625 Z M 2.578125 -3.640625 "/>
</g>
<g id="glyph-50-0">
</g>
<g id="glyph-51-0">
<path d="M 2.90625 -10.0625 C 2.863281 -9.882812 2.757812 -9.597656 2.59375 -9.203125 C 2.40625 -8.742188 2.269531 -8.503906 2.1875 -8.484375 C 2.03125 -8.421875 1.425781 -8.488281 0.375 -8.6875 C 0.28125 -8.539062 0.078125 -8.195312 -0.234375 -7.65625 C -0.660156 -7.625 -1.273438 -7.695312 -2.078125 -7.875 C -2.015625 -8.0625 -1.785156 -8.585938 -1.390625 -9.453125 C -0.484375 -9.203125 0.078125 -9.117188 0.296875 -9.203125 C 0.285156 -9.191406 0.53125 -9.554688 1.03125 -10.296875 Z M 9.15625 -3.21875 L 7.1875 -2.53125 C 6.789062 -2.382812 6.40625 -2.5 6.03125 -2.875 C 5.769531 -3.21875 5.507812 -3.5625 5.25 -3.90625 C 5.1875 -3.8125 5.097656 -3.460938 4.984375 -2.859375 C 4.859375 -2.335938 4.707031 -2.0625 4.53125 -2.03125 L 0.90625 -1.40625 C 0.820312 -2.539062 0.820312 -3.347656 0.90625 -3.828125 C 0.988281 -4.304688 1.222656 -4.769531 1.609375 -5.21875 C 2.160156 -5.832031 2.71875 -6.445312 3.28125 -7.0625 C 3.125 -7.21875 3 -7.375 2.90625 -7.53125 L 3.390625 -9.78125 L 3.5 -9.75 C 3.757812 -9.375 4.253906 -8.539062 4.984375 -7.25 C 5.617188 -6.144531 6.113281 -5.375 6.46875 -4.9375 C 6.65625 -4.695312 7 -4.660156 7.5 -4.828125 L 8.46875 -5.171875 Z M 4.734375 -4.71875 L 3.8125 -6.171875 C 3.738281 -5.890625 3.566406 -5.5 3.296875 -5 C 3.117188 -4.832031 2.929688 -4.59375 2.734375 -4.28125 Z M 4.734375 -4.71875 "/>
</g>
<g id="glyph-52-0">
<path d="M 7.125 -3.53125 L 5.796875 -2.875 C 5.753906 -2.769531 5.734375 -2.578125 5.734375 -2.296875 C 5.742188 -2.023438 5.695312 -1.828125 5.59375 -1.703125 L 1.203125 -1.8125 C 1.148438 -1.738281 1.148438 -1.492188 1.203125 -1.078125 C 1.210938 -0.765625 1.101562 -0.550781 0.875 -0.4375 L -0.359375 0.1875 L -1.28125 -1.671875 L 0.578125 -2.59375 C 0.628906 -2.695312 0.664062 -2.976562 0.6875 -3.4375 C 0.707031 -3.894531 0.75 -4.207031 0.8125 -4.375 C 0.976562 -4.832031 1.238281 -5.289062 1.59375 -5.75 C 1.957031 -6.207031 2.335938 -6.53125 2.734375 -6.71875 C 2.929688 -6.820312 3.070312 -6.863281 3.15625 -6.84375 C 3.25 -6.820312 3.335938 -6.738281 3.421875 -6.59375 C 3.609375 -6.207031 3.921875 -5.84375 4.359375 -5.5 C 4.878906 -5.070312 5.328125 -4.953125 5.703125 -5.140625 L 6.203125 -5.390625 Z M 3.421875 -4.0625 L 3.25 -4.40625 C 3.113281 -4.664062 2.875 -4.710938 2.53125 -4.546875 C 2.257812 -4.410156 2.070312 -4.253906 1.96875 -4.078125 C 2.21875 -4.046875 2.460938 -4.019531 2.703125 -4 C 3.035156 -3.988281 3.273438 -4.007812 3.421875 -4.0625 Z M 3.421875 -4.0625 "/>
</g>
<g id="glyph-53-0">
<path d="M 2.75 -10.65625 C 2.863281 -10.5 2.640625 -9.882812 2.078125 -8.8125 C 1.984375 -8.757812 1.65625 -8.75 1.09375 -8.78125 C 0.53125 -8.8125 0.226562 -8.859375 0.1875 -8.921875 C 0.132812 -9.003906 0.179688 -9.335938 0.328125 -9.921875 C 0.472656 -10.503906 0.585938 -10.820312 0.671875 -10.875 C 0.742188 -10.925781 1.097656 -10.914062 1.734375 -10.84375 C 2.378906 -10.769531 2.71875 -10.707031 2.75 -10.65625 Z M 9.203125 -6.171875 L -0.40625 0.265625 L -1.5625 -1.453125 L -0.671875 -2.046875 C -0.210938 -2.347656 -0.0625 -2.613281 -0.21875 -2.84375 C -0.769531 -3.664062 -1.679688 -4.695312 -2.953125 -5.9375 C -3.035156 -6.03125 -3.171875 -6.101562 -3.359375 -6.15625 C -3.554688 -6.207031 -3.703125 -6.300781 -3.796875 -6.4375 C -4.046875 -6.8125 -4.035156 -7.546875 -3.765625 -8.640625 C -3.210938 -8.140625 -2.222656 -8.007812 -0.796875 -8.25 C -0.785156 -8.207031 -0.75 -7.835938 -0.6875 -7.140625 C -0.632812 -6.691406 -0.742188 -6.34375 -1.015625 -6.09375 C -0.710938 -5.769531 -0.503906 -5.519531 -0.390625 -5.34375 C -0.242188 -5.132812 -0.113281 -4.84375 0 -4.46875 C 0.125 -4.144531 0.234375 -3.828125 0.328125 -3.515625 C 0.628906 -4.203125 1.132812 -5.113281 1.84375 -6.25 C 2.6875 -7.59375 3.304688 -8.394531 3.703125 -8.65625 C 4.054688 -8.894531 4.675781 -8.945312 5.5625 -8.8125 C 6.445312 -8.675781 7.007812 -8.445312 7.25 -8.125 L 7.3125 -7.40625 L 8.046875 -7.890625 Z M 5.984375 -6.515625 C 5.722656 -6.671875 5.332031 -6.75 4.8125 -6.75 C 4.300781 -6.757812 3.910156 -6.675781 3.640625 -6.5 C 3.222656 -6.21875 2.796875 -5.757812 2.359375 -5.125 C 1.992188 -4.5625 1.710938 -4.003906 1.515625 -3.453125 Z M 5.984375 -6.515625 "/>
</g>
<g id="glyph-54-0">
<path d="M -1.296875 -9.015625 C -1.253906 -8.898438 -1.238281 -8.550781 -1.25 -7.96875 C -1.257812 -7.394531 -1.296875 -7.078125 -1.359375 -7.015625 C -1.816406 -6.898438 -2.492188 -6.6875 -3.390625 -6.375 C -3.429688 -6.789062 -3.375 -7.5 -3.21875 -8.5 C -2.738281 -8.59375 -2.097656 -8.765625 -1.296875 -9.015625 Z M 4.75 -4.09375 L 3.609375 -3.109375 C 3.203125 -2.765625 2.613281 -2.804688 1.84375 -3.234375 L 2.125 -1.828125 L -0.25 0.21875 L -1.609375 -1.359375 L 1.09375 -3.703125 C 1.039062 -3.878906 0.882812 -4.109375 0.625 -4.390625 C 0.363281 -4.671875 0.210938 -4.910156 0.171875 -5.109375 C 0.078125 -5.378906 0.046875 -5.726562 0.078125 -6.15625 C 0.128906 -6.519531 0.175781 -6.890625 0.21875 -7.265625 L 0.765625 -6.625 C 0.878906 -6.488281 1.03125 -6.253906 1.21875 -5.921875 C 1.414062 -5.597656 1.570312 -5.375 1.6875 -5.25 C 2.03125 -4.851562 2.472656 -4.890625 3.015625 -5.359375 L 3.390625 -5.671875 Z M 4.75 -4.09375 "/>
</g>
<g id="glyph-55-0">
<path d="M 2.859375 -6.1875 C 3.035156 -6.019531 3.195312 -5.640625 3.34375 -5.046875 C 3.425781 -4.597656 3.515625 -4.164062 3.609375 -3.75 C 3.492188 -3.8125 2.414062 -3.328125 0.375 -2.296875 C 0.332031 -2.171875 0.382812 -1.882812 0.53125 -1.4375 C 0.675781 -1 0.695312 -0.726562 0.59375 -0.625 L -0.296875 0.3125 L -1.796875 -1.125 L -0.765625 -2.203125 C -0.765625 -2.328125 -0.914062 -2.785156 -1.21875 -3.578125 C -1.414062 -4.054688 -1.4375 -4.738281 -1.28125 -5.625 C -1.125 -6.507812 -0.863281 -7.144531 -0.5 -7.53125 C -0.300781 -7.738281 -0.0976562 -7.796875 0.109375 -7.703125 C 0.285156 -7.585938 0.472656 -7.472656 0.671875 -7.359375 C 1.066406 -7.179688 1.453125 -7.007812 1.828125 -6.84375 C 2.285156 -6.625 2.628906 -6.40625 2.859375 -6.1875 Z M 1.0625 -5.109375 C 0.875 -5.222656 0.566406 -5.332031 0.140625 -5.4375 L 0.03125 -5.359375 C -0.144531 -5.179688 -0.273438 -4.882812 -0.359375 -4.46875 Z M 1.0625 -5.109375 "/>
</g>
<g id="glyph-56-0">
<path d="M 1.71875 1.6875 C 1.644531 1.6875 1.59375 1.679688 1.5625 1.671875 C 1.382812 1.578125 1.210938 1.398438 1.046875 1.140625 C 0.847656 0.796875 0.753906 0.398438 0.765625 -0.046875 C 0.765625 -0.472656 0.859375 -0.875 1.046875 -1.25 C 1.503906 -2.175781 2.1875 -2.671875 3.09375 -2.734375 C 3.351562 -2.742188 3.5625 -2.710938 3.71875 -2.640625 C 3.75 -2.617188 3.773438 -2.585938 3.796875 -2.546875 L 3.75 -2.453125 C 3.695312 -2.441406 3.617188 -2.445312 3.515625 -2.46875 C 3.085938 -2.519531 2.710938 -2.367188 2.390625 -2.015625 C 2.191406 -1.804688 1.96875 -1.441406 1.71875 -0.921875 C 1.457031 -0.398438 1.3125 0.0078125 1.28125 0.3125 C 1.195312 0.832031 1.335938 1.234375 1.703125 1.515625 L 1.75 1.625 Z M 1.71875 1.6875 "/>
</g>
<g id="glyph-57-0">
<path d="M 0.53125 0.234375 L 2.046875 -3.328125 L 3.0625 -2.90625 L 4.171875 0.375 L 5.171875 -2 L 6.15625 -1.578125 L 4.640625 1.984375 L 3.59375 1.53125 L 2.515625 -1.6875 L 1.515625 0.640625 Z M 0.53125 0.234375 "/>
</g>
<g id="glyph-58-0">
<path d="M 3.46875 -0.3125 L 3.6875 -0.9375 L 6.015625 -0.140625 L 5.515625 1.328125 C 5.234375 1.390625 4.859375 1.40625 4.390625 1.375 C 3.929688 1.34375 3.484375 1.253906 3.046875 1.109375 C 2.484375 0.921875 2.019531 0.675781 1.65625 0.375 C 1.289062 0.0703125 1.054688 -0.257812 0.953125 -0.625 C 0.859375 -1 0.867188 -1.363281 0.984375 -1.71875 C 1.117188 -2.101562 1.347656 -2.40625 1.671875 -2.625 C 2.003906 -2.84375 2.429688 -2.953125 2.953125 -2.953125 C 3.328125 -2.960938 3.769531 -2.878906 4.28125 -2.703125 C 4.9375 -2.484375 5.414062 -2.210938 5.71875 -1.890625 C 6.019531 -1.566406 6.171875 -1.222656 6.171875 -0.859375 L 5.0625 -1.078125 C 5.039062 -1.296875 4.941406 -1.488281 4.765625 -1.65625 C 4.597656 -1.832031 4.363281 -1.972656 4.0625 -2.078125 C 3.59375 -2.242188 3.179688 -2.269531 2.828125 -2.15625 C 2.484375 -2.039062 2.242188 -1.78125 2.109375 -1.375 C 1.960938 -0.945312 1.992188 -0.578125 2.203125 -0.265625 C 2.410156 0.046875 2.738281 0.28125 3.1875 0.4375 C 3.414062 0.519531 3.648438 0.570312 3.890625 0.59375 C 4.128906 0.613281 4.347656 0.601562 4.546875 0.5625 L 4.703125 0.109375 Z M 3.46875 -0.3125 "/>
</g>
<g id="glyph-59-0">
<path d="M 0.8125 -1.78125 C 0.90625 -2.164062 1.066406 -2.460938 1.296875 -2.671875 C 1.472656 -2.816406 1.6875 -2.941406 1.9375 -3.046875 C 2.195312 -3.160156 2.460938 -3.21875 2.734375 -3.21875 C 3.097656 -3.226562 3.503906 -3.175781 3.953125 -3.0625 C 4.753906 -2.851562 5.347656 -2.515625 5.734375 -2.046875 C 6.128906 -1.578125 6.25 -1.035156 6.09375 -0.421875 C 5.9375 0.179688 5.570312 0.59375 5 0.8125 C 4.4375 1.03125 3.757812 1.035156 2.96875 0.828125 C 2.15625 0.617188 1.550781 0.285156 1.15625 -0.171875 C 0.769531 -0.640625 0.65625 -1.175781 0.8125 -1.78125 Z M 1.953125 -1.5 C 1.835938 -1.070312 1.894531 -0.710938 2.125 -0.421875 C 2.351562 -0.140625 2.691406 0.0546875 3.140625 0.171875 C 3.566406 0.285156 3.945312 0.269531 4.28125 0.125 C 4.625 -0.0078125 4.851562 -0.296875 4.96875 -0.734375 C 5.082031 -1.171875 5.023438 -1.53125 4.796875 -1.8125 C 4.578125 -2.09375 4.242188 -2.289062 3.796875 -2.40625 C 3.335938 -2.519531 2.941406 -2.503906 2.609375 -2.359375 C 2.285156 -2.222656 2.066406 -1.9375 1.953125 -1.5 Z M 1.953125 -1.5 "/>
</g>
<g id="glyph-60-0">
</g>
<g id="glyph-61-0">
<path d="M 3.109375 -3.046875 C 3.054688 -2.992188 2.957031 -2.910156 2.8125 -2.796875 C 2.644531 -2.671875 2.546875 -2.609375 2.515625 -2.609375 C 2.441406 -2.628906 2.238281 -2.78125 1.90625 -3.0625 C 1.84375 -3.03125 1.695312 -2.945312 1.46875 -2.8125 C 1.300781 -2.882812 1.09375 -3.035156 0.84375 -3.265625 C 0.90625 -3.316406 1.097656 -3.460938 1.421875 -3.703125 C 1.703125 -3.429688 1.890625 -3.289062 1.984375 -3.28125 C 1.972656 -3.28125 2.132812 -3.359375 2.46875 -3.515625 Z M 3.96875 0.703125 L 3.125 0.546875 C 2.945312 0.515625 2.828125 0.394531 2.765625 0.1875 C 2.734375 0.0195312 2.707031 -0.148438 2.6875 -0.328125 C 2.644531 -0.316406 2.546875 -0.21875 2.390625 -0.03125 C 2.242188 0.132812 2.132812 0.207031 2.0625 0.1875 L 0.609375 -0.328125 C 0.804688 -0.753906 0.96875 -1.046875 1.09375 -1.203125 C 1.226562 -1.359375 1.414062 -1.476562 1.65625 -1.5625 C 1.96875 -1.664062 2.296875 -1.773438 2.640625 -1.890625 C 2.609375 -1.984375 2.585938 -2.066406 2.578125 -2.140625 L 3.203125 -2.84375 L 3.25 -2.8125 C 3.269531 -2.625 3.28125 -2.222656 3.28125 -1.609375 C 3.28125 -1.085938 3.304688 -0.707031 3.359375 -0.46875 C 3.378906 -0.34375 3.5 -0.257812 3.71875 -0.21875 L 4.125 -0.140625 Z M 2.671875 -0.734375 L 2.65625 -1.453125 C 2.5625 -1.359375 2.414062 -1.253906 2.21875 -1.140625 C 2.113281 -1.128906 1.988281 -1.082031 1.84375 -1 Z M 2.671875 -0.734375 "/>
</g>
<g id="glyph-62-0">
<path d="M 2.5625 0.34375 L 2.03125 0.28125 C 1.738281 0.238281 1.5625 0.046875 1.5 -0.296875 C 1.445312 -0.242188 1.320312 -0.09375 1.125 0.15625 L -0.15625 -0.015625 L -0.046875 -0.859375 L 1.421875 -0.671875 C 1.484375 -0.734375 1.523438 -0.828125 1.546875 -0.953125 C 1.492188 -0.671875 1.503906 -0.769531 1.578125 -1.25 C 1.585938 -1.34375 1.6875 -1.46875 1.875 -1.625 C 2.019531 -1.71875 2.160156 -1.820312 2.296875 -1.9375 L 2.234375 -1.515625 C 2.234375 -1.453125 2.210938 -1.359375 2.171875 -1.234375 C 2.128906 -1.109375 2.109375 -1.007812 2.109375 -0.9375 C 2.066406 -0.695312 2.175781 -0.5625 2.4375 -0.53125 L 2.671875 -0.5 Z M 2.109375 1.125 C 2.015625 1.195312 1.921875 1.28125 1.828125 1.375 C 1.671875 1.507812 1.578125 1.578125 1.546875 1.578125 C 1.492188 1.566406 1.300781 1.425781 0.96875 1.15625 C 0.875 1.1875 0.75 1.28125 0.59375 1.4375 C 0.382812 1.332031 0.179688 1.179688 -0.015625 0.984375 C 0.00390625 0.929688 0.0820312 0.859375 0.21875 0.765625 C 0.320312 0.679688 0.421875 0.597656 0.515625 0.515625 C 0.660156 0.648438 0.835938 0.789062 1.046875 0.9375 L 1.46875 0.625 Z M 2.109375 1.125 "/>
</g>
<g id="glyph-63-0">
<path d="M 2.75 -0.65625 C 2.738281 -0.550781 2.65625 -0.398438 2.5 -0.203125 C 2.382812 -0.0664062 2.265625 0.0703125 2.140625 0.21875 C 2.140625 0.164062 1.710938 -0.0625 0.859375 -0.46875 C 0.796875 -0.445312 0.710938 -0.351562 0.609375 -0.1875 C 0.503906 -0.03125 0.421875 0.0390625 0.359375 0.03125 L -0.1875 -0.015625 L -0.09375 -0.875 L 0.53125 -0.8125 C 0.570312 -0.84375 0.679688 -1.007812 0.859375 -1.3125 C 0.960938 -1.5 1.175781 -1.675781 1.5 -1.84375 C 1.832031 -2.019531 2.109375 -2.097656 2.328125 -2.078125 C 2.441406 -2.066406 2.515625 -2.019531 2.546875 -1.9375 C 2.546875 -1.84375 2.554688 -1.75 2.578125 -1.65625 C 2.617188 -1.488281 2.65625 -1.320312 2.6875 -1.15625 C 2.738281 -0.957031 2.757812 -0.789062 2.75 -0.65625 Z M 1.921875 -0.953125 C 1.910156 -1.046875 1.875 -1.175781 1.8125 -1.34375 L 1.765625 -1.34375 C 1.660156 -1.363281 1.53125 -1.335938 1.375 -1.265625 Z M 1.921875 -0.953125 "/>
</g>
<g id="glyph-64-0">
<path d="M 3.046875 0.203125 L 2.421875 0.15625 C 2.253906 0.457031 2.046875 0.722656 1.796875 0.953125 C 1.472656 1.242188 1.164062 1.378906 0.875 1.359375 C 0.644531 1.347656 0.378906 1.273438 0.078125 1.140625 C -0.234375 1.003906 -0.457031 0.859375 -0.59375 0.703125 L -0.59375 0.625 C -0.570312 0.601562 -0.546875 0.59375 -0.515625 0.59375 C -0.429688 0.601562 -0.304688 0.632812 -0.140625 0.6875 C 0.015625 0.738281 0.132812 0.765625 0.21875 0.765625 C 0.457031 0.785156 0.75 0.726562 1.09375 0.59375 C 1.382812 0.46875 1.640625 0.316406 1.859375 0.140625 C 1.410156 0.0351562 1.171875 -0.0234375 1.140625 -0.046875 C 0.859375 -0.148438 0.726562 -0.316406 0.75 -0.546875 C 0.757812 -0.796875 0.890625 -1.113281 1.140625 -1.5 C 1.398438 -1.914062 1.65625 -2.117188 1.90625 -2.109375 C 2.28125 -2.078125 2.515625 -1.601562 2.609375 -0.6875 L 3.109375 -0.65625 Z M 1.953125 -0.796875 C 1.867188 -0.984375 1.820312 -1.082031 1.8125 -1.09375 C 1.75 -1.226562 1.675781 -1.300781 1.59375 -1.3125 C 1.550781 -1.3125 1.488281 -1.273438 1.40625 -1.203125 C 1.320312 -1.140625 1.285156 -1.09375 1.296875 -1.0625 C 1.296875 -1.050781 1.296875 -1.039062 1.296875 -1.03125 C 1.378906 -0.863281 1.539062 -0.773438 1.78125 -0.765625 C 1.832031 -0.753906 1.890625 -0.765625 1.953125 -0.796875 Z M 1.953125 -0.796875 "/>
</g>
<g id="glyph-65-0">
<path d="M 3.078125 0.09375 L 2.375 0.078125 L 1.796875 -0.484375 L 1.4375 0.046875 L -0.15625 0 L -0.125 -0.859375 L 1.4375 -0.8125 L 0.15625 -2.046875 C 0.238281 -2.441406 0.363281 -2.820312 0.53125 -3.1875 C 2.144531 -3.46875 2.984375 -3.597656 3.046875 -3.578125 C 2.972656 -3.328125 2.878906 -3.054688 2.765625 -2.765625 L 0.875 -2.4375 C 1.164062 -2.125 1.453125 -1.804688 1.734375 -1.484375 C 2.140625 -1.023438 2.46875 -0.789062 2.71875 -0.78125 L 3.109375 -0.765625 Z M 3.078125 0.09375 "/>
</g>
<g id="glyph-66-0">
<path d="M 4.578125 -1.234375 L 4.03125 -0.484375 C 3.226562 -0.515625 2.800781 -0.53125 2.75 -0.53125 C 2.53125 -0.53125 2.265625 -0.445312 1.953125 -0.28125 C 1.640625 -0.113281 1.414062 -0.0234375 1.28125 -0.015625 L -0.125 0 L -0.140625 -0.859375 L 1.34375 -0.890625 C 1.53125 -0.890625 1.757812 -0.929688 2.03125 -1.015625 L 2.015625 -1.046875 C 1.503906 -1.296875 1.164062 -1.421875 1 -1.421875 C 0.875 -1.421875 0.765625 -1.394531 0.671875 -1.34375 C 0.578125 -1.300781 0.484375 -1.25 0.390625 -1.1875 C 0.765625 -1.875 1.128906 -2.222656 1.484375 -2.234375 C 1.753906 -2.234375 2.113281 -2.128906 2.5625 -1.921875 C 3.113281 -1.660156 3.460938 -1.507812 3.609375 -1.46875 C 3.816406 -1.414062 4.140625 -1.335938 4.578125 -1.234375 Z M 4.578125 -1.234375 "/>
</g>
<g id="glyph-67-0">
</g>
<g id="glyph-68-0">
<path d="M 4.765625 0.375 C 4.742188 0.4375 4.6875 0.539062 4.59375 0.6875 C 4.488281 0.863281 4.414062 0.957031 4.375 0.96875 C 4.34375 0.96875 4.117188 0.863281 3.703125 0.65625 C 3.617188 0.75 3.519531 0.878906 3.40625 1.046875 C 3.125 0.972656 2.882812 0.863281 2.6875 0.71875 L 3.109375 0.15625 C 3.453125 0.34375 3.65625 0.4375 3.71875 0.4375 C 3.738281 0.425781 3.800781 0.363281 3.90625 0.25 C 3.976562 0.1875 4.039062 0.117188 4.09375 0.046875 Z M 4.921875 -0.5 L 4.328125 -0.4375 C 4.222656 -0.425781 4.109375 -0.472656 3.984375 -0.578125 C 3.867188 -0.679688 3.800781 -0.78125 3.78125 -0.875 L 3.765625 -0.890625 L 3.515625 -0.359375 C 3.441406 -0.347656 3.296875 -0.328125 3.078125 -0.296875 C 2.867188 -0.265625 2.707031 -0.242188 2.59375 -0.234375 C 2.195312 -0.191406 1.910156 -0.203125 1.734375 -0.265625 L 1.703125 -0.25 C 1.628906 0.0507812 1.488281 0.359375 1.28125 0.671875 C 1.03125 1.054688 0.773438 1.265625 0.515625 1.296875 C 0.316406 1.304688 -0.03125 1.226562 -0.53125 1.0625 C -1.070312 0.882812 -1.335938 0.71875 -1.328125 0.5625 L -1.265625 0.546875 C -1.003906 0.734375 -0.726562 0.8125 -0.4375 0.78125 C -0.164062 0.757812 0.175781 0.578125 0.59375 0.234375 C 1.019531 -0.109375 1.222656 -0.367188 1.203125 -0.546875 C 1.203125 -0.617188 1.125 -0.734375 0.96875 -0.890625 C 0.851562 -0.992188 0.742188 -1.09375 0.640625 -1.1875 L 1.03125 -1.96875 C 1.09375 -1.925781 1.195312 -1.832031 1.34375 -1.6875 C 1.488281 -1.476562 1.609375 -1.335938 1.703125 -1.265625 C 1.878906 -1.117188 2.054688 -1.054688 2.234375 -1.078125 L 3.640625 -1.21875 C 3.648438 -1.289062 3.644531 -1.40625 3.625 -1.5625 C 3.613281 -1.71875 3.617188 -1.832031 3.640625 -1.90625 C 3.679688 -2.019531 3.757812 -2.144531 3.875 -2.28125 C 3.96875 -2.394531 4.0625 -2.507812 4.15625 -2.625 C 4.164062 -2.570312 4.175781 -2.507812 4.1875 -2.4375 C 4.195312 -2.351562 4.195312 -2.21875 4.1875 -2.03125 C 4.175781 -1.851562 4.175781 -1.71875 4.1875 -1.625 C 4.207031 -1.476562 4.289062 -1.382812 4.4375 -1.34375 C 4.507812 -1.332031 4.640625 -1.335938 4.828125 -1.359375 Z M 4.921875 -0.5 "/>
</g>
<g id="glyph-69-0">
<path d="M 1.859375 -3.578125 C 1.859375 -3.492188 1.675781 -3.3125 1.3125 -3.03125 C 1.257812 -3.03125 1.132812 -3.078125 0.9375 -3.171875 C 0.738281 -3.273438 0.640625 -3.34375 0.640625 -3.375 C 0.628906 -3.414062 0.800781 -3.625 1.15625 -4 C 1.21875 -4 1.453125 -3.859375 1.859375 -3.578125 Z M 3.671875 -1.453125 L 3.28125 -0.515625 L -0.109375 0.015625 L -0.25 -0.828125 L 0.359375 -0.921875 C 0.347656 -1.035156 0.34375 -1.191406 0.34375 -1.390625 C 0.4375 -1.660156 0.628906 -1.929688 0.921875 -2.203125 C 1.234375 -2.523438 1.515625 -2.707031 1.765625 -2.75 C 2.046875 -2.789062 2.332031 -2.707031 2.625 -2.5 C 2.925781 -2.289062 3.09375 -2.046875 3.125 -1.765625 C 2.769531 -1.992188 2.335938 -2.066406 1.828125 -1.984375 C 1.359375 -1.910156 1.066406 -1.804688 0.953125 -1.671875 C 1.242188 -1.453125 1.539062 -1.285156 1.84375 -1.171875 Z M 3.671875 -1.453125 "/>
</g>
<g id="glyph-70-0">
</g>
<g id="glyph-71-0">
<path d="M 1.671875 -4 C 1.640625 -3.9375 1.578125 -3.828125 1.484375 -3.671875 C 1.398438 -3.492188 1.34375 -3.398438 1.3125 -3.390625 C 1.238281 -3.378906 0.988281 -3.429688 0.5625 -3.546875 C 0.519531 -3.503906 0.410156 -3.378906 0.234375 -3.171875 C 0.0664062 -3.171875 -0.171875 -3.222656 -0.484375 -3.328125 C -0.460938 -3.398438 -0.34375 -3.613281 -0.125 -3.96875 C 0.238281 -3.820312 0.46875 -3.757812 0.5625 -3.78125 C 0.550781 -3.78125 0.664062 -3.921875 0.90625 -4.203125 Z M 3.921875 -0.90625 L 3.09375 -0.71875 C 2.914062 -0.675781 2.757812 -0.742188 2.625 -0.921875 C 2.53125 -1.066406 2.441406 -1.210938 2.359375 -1.359375 C 2.316406 -1.328125 2.257812 -1.191406 2.1875 -0.953125 C 2.113281 -0.742188 2.046875 -0.640625 1.984375 -0.640625 L 0.4375 -0.53125 C 0.457031 -1.007812 0.492188 -1.34375 0.546875 -1.53125 C 0.597656 -1.726562 0.722656 -1.914062 0.921875 -2.09375 C 1.160156 -2.3125 1.414062 -2.535156 1.6875 -2.765625 C 1.625 -2.835938 1.570312 -2.90625 1.53125 -2.96875 L 1.859375 -3.875 L 1.90625 -3.859375 C 1.988281 -3.691406 2.148438 -3.328125 2.390625 -2.765625 C 2.597656 -2.285156 2.769531 -1.941406 2.90625 -1.734375 C 2.976562 -1.628906 3.117188 -1.601562 3.328125 -1.65625 L 3.734375 -1.75 Z M 2.171875 -1.71875 L 1.875 -2.375 C 1.820312 -2.25 1.726562 -2.097656 1.59375 -1.921875 C 1.507812 -1.867188 1.414062 -1.773438 1.3125 -1.640625 Z M 2.171875 -1.71875 "/>
</g>
<g id="glyph-72-0">
<path d="M 3.171875 -0.890625 L 2.59375 -0.71875 C 2.570312 -0.675781 2.550781 -0.597656 2.53125 -0.484375 C 2.507812 -0.378906 2.46875 -0.304688 2.40625 -0.265625 L 0.625 -0.640625 C 0.601562 -0.617188 0.582031 -0.523438 0.5625 -0.359375 C 0.550781 -0.222656 0.492188 -0.140625 0.390625 -0.109375 L -0.171875 0.046875 L -0.40625 -0.78125 L 0.421875 -1.015625 C 0.453125 -1.054688 0.492188 -1.171875 0.546875 -1.359375 C 0.597656 -1.546875 0.640625 -1.671875 0.671875 -1.734375 C 0.773438 -1.898438 0.914062 -2.0625 1.09375 -2.21875 C 1.28125 -2.375 1.46875 -2.476562 1.65625 -2.53125 C 1.726562 -2.550781 1.78125 -2.550781 1.8125 -2.53125 C 1.851562 -2.519531 1.890625 -2.488281 1.921875 -2.4375 C 1.953125 -2.257812 2.050781 -2.078125 2.21875 -1.890625 C 2.382812 -1.679688 2.554688 -1.601562 2.734375 -1.65625 L 2.9375 -1.71875 Z M 1.703125 -1.390625 L 1.65625 -1.5625 C 1.625 -1.664062 1.535156 -1.695312 1.390625 -1.65625 C 1.265625 -1.613281 1.175781 -1.554688 1.125 -1.484375 C 1.226562 -1.472656 1.328125 -1.453125 1.421875 -1.421875 C 1.546875 -1.390625 1.640625 -1.378906 1.703125 -1.390625 Z M 1.703125 -1.390625 "/>
</g>
<g id="glyph-73-0">
<path d="M 2.296875 -3.953125 C 2.328125 -3.878906 2.171875 -3.660156 1.828125 -3.296875 C 1.773438 -3.285156 1.640625 -3.316406 1.421875 -3.390625 C 1.203125 -3.460938 1.085938 -3.515625 1.078125 -3.546875 C 1.066406 -3.585938 1.117188 -3.710938 1.234375 -3.921875 C 1.359375 -4.140625 1.441406 -4.253906 1.484375 -4.265625 C 1.523438 -4.285156 1.664062 -4.242188 1.90625 -4.140625 C 2.15625 -4.046875 2.285156 -3.984375 2.296875 -3.953125 Z M 4.375 -1.453125 L -0.1875 0.0625 L -0.453125 -0.75 L -0.046875 -0.890625 C 0.171875 -0.960938 0.257812 -1.054688 0.21875 -1.171875 C 0.09375 -1.554688 -0.148438 -2.066406 -0.515625 -2.703125 C -0.546875 -2.753906 -0.59375 -2.796875 -0.65625 -2.828125 C -0.726562 -2.867188 -0.773438 -2.925781 -0.796875 -3 C -0.859375 -3.175781 -0.769531 -3.46875 -0.53125 -3.875 C -0.375 -3.601562 0.00390625 -3.4375 0.609375 -3.375 C 0.609375 -3.363281 0.582031 -3.21875 0.53125 -2.9375 C 0.507812 -2.738281 0.425781 -2.601562 0.28125 -2.53125 C 0.351562 -2.375 0.398438 -2.257812 0.421875 -2.1875 C 0.460938 -2.070312 0.488281 -1.9375 0.5 -1.78125 C 0.507812 -1.644531 0.515625 -1.503906 0.515625 -1.359375 C 0.722656 -1.609375 1.023438 -1.914062 1.421875 -2.28125 C 1.910156 -2.726562 2.25 -2.984375 2.4375 -3.046875 C 2.613281 -3.097656 2.867188 -3.050781 3.203125 -2.90625 C 3.546875 -2.757812 3.742188 -2.601562 3.796875 -2.4375 L 3.75 -2.140625 L 4.109375 -2.265625 Z M 3.125 -1.9375 C 3.039062 -2.03125 2.894531 -2.109375 2.6875 -2.171875 C 2.476562 -2.234375 2.3125 -2.242188 2.1875 -2.203125 C 1.976562 -2.128906 1.757812 -1.988281 1.53125 -1.78125 C 1.320312 -1.601562 1.140625 -1.410156 0.984375 -1.203125 Z M 3.125 -1.9375 "/>
</g>
<g id="glyph-74-0">
<path d="M 0.75 -3.703125 C 0.769531 -3.648438 0.734375 -3.507812 0.640625 -3.28125 C 0.554688 -3.0625 0.503906 -2.941406 0.484375 -2.921875 C 0.265625 -2.941406 -0.0351562 -2.957031 -0.421875 -2.96875 C -0.378906 -3.132812 -0.253906 -3.40625 -0.046875 -3.78125 C 0.148438 -3.75 0.414062 -3.722656 0.75 -3.703125 Z M 2.4375 -0.921875 L 1.859375 -0.703125 C 1.648438 -0.628906 1.421875 -0.726562 1.171875 -1 L 1.078125 -0.40625 L -0.125 0.046875 L -0.4375 -0.75 L 0.953125 -1.28125 C 0.953125 -1.363281 0.921875 -1.476562 0.859375 -1.625 C 0.804688 -1.78125 0.78125 -1.894531 0.78125 -1.96875 C 0.800781 -2.09375 0.847656 -2.234375 0.921875 -2.390625 C 0.984375 -2.523438 1.039062 -2.660156 1.09375 -2.796875 L 1.21875 -2.46875 C 1.25 -2.40625 1.28125 -2.296875 1.3125 -2.140625 C 1.34375 -1.984375 1.375 -1.867188 1.40625 -1.796875 C 1.476562 -1.585938 1.65625 -1.539062 1.9375 -1.65625 L 2.125 -1.71875 Z M 2.4375 -0.921875 "/>
</g>
<g id="glyph-75-0">
<path d="M 2.109375 -1.875 C 2.148438 -1.78125 2.148438 -1.613281 2.109375 -1.375 C 2.054688 -1.195312 2.015625 -1.015625 1.984375 -0.828125 C 1.953125 -0.878906 1.460938 -0.875 0.515625 -0.8125 C 0.472656 -0.769531 0.445312 -0.648438 0.4375 -0.453125 C 0.425781 -0.265625 0.390625 -0.160156 0.328125 -0.140625 L -0.171875 0.078125 L -0.5 -0.71875 L 0.078125 -0.96875 C 0.109375 -1 0.125 -1.195312 0.125 -1.5625 C 0.125 -1.78125 0.226562 -2.039062 0.4375 -2.34375 C 0.644531 -2.65625 0.847656 -2.851562 1.046875 -2.9375 C 1.160156 -2.988281 1.25 -2.984375 1.3125 -2.921875 C 1.351562 -2.835938 1.398438 -2.757812 1.453125 -2.6875 C 1.585938 -2.570312 1.707031 -2.441406 1.8125 -2.296875 C 1.957031 -2.140625 2.054688 -2 2.109375 -1.875 Z M 1.25 -1.75 C 1.1875 -1.84375 1.085938 -1.941406 0.953125 -2.046875 L 0.90625 -2.03125 C 0.800781 -1.988281 0.703125 -1.898438 0.609375 -1.765625 Z M 1.25 -1.75 "/>
</g>
<g id="glyph-76-0">
<path d="M 1.640625 -2.453125 C 1.828125 -2.046875 1.921875 -1.625 1.921875 -1.1875 C 1.898438 -0.707031 1.757812 -0.289062 1.5 0.0625 C 1.351562 0.269531 1.203125 0.410156 1.046875 0.484375 C 0.960938 0.515625 0.90625 0.5 0.875 0.4375 C 0.863281 0.414062 0.878906 0.378906 0.921875 0.328125 C 1.035156 0.210938 1.113281 0.128906 1.15625 0.078125 C 1.226562 -0.015625 1.28125 -0.109375 1.3125 -0.203125 C 1.445312 -0.671875 1.359375 -1.238281 1.046875 -1.90625 L 0.796875 -2.453125 C 0.660156 -2.753906 0.488281 -3.015625 0.28125 -3.234375 C 0.0195312 -3.523438 -0.25 -3.675781 -0.53125 -3.6875 C -0.625 -3.6875 -0.722656 -3.6875 -0.828125 -3.6875 C -0.972656 -3.675781 -1.054688 -3.695312 -1.078125 -3.75 C -1.109375 -3.8125 -1.085938 -3.851562 -1.015625 -3.875 C -0.828125 -3.96875 -0.582031 -3.992188 -0.28125 -3.953125 C 0.125 -3.898438 0.507812 -3.726562 0.875 -3.4375 C 1.207031 -3.15625 1.460938 -2.828125 1.640625 -2.453125 Z M 1.640625 -2.453125 "/>
</g>
</g>
<clipPath id="clip-0">
<path clip-rule="nonzero" d="M 0 23 L 185.84375 23 L 185.84375 212.140625 L 0 212.140625 Z M 0 23 "/>
</clipPath>
<clipPath id="clip-1">
<path clip-rule="nonzero" d="M 136 139 L 167 139 L 167 164 L 136 164 Z M 136 139 "/>
</clipPath>
<clipPath id="clip-2">
<path clip-rule="nonzero" d="M 136.820312 163.433594 C 136.820312 163.433594 155.730469 165.804688 165.691406 158.621094 C 171.332031 154.550781 147.121094 139.710938 147.121094 139.710938 Z M 136.820312 163.433594 "/>
</clipPath>
<radialGradient id="radial-pattern-0" gradientUnits="userSpaceOnUse" cx="0" cy="0" fx="0" fy="0" r="2074.416016" gradientTransform="matrix(1, 0, 0, 1, 151.682282, 151.760242)">
<stop offset="0" stop-color="rgb(100%, 100%, 100%)" stop-opacity="1"/>
<stop offset="0.125" stop-color="rgb(100%, 100%, 100%)" stop-opacity="1"/>
<stop offset="0.179688" stop-color="rgb(100%, 100%, 100%)" stop-opacity="1"/>
<stop offset="0.203125" stop-color="rgb(99.607849%, 99.607849%, 100%)" stop-opacity="1"/>
<stop offset="0.207031" stop-color="rgb(98.823547%, 98.823547%, 99.607849%)" stop-opacity="1"/>
<stop offset="0.210938" stop-color="rgb(98.039246%, 98.039246%, 99.215698%)" stop-opacity="1"/>
<stop offset="0.214844" stop-color="rgb(97.254944%, 97.254944%, 98.823547%)" stop-opacity="1"/>
<stop offset="0.21875" stop-color="rgb(96.470642%, 96.470642%, 98.431396%)" stop-opacity="1"/>
<stop offset="0.222656" stop-color="rgb(95.68634%, 96.078491%, 98.431396%)" stop-opacity="1"/>
<stop offset="0.226562" stop-color="rgb(94.902039%, 95.294189%, 98.039246%)" stop-opacity="1"/>
<stop offset="0.230469" stop-color="rgb(94.509888%, 94.902039%, 97.647095%)" stop-opacity="1"/>
<stop offset="0.234375" stop-color="rgb(93.725586%, 94.117737%, 97.647095%)" stop-opacity="1"/>
<stop offset="0.238281" stop-color="rgb(93.333435%, 93.725586%, 97.254944%)" stop-opacity="1"/>
<stop offset="0.242188" stop-color="rgb(92.941284%, 93.333435%, 97.254944%)" stop-opacity="1"/>
<stop offset="0.246094" stop-color="rgb(92.156982%, 92.941284%, 96.862793%)" stop-opacity="1"/>
<stop offset="0.25" stop-color="rgb(91.764832%, 92.549133%, 96.470642%)" stop-opacity="1"/>
<stop offset="0.253906" stop-color="rgb(90.98053%, 91.764832%, 96.470642%)" stop-opacity="1"/>
<stop offset="0.257812" stop-color="rgb(90.588379%, 91.372681%, 96.078491%)" stop-opacity="1"/>
<stop offset="0.261719" stop-color="rgb(90.196228%, 90.98053%, 95.68634%)" stop-opacity="1"/>
<stop offset="0.265625" stop-color="rgb(89.411926%, 90.588379%, 95.68634%)" stop-opacity="1"/>
<stop offset="0.269531" stop-color="rgb(89.019775%, 89.804077%, 95.294189%)" stop-opacity="1"/>
<stop offset="0.273438" stop-color="rgb(88.235474%, 89.411926%, 95.294189%)" stop-opacity="1"/>
<stop offset="0.277344" stop-color="rgb(87.843323%, 89.019775%, 94.902039%)" stop-opacity="1"/>
<stop offset="0.28125" stop-color="rgb(87.451172%, 88.627625%, 94.509888%)" stop-opacity="1"/>
<stop offset="0.285156" stop-color="rgb(86.66687%, 88.235474%, 94.509888%)" stop-opacity="1"/>
<stop offset="0.289062" stop-color="rgb(86.274719%, 87.843323%, 94.117737%)" stop-opacity="1"/>
<stop offset="0.292969" stop-color="rgb(85.882568%, 87.451172%, 93.725586%)" stop-opacity="1"/>
<stop offset="0.296875" stop-color="rgb(85.098267%, 87.059021%, 93.725586%)" stop-opacity="1"/>
<stop offset="0.300781" stop-color="rgb(84.706116%, 86.274719%, 93.333435%)" stop-opacity="1"/>
<stop offset="0.304688" stop-color="rgb(83.921814%, 85.882568%, 93.333435%)" stop-opacity="1"/>
<stop offset="0.308594" stop-color="rgb(83.529663%, 85.490417%, 92.941284%)" stop-opacity="1"/>
<stop offset="0.3125" stop-color="rgb(83.137512%, 85.098267%, 92.549133%)" stop-opacity="1"/>
<stop offset="0.316406" stop-color="rgb(82.745361%, 84.706116%, 92.549133%)" stop-opacity="1"/>
<stop offset="0.320312" stop-color="rgb(81.96106%, 84.313965%, 92.549133%)" stop-opacity="1"/>
<stop offset="0.324219" stop-color="rgb(81.568909%, 83.921814%, 92.156982%)" stop-opacity="1"/>
<stop offset="0.328125" stop-color="rgb(81.176758%, 83.529663%, 92.156982%)" stop-opacity="1"/>
<stop offset="0.332031" stop-color="rgb(80.784607%, 83.137512%, 91.764832%)" stop-opacity="1"/>
<stop offset="0.335938" stop-color="rgb(80.000305%, 82.35321%, 91.764832%)" stop-opacity="1"/>
<stop offset="0.339844" stop-color="rgb(79.608154%, 81.96106%, 91.372681%)" stop-opacity="1"/>
<stop offset="0.34375" stop-color="rgb(79.216003%, 81.568909%, 90.98053%)" stop-opacity="1"/>
<stop offset="0.347656" stop-color="rgb(78.823853%, 81.176758%, 90.98053%)" stop-opacity="1"/>
<stop offset="0.351562" stop-color="rgb(78.039551%, 80.784607%, 90.588379%)" stop-opacity="1"/>
<stop offset="0.355469" stop-color="rgb(77.6474%, 80.392456%, 90.588379%)" stop-opacity="1"/>
<stop offset="0.359375" stop-color="rgb(76.863098%, 80.000305%, 90.196228%)" stop-opacity="1"/>
<stop offset="0.363281" stop-color="rgb(76.470947%, 79.608154%, 89.804077%)" stop-opacity="1"/>
<stop offset="0.367188" stop-color="rgb(76.078796%, 79.216003%, 89.804077%)" stop-opacity="1"/>
<stop offset="0.371094" stop-color="rgb(75.686646%, 78.823853%, 89.804077%)" stop-opacity="1"/>
<stop offset="0.375" stop-color="rgb(75.294495%, 78.431702%, 89.411926%)" stop-opacity="1"/>
<stop offset="0.378906" stop-color="rgb(74.902344%, 78.039551%, 89.411926%)" stop-opacity="1"/>
<stop offset="0.382812" stop-color="rgb(74.510193%, 77.6474%, 89.019775%)" stop-opacity="1"/>
<stop offset="0.386719" stop-color="rgb(73.725891%, 77.255249%, 89.019775%)" stop-opacity="1"/>
<stop offset="0.390625" stop-color="rgb(73.33374%, 76.863098%, 88.627625%)" stop-opacity="1"/>
<stop offset="0.394531" stop-color="rgb(72.941589%, 76.470947%, 88.235474%)" stop-opacity="1"/>
<stop offset="0.398438" stop-color="rgb(72.549438%, 76.078796%, 88.235474%)" stop-opacity="1"/>
<stop offset="0.402344" stop-color="rgb(71.765137%, 75.686646%, 87.843323%)" stop-opacity="1"/>
<stop offset="0.40625" stop-color="rgb(71.372986%, 75.294495%, 87.843323%)" stop-opacity="1"/>
<stop offset="0.410156" stop-color="rgb(70.980835%, 74.902344%, 87.451172%)" stop-opacity="1"/>
<stop offset="0.414062" stop-color="rgb(70.588684%, 74.510193%, 87.059021%)" stop-opacity="1"/>
<stop offset="0.417969" stop-color="rgb(69.804382%, 74.118042%, 87.059021%)" stop-opacity="1"/>
<stop offset="0.421875" stop-color="rgb(69.412231%, 73.725891%, 87.059021%)" stop-opacity="1"/>
<stop offset="0.425781" stop-color="rgb(69.020081%, 73.33374%, 86.66687%)" stop-opacity="1"/>
<stop offset="0.429688" stop-color="rgb(68.62793%, 72.941589%, 86.66687%)" stop-opacity="1"/>
<stop offset="0.433594" stop-color="rgb(67.843628%, 72.549438%, 86.274719%)" stop-opacity="1"/>
<stop offset="0.4375" stop-color="rgb(67.451477%, 72.157288%, 86.274719%)" stop-opacity="1"/>
<stop offset="0.441406" stop-color="rgb(67.059326%, 71.765137%, 85.882568%)" stop-opacity="1"/>
<stop offset="0.445312" stop-color="rgb(66.667175%, 71.372986%, 85.490417%)" stop-opacity="1"/>
<stop offset="0.449219" stop-color="rgb(66.275024%, 70.980835%, 85.490417%)" stop-opacity="1"/>
<stop offset="0.453125" stop-color="rgb(65.490723%, 70.588684%, 85.098267%)" stop-opacity="1"/>
<stop offset="0.457031" stop-color="rgb(65.098572%, 70.196533%, 85.098267%)" stop-opacity="1"/>
<stop offset="0.460938" stop-color="rgb(64.706421%, 69.804382%, 84.706116%)" stop-opacity="1"/>
<stop offset="0.464844" stop-color="rgb(64.31427%, 69.412231%, 84.706116%)" stop-opacity="1"/>
<stop offset="0.46875" stop-color="rgb(63.922119%, 69.412231%, 84.706116%)" stop-opacity="1"/>
<stop offset="0.472656" stop-color="rgb(63.529968%, 69.020081%, 84.313965%)" stop-opacity="1"/>
<stop offset="0.476562" stop-color="rgb(63.137817%, 68.62793%, 83.921814%)" stop-opacity="1"/>
<stop offset="0.480469" stop-color="rgb(62.745667%, 68.235779%, 83.921814%)" stop-opacity="1"/>
<stop offset="0.484375" stop-color="rgb(61.961365%, 67.843628%, 83.921814%)" stop-opacity="1"/>
<stop offset="0.488281" stop-color="rgb(61.569214%, 67.451477%, 83.529663%)" stop-opacity="1"/>
<stop offset="0.492188" stop-color="rgb(61.177063%, 67.059326%, 83.529663%)" stop-opacity="1"/>
<stop offset="0.496094" stop-color="rgb(60.784912%, 66.667175%, 83.137512%)" stop-opacity="1"/>
<stop offset="0.5" stop-color="rgb(60.00061%, 66.275024%, 83.137512%)" stop-opacity="1"/>
<stop offset="0.503906" stop-color="rgb(59.608459%, 65.882874%, 82.745361%)" stop-opacity="1"/>
<stop offset="0.507812" stop-color="rgb(59.216309%, 65.490723%, 82.745361%)" stop-opacity="1"/>
<stop offset="0.511719" stop-color="rgb(58.824158%, 65.098572%, 82.35321%)" stop-opacity="1"/>
<stop offset="0.515625" stop-color="rgb(58.432007%, 64.706421%, 81.96106%)" stop-opacity="1"/>
<stop offset="0.519531" stop-color="rgb(58.039856%, 64.31427%, 81.96106%)" stop-opacity="1"/>
<stop offset="0.523438" stop-color="rgb(57.647705%, 64.31427%, 81.96106%)" stop-opacity="1"/>
<stop offset="0.527344" stop-color="rgb(57.255554%, 63.922119%, 81.568909%)" stop-opacity="1"/>
<stop offset="0.53125" stop-color="rgb(56.863403%, 63.529968%, 81.568909%)" stop-opacity="1"/>
<stop offset="0.535156" stop-color="rgb(56.471252%, 63.137817%, 81.176758%)" stop-opacity="1"/>
<stop offset="0.539062" stop-color="rgb(56.079102%, 63.137817%, 81.176758%)" stop-opacity="1"/>
<stop offset="0.542969" stop-color="rgb(55.686951%, 62.745667%, 80.784607%)" stop-opacity="1"/>
<stop offset="0.546875" stop-color="rgb(55.2948%, 62.353516%, 80.784607%)" stop-opacity="1"/>
<stop offset="0.550781" stop-color="rgb(54.510498%, 61.961365%, 80.392456%)" stop-opacity="1"/>
<stop offset="0.554688" stop-color="rgb(54.118347%, 61.569214%, 80.392456%)" stop-opacity="1"/>
<stop offset="0.558594" stop-color="rgb(53.726196%, 61.177063%, 80.000305%)" stop-opacity="1"/>
<stop offset="0.5625" stop-color="rgb(53.334045%, 60.784912%, 80.000305%)" stop-opacity="1"/>
<stop offset="0.566406" stop-color="rgb(52.941895%, 60.392761%, 79.608154%)" stop-opacity="1"/>
<stop offset="0.570312" stop-color="rgb(52.549744%, 60.392761%, 79.608154%)" stop-opacity="1"/>
<stop offset="0.574219" stop-color="rgb(52.157593%, 60.00061%, 79.608154%)" stop-opacity="1"/>
<stop offset="0.578125" stop-color="rgb(51.765442%, 59.608459%, 79.216003%)" stop-opacity="1"/>
<stop offset="0.582031" stop-color="rgb(51.373291%, 59.608459%, 79.216003%)" stop-opacity="1"/>
<stop offset="0.585938" stop-color="rgb(50.98114%, 59.216309%, 78.823853%)" stop-opacity="1"/>
<stop offset="0.589844" stop-color="rgb(50.588989%, 58.824158%, 78.823853%)" stop-opacity="1"/>
<stop offset="0.59375" stop-color="rgb(50.196838%, 58.432007%, 78.431702%)" stop-opacity="1"/>
<stop offset="0.597656" stop-color="rgb(49.803162%, 58.039856%, 78.431702%)" stop-opacity="1"/>
<stop offset="0.601562" stop-color="rgb(49.411011%, 57.647705%, 78.039551%)" stop-opacity="1"/>
<stop offset="0.605469" stop-color="rgb(49.01886%, 57.255554%, 78.039551%)" stop-opacity="1"/>
<stop offset="0.609375" stop-color="rgb(48.626709%, 56.863403%, 77.6474%)" stop-opacity="1"/>
<stop offset="0.613281" stop-color="rgb(48.234558%, 56.863403%, 77.6474%)" stop-opacity="1"/>
<stop offset="0.617188" stop-color="rgb(47.450256%, 56.471252%, 77.255249%)" stop-opacity="1"/>
<stop offset="0.621094" stop-color="rgb(47.058105%, 56.079102%, 77.255249%)" stop-opacity="1"/>
<stop offset="0.625" stop-color="rgb(46.665955%, 55.686951%, 76.863098%)" stop-opacity="1"/>
<stop offset="0.628906" stop-color="rgb(46.665955%, 55.686951%, 76.863098%)" stop-opacity="1"/>
<stop offset="0.632812" stop-color="rgb(46.273804%, 55.2948%, 76.863098%)" stop-opacity="1"/>
<stop offset="0.636719" stop-color="rgb(45.881653%, 54.902649%, 76.470947%)" stop-opacity="1"/>
<stop offset="0.640625" stop-color="rgb(45.489502%, 54.902649%, 76.470947%)" stop-opacity="1"/>
<stop offset="0.644531" stop-color="rgb(45.097351%, 54.510498%, 76.078796%)" stop-opacity="1"/>
<stop offset="0.648438" stop-color="rgb(44.7052%, 54.118347%, 76.078796%)" stop-opacity="1"/>
<stop offset="0.652344" stop-color="rgb(44.313049%, 54.118347%, 75.686646%)" stop-opacity="1"/>
<stop offset="0.65625" stop-color="rgb(43.920898%, 53.726196%, 75.686646%)" stop-opacity="1"/>
<stop offset="0.660156" stop-color="rgb(43.528748%, 53.334045%, 75.686646%)" stop-opacity="1"/>
<stop offset="0.664062" stop-color="rgb(43.136597%, 52.941895%, 75.294495%)" stop-opacity="1"/>
<stop offset="0.667969" stop-color="rgb(42.352295%, 52.549744%, 75.294495%)" stop-opacity="1"/>
<stop offset="0.671875" stop-color="rgb(41.960144%, 52.549744%, 75.294495%)" stop-opacity="1"/>
<stop offset="0.675781" stop-color="rgb(41.567993%, 52.157593%, 74.902344%)" stop-opacity="1"/>
<stop offset="0.679688" stop-color="rgb(41.567993%, 51.765442%, 74.510193%)" stop-opacity="1"/>
<stop offset="0.683594" stop-color="rgb(41.175842%, 51.765442%, 74.510193%)" stop-opacity="1"/>
<stop offset="0.6875" stop-color="rgb(40.783691%, 51.373291%, 74.510193%)" stop-opacity="1"/>
<stop offset="0.691406" stop-color="rgb(40.391541%, 50.98114%, 74.118042%)" stop-opacity="1"/>
<stop offset="0.695312" stop-color="rgb(39.99939%, 50.98114%, 74.118042%)" stop-opacity="1"/>
<stop offset="0.699219" stop-color="rgb(39.215088%, 50.588989%, 74.118042%)" stop-opacity="1"/>
<stop offset="0.703125" stop-color="rgb(38.822937%, 50.588989%, 73.725891%)" stop-opacity="1"/>
<stop offset="0.707031" stop-color="rgb(38.430786%, 50.196838%, 73.725891%)" stop-opacity="1"/>
<stop offset="0.710938" stop-color="rgb(38.038635%, 49.803162%, 73.33374%)" stop-opacity="1"/>
<stop offset="0.714844" stop-color="rgb(37.646484%, 49.411011%, 73.33374%)" stop-opacity="1"/>
<stop offset="0.71875" stop-color="rgb(37.254333%, 49.01886%, 72.941589%)" stop-opacity="1"/>
<stop offset="0.722656" stop-color="rgb(36.862183%, 49.01886%, 72.941589%)" stop-opacity="1"/>
<stop offset="0.726562" stop-color="rgb(36.470032%, 48.626709%, 72.549438%)" stop-opacity="1"/>
<stop offset="0.730469" stop-color="rgb(36.077881%, 48.234558%, 72.549438%)" stop-opacity="1"/>
<stop offset="0.734375" stop-color="rgb(35.68573%, 48.234558%, 72.549438%)" stop-opacity="1"/>
<stop offset="0.738281" stop-color="rgb(35.293579%, 47.842407%, 72.549438%)" stop-opacity="1"/>
<stop offset="0.742188" stop-color="rgb(34.901428%, 47.450256%, 72.157288%)" stop-opacity="1"/>
<stop offset="0.746094" stop-color="rgb(34.509277%, 47.450256%, 72.157288%)" stop-opacity="1"/>
<stop offset="0.75" stop-color="rgb(34.117126%, 47.058105%, 72.157288%)" stop-opacity="1"/>
<stop offset="0.753906" stop-color="rgb(33.724976%, 47.058105%, 71.765137%)" stop-opacity="1"/>
<stop offset="0.757812" stop-color="rgb(33.332825%, 46.665955%, 71.765137%)" stop-opacity="1"/>
<stop offset="0.761719" stop-color="rgb(32.940674%, 46.273804%, 71.372986%)" stop-opacity="1"/>
<stop offset="0.765625" stop-color="rgb(32.156372%, 46.273804%, 71.372986%)" stop-opacity="1"/>
<stop offset="0.769531" stop-color="rgb(31.764221%, 45.881653%, 70.980835%)" stop-opacity="1"/>
<stop offset="0.773438" stop-color="rgb(31.37207%, 45.881653%, 70.980835%)" stop-opacity="1"/>
<stop offset="0.777344" stop-color="rgb(30.979919%, 45.489502%, 70.588684%)" stop-opacity="1"/>
<stop offset="0.78125" stop-color="rgb(30.587769%, 45.097351%, 70.588684%)" stop-opacity="1"/>
<stop offset="0.785156" stop-color="rgb(29.803467%, 45.097351%, 70.588684%)" stop-opacity="1"/>
<stop offset="0.789062" stop-color="rgb(29.411316%, 44.7052%, 70.588684%)" stop-opacity="1"/>
<stop offset="0.792969" stop-color="rgb(29.019165%, 44.313049%, 70.196533%)" stop-opacity="1"/>
<stop offset="0.796875" stop-color="rgb(28.627014%, 44.313049%, 70.196533%)" stop-opacity="1"/>
<stop offset="0.800781" stop-color="rgb(28.234863%, 43.920898%, 70.196533%)" stop-opacity="1"/>
<stop offset="0.804688" stop-color="rgb(27.842712%, 43.920898%, 69.804382%)" stop-opacity="1"/>
<stop offset="0.808594" stop-color="rgb(27.450562%, 43.528748%, 69.804382%)" stop-opacity="1"/>
<stop offset="0.8125" stop-color="rgb(27.058411%, 43.136597%, 69.804382%)" stop-opacity="1"/>
<stop offset="0.816406" stop-color="rgb(26.274109%, 43.136597%, 69.412231%)" stop-opacity="1"/>
<stop offset="0.820312" stop-color="rgb(25.881958%, 42.744446%, 69.412231%)" stop-opacity="1"/>
<stop offset="0.824219" stop-color="rgb(25.489807%, 42.744446%, 69.412231%)" stop-opacity="1"/>
<stop offset="0.828125" stop-color="rgb(24.705505%, 42.352295%, 69.020081%)" stop-opacity="1"/>
<stop offset="0.832031" stop-color="rgb(24.313354%, 42.352295%, 69.020081%)" stop-opacity="1"/>
<stop offset="0.835938" stop-color="rgb(23.529053%, 42.352295%, 69.020081%)" stop-opacity="1"/>
<stop offset="0.839844" stop-color="rgb(23.136902%, 41.960144%, 69.020081%)" stop-opacity="1"/>
<stop offset="0.84375" stop-color="rgb(22.744751%, 41.567993%, 68.62793%)" stop-opacity="1"/>
<stop offset="0.847656" stop-color="rgb(22.3526%, 41.567993%, 68.62793%)" stop-opacity="1"/>
<stop offset="0.851562" stop-color="rgb(21.568298%, 41.175842%, 68.62793%)" stop-opacity="1"/>
<stop offset="0.855469" stop-color="rgb(21.176147%, 41.175842%, 68.235779%)" stop-opacity="1"/>
<stop offset="0.859375" stop-color="rgb(20.783997%, 40.783691%, 68.235779%)" stop-opacity="1"/>
<stop offset="0.863281" stop-color="rgb(19.999695%, 40.391541%, 67.843628%)" stop-opacity="1"/>
<stop offset="0.867188" stop-color="rgb(19.607544%, 40.391541%, 67.843628%)" stop-opacity="1"/>
<stop offset="0.871094" stop-color="rgb(18.823242%, 39.99939%, 67.451477%)" stop-opacity="1"/>
<stop offset="0.875" stop-color="rgb(18.431091%, 39.607239%, 67.451477%)" stop-opacity="1"/>
<stop offset="0.878906" stop-color="rgb(17.64679%, 39.607239%, 67.059326%)" stop-opacity="1"/>
<stop offset="0.882812" stop-color="rgb(17.254639%, 39.215088%, 67.059326%)" stop-opacity="1"/>
<stop offset="0.886719" stop-color="rgb(16.470337%, 39.215088%, 67.059326%)" stop-opacity="1"/>
<stop offset="0.890625" stop-color="rgb(15.686035%, 38.822937%, 66.667175%)" stop-opacity="1"/>
<stop offset="0.894531" stop-color="rgb(14.901733%, 38.822937%, 66.667175%)" stop-opacity="1"/>
<stop offset="0.898438" stop-color="rgb(14.117432%, 38.822937%, 66.667175%)" stop-opacity="1"/>
<stop offset="0.902344" stop-color="rgb(13.33313%, 38.430786%, 66.667175%)" stop-opacity="1"/>
<stop offset="0.90625" stop-color="rgb(12.940979%, 38.430786%, 66.275024%)" stop-opacity="1"/>
<stop offset="0.910156" stop-color="rgb(12.156677%, 38.038635%, 66.275024%)" stop-opacity="1"/>
<stop offset="0.914062" stop-color="rgb(10.980225%, 37.646484%, 66.275024%)" stop-opacity="1"/>
<stop offset="0.917969" stop-color="rgb(10.195923%, 37.646484%, 65.882874%)" stop-opacity="1"/>
<stop offset="0.921875" stop-color="rgb(9.411621%, 37.254333%, 65.882874%)" stop-opacity="1"/>
<stop offset="0.925781" stop-color="rgb(8.235168%, 37.254333%, 65.882874%)" stop-opacity="1"/>
<stop offset="0.929688" stop-color="rgb(7.058716%, 36.862183%, 65.490723%)" stop-opacity="1"/>
<stop offset="0.933594" stop-color="rgb(5.882263%, 36.862183%, 65.490723%)" stop-opacity="1"/>
<stop offset="0.9375" stop-color="rgb(4.31366%, 36.862183%, 65.490723%)" stop-opacity="1"/>
<stop offset="0.941406" stop-color="rgb(2.745056%, 36.470032%, 65.098572%)" stop-opacity="1"/>
<stop offset="0.945312" stop-color="rgb(0.784302%, 36.077881%, 65.098572%)" stop-opacity="1"/>
<stop offset="0.949219" stop-color="rgb(0%, 36.077881%, 65.098572%)" stop-opacity="1"/>
<stop offset="0.960938" stop-color="rgb(0%, 35.68573%, 64.706421%)" stop-opacity="1"/>
<stop offset="0.964844" stop-color="rgb(0%, 35.293579%, 64.706421%)" stop-opacity="1"/>
<stop offset="0.96875" stop-color="rgb(0%, 35.293579%, 64.31427%)" stop-opacity="1"/>
<stop offset="0.972656" stop-color="rgb(0%, 34.901428%, 64.31427%)" stop-opacity="1"/>
<stop offset="0.976562" stop-color="rgb(0%, 34.509277%, 64.31427%)" stop-opacity="1"/>
<stop offset="0.980469" stop-color="rgb(0%, 34.509277%, 63.922119%)" stop-opacity="1"/>
<stop offset="0.984375" stop-color="rgb(0%, 34.117126%, 63.922119%)" stop-opacity="1"/>
<stop offset="0.992188" stop-color="rgb(0%, 33.724976%, 63.529968%)" stop-opacity="1"/>
<stop offset="1" stop-color="rgb(0%, 33.724976%, 63.529968%)" stop-opacity="1"/>
</radialGradient>
<clipPath id="clip-3">
<path clip-rule="nonzero" d="M 12 114 L 43 114 L 43 139 L 12 139 Z M 12 114 "/>
</clipPath>
<clipPath id="clip-4">
<path clip-rule="nonzero" d="M 13.226562 133.046875 C 23.1875 140.226562 42.097656 137.859375 42.097656 137.859375 L 31.796875 114.140625 C 31.796875 114.140625 7.585938 128.980469 13.226562 133.046875 "/>
</clipPath>
<radialGradient id="radial-pattern-1" gradientUnits="userSpaceOnUse" cx="0" cy="0" fx="0" fy="0" r="1891.967285" gradientTransform="matrix(1, 0, 0, 1, 27.23509, 126.186775)">
<stop offset="0" stop-color="rgb(100%, 100%, 100%)" stop-opacity="1"/>
<stop offset="0.25" stop-color="rgb(100%, 100%, 100%)" stop-opacity="1"/>
<stop offset="0.273438" stop-color="rgb(99.607849%, 99.607849%, 99.607849%)" stop-opacity="1"/>
<stop offset="0.277344" stop-color="rgb(98.431396%, 98.431396%, 99.215698%)" stop-opacity="1"/>
<stop offset="0.28125" stop-color="rgb(97.647095%, 97.647095%, 98.823547%)" stop-opacity="1"/>
<stop offset="0.285156" stop-color="rgb(96.862793%, 96.862793%, 98.823547%)" stop-opacity="1"/>
<stop offset="0.289062" stop-color="rgb(96.078491%, 96.078491%, 98.431396%)" stop-opacity="1"/>
<stop offset="0.292969" stop-color="rgb(95.294189%, 95.68634%, 98.039246%)" stop-opacity="1"/>
<stop offset="0.296875" stop-color="rgb(94.509888%, 94.902039%, 98.039246%)" stop-opacity="1"/>
<stop offset="0.300781" stop-color="rgb(94.117737%, 94.509888%, 97.647095%)" stop-opacity="1"/>
<stop offset="0.304688" stop-color="rgb(93.333435%, 93.725586%, 97.254944%)" stop-opacity="1"/>
<stop offset="0.308594" stop-color="rgb(92.941284%, 93.333435%, 97.254944%)" stop-opacity="1"/>
<stop offset="0.3125" stop-color="rgb(92.156982%, 92.941284%, 96.862793%)" stop-opacity="1"/>
<stop offset="0.316406" stop-color="rgb(91.764832%, 92.156982%, 96.470642%)" stop-opacity="1"/>
<stop offset="0.320312" stop-color="rgb(90.98053%, 91.764832%, 96.470642%)" stop-opacity="1"/>
<stop offset="0.324219" stop-color="rgb(90.588379%, 91.372681%, 96.078491%)" stop-opacity="1"/>
<stop offset="0.328125" stop-color="rgb(89.804077%, 90.588379%, 95.68634%)" stop-opacity="1"/>
<stop offset="0.332031" stop-color="rgb(89.411926%, 90.196228%, 95.294189%)" stop-opacity="1"/>
<stop offset="0.335938" stop-color="rgb(88.627625%, 89.804077%, 95.294189%)" stop-opacity="1"/>
<stop offset="0.339844" stop-color="rgb(87.843323%, 89.019775%, 94.902039%)" stop-opacity="1"/>
<stop offset="0.34375" stop-color="rgb(87.451172%, 88.627625%, 94.509888%)" stop-opacity="1"/>
<stop offset="0.347656" stop-color="rgb(87.059021%, 88.235474%, 94.509888%)" stop-opacity="1"/>
<stop offset="0.351562" stop-color="rgb(86.274719%, 87.843323%, 94.117737%)" stop-opacity="1"/>
<stop offset="0.355469" stop-color="rgb(85.882568%, 87.451172%, 93.725586%)" stop-opacity="1"/>
<stop offset="0.359375" stop-color="rgb(85.098267%, 87.059021%, 93.725586%)" stop-opacity="1"/>
<stop offset="0.363281" stop-color="rgb(84.313965%, 86.274719%, 93.333435%)" stop-opacity="1"/>
<stop offset="0.367188" stop-color="rgb(83.921814%, 85.882568%, 92.941284%)" stop-opacity="1"/>
<stop offset="0.371094" stop-color="rgb(83.137512%, 85.098267%, 92.941284%)" stop-opacity="1"/>
<stop offset="0.375" stop-color="rgb(82.745361%, 84.706116%, 92.549133%)" stop-opacity="1"/>
<stop offset="0.378906" stop-color="rgb(82.35321%, 84.313965%, 92.549133%)" stop-opacity="1"/>
<stop offset="0.382812" stop-color="rgb(81.568909%, 83.921814%, 92.156982%)" stop-opacity="1"/>
<stop offset="0.386719" stop-color="rgb(81.176758%, 83.529663%, 92.156982%)" stop-opacity="1"/>
<stop offset="0.390625" stop-color="rgb(80.784607%, 83.137512%, 91.764832%)" stop-opacity="1"/>
<stop offset="0.394531" stop-color="rgb(80.000305%, 82.35321%, 91.764832%)" stop-opacity="1"/>
<stop offset="0.398438" stop-color="rgb(79.608154%, 81.96106%, 91.372681%)" stop-opacity="1"/>
<stop offset="0.402344" stop-color="rgb(79.216003%, 81.568909%, 90.98053%)" stop-opacity="1"/>
<stop offset="0.40625" stop-color="rgb(78.431702%, 81.176758%, 90.98053%)" stop-opacity="1"/>
<stop offset="0.410156" stop-color="rgb(78.039551%, 80.784607%, 90.588379%)" stop-opacity="1"/>
<stop offset="0.414062" stop-color="rgb(77.255249%, 80.392456%, 90.196228%)" stop-opacity="1"/>
<stop offset="0.417969" stop-color="rgb(76.470947%, 79.608154%, 90.196228%)" stop-opacity="1"/>
<stop offset="0.421875" stop-color="rgb(76.078796%, 79.216003%, 89.804077%)" stop-opacity="1"/>
<stop offset="0.425781" stop-color="rgb(75.686646%, 78.823853%, 89.804077%)" stop-opacity="1"/>
<stop offset="0.429688" stop-color="rgb(75.294495%, 78.431702%, 89.411926%)" stop-opacity="1"/>
<stop offset="0.433594" stop-color="rgb(74.902344%, 78.039551%, 89.411926%)" stop-opacity="1"/>
<stop offset="0.4375" stop-color="rgb(74.118042%, 77.6474%, 89.019775%)" stop-opacity="1"/>
<stop offset="0.441406" stop-color="rgb(73.725891%, 77.255249%, 89.019775%)" stop-opacity="1"/>
<stop offset="0.445312" stop-color="rgb(73.33374%, 76.863098%, 88.627625%)" stop-opacity="1"/>
<stop offset="0.449219" stop-color="rgb(72.549438%, 76.470947%, 88.235474%)" stop-opacity="1"/>
<stop offset="0.453125" stop-color="rgb(72.157288%, 76.078796%, 87.843323%)" stop-opacity="1"/>
<stop offset="0.457031" stop-color="rgb(71.765137%, 75.686646%, 87.843323%)" stop-opacity="1"/>
<stop offset="0.460938" stop-color="rgb(70.980835%, 74.902344%, 87.451172%)" stop-opacity="1"/>
<stop offset="0.464844" stop-color="rgb(70.588684%, 74.510193%, 87.059021%)" stop-opacity="1"/>
<stop offset="0.46875" stop-color="rgb(70.196533%, 74.118042%, 87.059021%)" stop-opacity="1"/>
<stop offset="0.472656" stop-color="rgb(69.412231%, 73.725891%, 87.059021%)" stop-opacity="1"/>
<stop offset="0.476562" stop-color="rgb(69.020081%, 73.33374%, 86.66687%)" stop-opacity="1"/>
<stop offset="0.480469" stop-color="rgb(68.62793%, 72.941589%, 86.66687%)" stop-opacity="1"/>
<stop offset="0.484375" stop-color="rgb(67.843628%, 72.549438%, 86.274719%)" stop-opacity="1"/>
<stop offset="0.488281" stop-color="rgb(67.451477%, 72.157288%, 85.882568%)" stop-opacity="1"/>
<stop offset="0.492188" stop-color="rgb(66.667175%, 71.765137%, 85.882568%)" stop-opacity="1"/>
<stop offset="0.496094" stop-color="rgb(66.275024%, 71.372986%, 85.490417%)" stop-opacity="1"/>
<stop offset="0.5" stop-color="rgb(65.882874%, 70.980835%, 85.098267%)" stop-opacity="1"/>
<stop offset="0.503906" stop-color="rgb(65.098572%, 70.588684%, 85.098267%)" stop-opacity="1"/>
<stop offset="0.507812" stop-color="rgb(64.706421%, 70.196533%, 85.098267%)" stop-opacity="1"/>
<stop offset="0.511719" stop-color="rgb(64.31427%, 69.804382%, 84.706116%)" stop-opacity="1"/>
<stop offset="0.515625" stop-color="rgb(63.529968%, 69.020081%, 84.706116%)" stop-opacity="1"/>
<stop offset="0.519531" stop-color="rgb(63.137817%, 68.62793%, 84.313965%)" stop-opacity="1"/>
<stop offset="0.523438" stop-color="rgb(62.745667%, 68.235779%, 83.921814%)" stop-opacity="1"/>
<stop offset="0.527344" stop-color="rgb(62.353516%, 67.843628%, 83.921814%)" stop-opacity="1"/>
<stop offset="0.53125" stop-color="rgb(61.961365%, 67.451477%, 83.529663%)" stop-opacity="1"/>
<stop offset="0.535156" stop-color="rgb(61.569214%, 67.059326%, 83.529663%)" stop-opacity="1"/>
<stop offset="0.539062" stop-color="rgb(60.784912%, 66.667175%, 83.529663%)" stop-opacity="1"/>
<stop offset="0.542969" stop-color="rgb(60.392761%, 66.275024%, 83.137512%)" stop-opacity="1"/>
<stop offset="0.546875" stop-color="rgb(60.00061%, 65.882874%, 82.745361%)" stop-opacity="1"/>
<stop offset="0.550781" stop-color="rgb(59.216309%, 65.490723%, 82.745361%)" stop-opacity="1"/>
<stop offset="0.554688" stop-color="rgb(58.824158%, 65.098572%, 82.35321%)" stop-opacity="1"/>
<stop offset="0.558594" stop-color="rgb(58.432007%, 64.706421%, 81.96106%)" stop-opacity="1"/>
<stop offset="0.5625" stop-color="rgb(58.039856%, 64.31427%, 81.96106%)" stop-opacity="1"/>
<stop offset="0.566406" stop-color="rgb(57.647705%, 63.922119%, 81.568909%)" stop-opacity="1"/>
<stop offset="0.570312" stop-color="rgb(56.863403%, 63.529968%, 81.568909%)" stop-opacity="1"/>
<stop offset="0.574219" stop-color="rgb(56.471252%, 63.137817%, 81.176758%)" stop-opacity="1"/>
<stop offset="0.578125" stop-color="rgb(56.079102%, 63.137817%, 81.176758%)" stop-opacity="1"/>
<stop offset="0.582031" stop-color="rgb(55.686951%, 62.745667%, 80.784607%)" stop-opacity="1"/>
<stop offset="0.585938" stop-color="rgb(55.2948%, 62.353516%, 80.784607%)" stop-opacity="1"/>
<stop offset="0.589844" stop-color="rgb(54.902649%, 61.961365%, 80.392456%)" stop-opacity="1"/>
<stop offset="0.59375" stop-color="rgb(54.118347%, 61.569214%, 80.392456%)" stop-opacity="1"/>
<stop offset="0.597656" stop-color="rgb(53.726196%, 61.177063%, 80.000305%)" stop-opacity="1"/>
<stop offset="0.601562" stop-color="rgb(53.334045%, 60.784912%, 80.000305%)" stop-opacity="1"/>
<stop offset="0.605469" stop-color="rgb(52.941895%, 60.392761%, 79.608154%)" stop-opacity="1"/>
<stop offset="0.609375" stop-color="rgb(52.549744%, 60.00061%, 79.608154%)" stop-opacity="1"/>
<stop offset="0.613281" stop-color="rgb(51.765442%, 60.00061%, 79.216003%)" stop-opacity="1"/>
<stop offset="0.617188" stop-color="rgb(51.373291%, 59.608459%, 79.216003%)" stop-opacity="1"/>
<stop offset="0.621094" stop-color="rgb(50.98114%, 59.216309%, 78.823853%)" stop-opacity="1"/>
<stop offset="0.625" stop-color="rgb(50.588989%, 58.824158%, 78.823853%)" stop-opacity="1"/>
<stop offset="0.628906" stop-color="rgb(50.196838%, 58.432007%, 78.431702%)" stop-opacity="1"/>
<stop offset="0.632812" stop-color="rgb(49.803162%, 58.039856%, 78.431702%)" stop-opacity="1"/>
<stop offset="0.636719" stop-color="rgb(49.411011%, 57.647705%, 78.039551%)" stop-opacity="1"/>
<stop offset="0.640625" stop-color="rgb(49.01886%, 57.255554%, 78.039551%)" stop-opacity="1"/>
<stop offset="0.644531" stop-color="rgb(48.626709%, 56.863403%, 77.6474%)" stop-opacity="1"/>
<stop offset="0.648438" stop-color="rgb(47.842407%, 56.863403%, 77.6474%)" stop-opacity="1"/>
<stop offset="0.652344" stop-color="rgb(47.450256%, 56.471252%, 77.255249%)" stop-opacity="1"/>
<stop offset="0.65625" stop-color="rgb(47.058105%, 56.079102%, 77.255249%)" stop-opacity="1"/>
<stop offset="0.660156" stop-color="rgb(46.665955%, 55.686951%, 76.863098%)" stop-opacity="1"/>
<stop offset="0.664062" stop-color="rgb(46.273804%, 55.2948%, 76.863098%)" stop-opacity="1"/>
<stop offset="0.667969" stop-color="rgb(45.881653%, 54.902649%, 76.470947%)" stop-opacity="1"/>
<stop offset="0.671875" stop-color="rgb(45.489502%, 54.902649%, 76.470947%)" stop-opacity="1"/>
<stop offset="0.675781" stop-color="rgb(45.097351%, 54.510498%, 76.078796%)" stop-opacity="1"/>
<stop offset="0.679688" stop-color="rgb(44.7052%, 54.118347%, 76.078796%)" stop-opacity="1"/>
<stop offset="0.683594" stop-color="rgb(44.313049%, 54.118347%, 75.686646%)" stop-opacity="1"/>
<stop offset="0.6875" stop-color="rgb(43.920898%, 53.726196%, 75.686646%)" stop-opacity="1"/>
<stop offset="0.691406" stop-color="rgb(43.136597%, 53.334045%, 75.686646%)" stop-opacity="1"/>
<stop offset="0.695312" stop-color="rgb(42.744446%, 52.941895%, 75.294495%)" stop-opacity="1"/>
<stop offset="0.699219" stop-color="rgb(42.352295%, 52.549744%, 75.294495%)" stop-opacity="1"/>
<stop offset="0.703125" stop-color="rgb(41.960144%, 52.157593%, 74.902344%)" stop-opacity="1"/>
<stop offset="0.707031" stop-color="rgb(41.567993%, 51.765442%, 74.510193%)" stop-opacity="1"/>
<stop offset="0.710938" stop-color="rgb(41.175842%, 51.765442%, 74.510193%)" stop-opacity="1"/>
<stop offset="0.714844" stop-color="rgb(40.783691%, 51.373291%, 74.510193%)" stop-opacity="1"/>
<stop offset="0.71875" stop-color="rgb(40.391541%, 50.98114%, 74.118042%)" stop-opacity="1"/>
<stop offset="0.722656" stop-color="rgb(39.607239%, 50.98114%, 74.118042%)" stop-opacity="1"/>
<stop offset="0.726562" stop-color="rgb(39.215088%, 50.588989%, 74.118042%)" stop-opacity="1"/>
<stop offset="0.730469" stop-color="rgb(38.822937%, 50.196838%, 73.725891%)" stop-opacity="1"/>
<stop offset="0.734375" stop-color="rgb(38.430786%, 50.196838%, 73.725891%)" stop-opacity="1"/>
<stop offset="0.738281" stop-color="rgb(38.038635%, 49.803162%, 73.33374%)" stop-opacity="1"/>
<stop offset="0.742188" stop-color="rgb(37.646484%, 49.411011%, 72.941589%)" stop-opacity="1"/>
<stop offset="0.746094" stop-color="rgb(37.254333%, 49.01886%, 72.941589%)" stop-opacity="1"/>
<stop offset="0.75" stop-color="rgb(36.862183%, 48.626709%, 72.941589%)" stop-opacity="1"/>
<stop offset="0.753906" stop-color="rgb(36.470032%, 48.234558%, 72.549438%)" stop-opacity="1"/>
<stop offset="0.757812" stop-color="rgb(35.68573%, 48.234558%, 72.549438%)" stop-opacity="1"/>
<stop offset="0.761719" stop-color="rgb(35.293579%, 47.842407%, 72.157288%)" stop-opacity="1"/>
<stop offset="0.765625" stop-color="rgb(34.901428%, 47.450256%, 72.157288%)" stop-opacity="1"/>
<stop offset="0.769531" stop-color="rgb(34.117126%, 47.450256%, 72.157288%)" stop-opacity="1"/>
<stop offset="0.773438" stop-color="rgb(33.724976%, 47.058105%, 71.765137%)" stop-opacity="1"/>
<stop offset="0.777344" stop-color="rgb(33.332825%, 46.665955%, 71.765137%)" stop-opacity="1"/>
<stop offset="0.78125" stop-color="rgb(32.940674%, 46.665955%, 71.765137%)" stop-opacity="1"/>
<stop offset="0.785156" stop-color="rgb(32.548523%, 46.273804%, 71.372986%)" stop-opacity="1"/>
<stop offset="0.789062" stop-color="rgb(31.764221%, 45.881653%, 70.980835%)" stop-opacity="1"/>
<stop offset="0.792969" stop-color="rgb(31.37207%, 45.881653%, 70.980835%)" stop-opacity="1"/>
<stop offset="0.796875" stop-color="rgb(30.979919%, 45.489502%, 70.588684%)" stop-opacity="1"/>
<stop offset="0.800781" stop-color="rgb(30.195618%, 45.097351%, 70.588684%)" stop-opacity="1"/>
<stop offset="0.804688" stop-color="rgb(29.803467%, 45.097351%, 70.588684%)" stop-opacity="1"/>
<stop offset="0.808594" stop-color="rgb(29.411316%, 44.7052%, 70.196533%)" stop-opacity="1"/>
<stop offset="0.8125" stop-color="rgb(28.627014%, 44.313049%, 70.196533%)" stop-opacity="1"/>
<stop offset="0.816406" stop-color="rgb(28.234863%, 43.920898%, 70.196533%)" stop-opacity="1"/>
<stop offset="0.820312" stop-color="rgb(27.842712%, 43.920898%, 69.804382%)" stop-opacity="1"/>
<stop offset="0.824219" stop-color="rgb(27.450562%, 43.528748%, 69.804382%)" stop-opacity="1"/>
<stop offset="0.828125" stop-color="rgb(27.058411%, 43.136597%, 69.804382%)" stop-opacity="1"/>
<stop offset="0.832031" stop-color="rgb(26.66626%, 43.136597%, 69.412231%)" stop-opacity="1"/>
<stop offset="0.835938" stop-color="rgb(25.881958%, 42.744446%, 69.412231%)" stop-opacity="1"/>
<stop offset="0.839844" stop-color="rgb(25.489807%, 42.744446%, 69.412231%)" stop-opacity="1"/>
<stop offset="0.84375" stop-color="rgb(24.705505%, 42.352295%, 69.020081%)" stop-opacity="1"/>
<stop offset="0.847656" stop-color="rgb(23.921204%, 42.352295%, 69.020081%)" stop-opacity="1"/>
<stop offset="0.851562" stop-color="rgb(23.529053%, 41.960144%, 69.020081%)" stop-opacity="1"/>
<stop offset="0.855469" stop-color="rgb(22.744751%, 41.960144%, 68.62793%)" stop-opacity="1"/>
<stop offset="0.859375" stop-color="rgb(22.3526%, 41.567993%, 68.62793%)" stop-opacity="1"/>
<stop offset="0.863281" stop-color="rgb(21.960449%, 41.175842%, 68.62793%)" stop-opacity="1"/>
<stop offset="0.867188" stop-color="rgb(21.176147%, 41.175842%, 68.235779%)" stop-opacity="1"/>
<stop offset="0.871094" stop-color="rgb(20.783997%, 40.783691%, 68.235779%)" stop-opacity="1"/>
<stop offset="0.875" stop-color="rgb(19.999695%, 40.391541%, 67.843628%)" stop-opacity="1"/>
<stop offset="0.878906" stop-color="rgb(19.607544%, 40.391541%, 67.843628%)" stop-opacity="1"/>
<stop offset="0.882812" stop-color="rgb(18.823242%, 39.99939%, 67.451477%)" stop-opacity="1"/>
<stop offset="0.886719" stop-color="rgb(18.03894%, 39.607239%, 67.451477%)" stop-opacity="1"/>
<stop offset="0.890625" stop-color="rgb(17.64679%, 39.607239%, 67.059326%)" stop-opacity="1"/>
<stop offset="0.894531" stop-color="rgb(16.862488%, 39.215088%, 67.059326%)" stop-opacity="1"/>
<stop offset="0.898438" stop-color="rgb(16.078186%, 39.215088%, 67.059326%)" stop-opacity="1"/>
<stop offset="0.902344" stop-color="rgb(15.293884%, 38.822937%, 66.667175%)" stop-opacity="1"/>
<stop offset="0.90625" stop-color="rgb(14.509583%, 38.822937%, 66.667175%)" stop-opacity="1"/>
<stop offset="0.910156" stop-color="rgb(13.725281%, 38.430786%, 66.667175%)" stop-opacity="1"/>
<stop offset="0.914062" stop-color="rgb(12.940979%, 38.430786%, 66.275024%)" stop-opacity="1"/>
<stop offset="0.917969" stop-color="rgb(12.156677%, 38.038635%, 66.275024%)" stop-opacity="1"/>
<stop offset="0.921875" stop-color="rgb(10.980225%, 37.646484%, 66.275024%)" stop-opacity="1"/>
<stop offset="0.925781" stop-color="rgb(10.195923%, 37.646484%, 65.882874%)" stop-opacity="1"/>
<stop offset="0.929688" stop-color="rgb(9.01947%, 37.254333%, 65.882874%)" stop-opacity="1"/>
<stop offset="0.933594" stop-color="rgb(7.843018%, 37.254333%, 65.882874%)" stop-opacity="1"/>
<stop offset="0.9375" stop-color="rgb(6.274414%, 36.862183%, 65.490723%)" stop-opacity="1"/>
<stop offset="0.941406" stop-color="rgb(5.097961%, 36.862183%, 65.490723%)" stop-opacity="1"/>
<stop offset="0.945312" stop-color="rgb(3.137207%, 36.470032%, 65.490723%)" stop-opacity="1"/>
<stop offset="0.949219" stop-color="rgb(1.176453%, 36.077881%, 65.098572%)" stop-opacity="1"/>
<stop offset="0.953125" stop-color="rgb(0%, 36.077881%, 65.098572%)" stop-opacity="1"/>
<stop offset="0.957031" stop-color="rgb(0%, 35.68573%, 64.706421%)" stop-opacity="1"/>
<stop offset="0.964844" stop-color="rgb(0%, 35.68573%, 64.706421%)" stop-opacity="1"/>
<stop offset="0.96875" stop-color="rgb(0%, 35.293579%, 64.706421%)" stop-opacity="1"/>
<stop offset="0.972656" stop-color="rgb(0%, 34.901428%, 64.31427%)" stop-opacity="1"/>
<stop offset="0.976562" stop-color="rgb(0%, 34.901428%, 64.31427%)" stop-opacity="1"/>
<stop offset="0.980469" stop-color="rgb(0%, 34.509277%, 64.31427%)" stop-opacity="1"/>
<stop offset="0.984375" stop-color="rgb(0%, 34.117126%, 63.922119%)" stop-opacity="1"/>
<stop offset="0.988281" stop-color="rgb(0%, 34.117126%, 63.922119%)" stop-opacity="1"/>
<stop offset="0.992188" stop-color="rgb(0%, 33.724976%, 63.529968%)" stop-opacity="1"/>
<stop offset="1" stop-color="rgb(0%, 33.724976%, 63.529968%)" stop-opacity="1"/>
</radialGradient>
<clipPath id="clip-5">
<path clip-rule="nonzero" d="M 0 22 L 185.84375 22 L 185.84375 212.140625 L 0 212.140625 Z M 0 22 "/>
</clipPath>
<clipPath id="clip-6">
<path clip-rule="nonzero" d="M 21.4375 47.546875 L 52.589844 47.546875 L 52.589844 155.105469 L 21.4375 155.105469 Z M 21.4375 47.546875 "/>
</clipPath>
<linearGradient id="linear-pattern-0" gradientUnits="userSpaceOnUse" x1="-0.00000819598" y1="0" x2="1.000011" y2="0" gradientTransform="matrix(31.149399, 0, 0, 31.149399, 21.438255, 101.325981)">
<stop offset="0" stop-color="rgb(98.039246%, 65.098572%, 10.195923%)" stop-opacity="1"/>
<stop offset="0.00390625" stop-color="rgb(98.039246%, 65.490723%, 10.980225%)" stop-opacity="1"/>
<stop offset="0.0078125" stop-color="rgb(98.039246%, 65.490723%, 11.764526%)" stop-opacity="1"/>
<stop offset="0.0117188" stop-color="rgb(98.039246%, 65.882874%, 12.548828%)" stop-opacity="1"/>
<stop offset="0.015625" stop-color="rgb(98.039246%, 66.275024%, 13.725281%)" stop-opacity="1"/>
<stop offset="0.0195313" stop-color="rgb(98.431396%, 66.667175%, 14.901733%)" stop-opacity="1"/>
<stop offset="0.0234375" stop-color="rgb(98.431396%, 67.059326%, 16.078186%)" stop-opacity="1"/>
<stop offset="0.0273438" stop-color="rgb(98.431396%, 67.059326%, 16.862488%)" stop-opacity="1"/>
<stop offset="0.03125" stop-color="rgb(98.431396%, 67.451477%, 17.64679%)" stop-opacity="1"/>
<stop offset="0.0351563" stop-color="rgb(98.431396%, 67.451477%, 18.823242%)" stop-opacity="1"/>
<stop offset="0.0390625" stop-color="rgb(98.431396%, 67.843628%, 19.607544%)" stop-opacity="1"/>
<stop offset="0.0429688" stop-color="rgb(98.431396%, 68.235779%, 20.783997%)" stop-opacity="1"/>
<stop offset="0.046875" stop-color="rgb(98.431396%, 68.62793%, 21.568298%)" stop-opacity="1"/>
<stop offset="0.0507813" stop-color="rgb(98.431396%, 69.020081%, 22.3526%)" stop-opacity="1"/>
<stop offset="0.0546875" stop-color="rgb(98.431396%, 69.020081%, 23.529053%)" stop-opacity="1"/>
<stop offset="0.0585938" stop-color="rgb(98.823547%, 69.412231%, 24.313354%)" stop-opacity="1"/>
<stop offset="0.0625" stop-color="rgb(98.823547%, 69.804382%, 25.489807%)" stop-opacity="1"/>
<stop offset="0.0664062" stop-color="rgb(98.823547%, 70.196533%, 26.274109%)" stop-opacity="1"/>
<stop offset="0.0703125" stop-color="rgb(98.823547%, 70.588684%, 27.450562%)" stop-opacity="1"/>
<stop offset="0.0742188" stop-color="rgb(98.823547%, 70.588684%, 28.234863%)" stop-opacity="1"/>
<stop offset="0.078125" stop-color="rgb(98.823547%, 70.980835%, 29.019165%)" stop-opacity="1"/>
<stop offset="0.0820313" stop-color="rgb(98.823547%, 71.372986%, 30.195618%)" stop-opacity="1"/>
<stop offset="0.0859375" stop-color="rgb(98.823547%, 71.765137%, 30.979919%)" stop-opacity="1"/>
<stop offset="0.0898438" stop-color="rgb(98.823547%, 72.157288%, 31.764221%)" stop-opacity="1"/>
<stop offset="0.09375" stop-color="rgb(99.215698%, 72.549438%, 32.940674%)" stop-opacity="1"/>
<stop offset="0.0976563" stop-color="rgb(99.215698%, 72.549438%, 33.724976%)" stop-opacity="1"/>
<stop offset="0.101563" stop-color="rgb(99.215698%, 72.941589%, 34.509277%)" stop-opacity="1"/>
<stop offset="0.105469" stop-color="rgb(99.215698%, 73.33374%, 35.293579%)" stop-opacity="1"/>
<stop offset="0.109375" stop-color="rgb(99.215698%, 73.725891%, 36.470032%)" stop-opacity="1"/>
<stop offset="0.113281" stop-color="rgb(99.215698%, 74.118042%, 37.254333%)" stop-opacity="1"/>
<stop offset="0.117188" stop-color="rgb(99.215698%, 74.118042%, 38.430786%)" stop-opacity="1"/>
<stop offset="0.121094" stop-color="rgb(99.215698%, 74.510193%, 39.215088%)" stop-opacity="1"/>
<stop offset="0.125" stop-color="rgb(99.215698%, 74.902344%, 39.99939%)" stop-opacity="1"/>
<stop offset="0.128906" stop-color="rgb(99.215698%, 75.294495%, 40.783691%)" stop-opacity="1"/>
<stop offset="0.132812" stop-color="rgb(99.215698%, 75.686646%, 41.567993%)" stop-opacity="1"/>
<stop offset="0.136719" stop-color="rgb(99.215698%, 75.686646%, 42.352295%)" stop-opacity="1"/>
<stop offset="0.140625" stop-color="rgb(99.215698%, 76.078796%, 42.744446%)" stop-opacity="1"/>
<stop offset="0.144531" stop-color="rgb(99.215698%, 76.470947%, 43.920898%)" stop-opacity="1"/>
<stop offset="0.148438" stop-color="rgb(99.215698%, 76.863098%, 44.7052%)" stop-opacity="1"/>
<stop offset="0.152344" stop-color="rgb(99.607849%, 77.255249%, 45.489502%)" stop-opacity="1"/>
<stop offset="0.15625" stop-color="rgb(99.607849%, 77.255249%, 46.273804%)" stop-opacity="1"/>
<stop offset="0.160156" stop-color="rgb(99.607849%, 77.6474%, 47.058105%)" stop-opacity="1"/>
<stop offset="0.164063" stop-color="rgb(99.607849%, 78.039551%, 47.450256%)" stop-opacity="1"/>
<stop offset="0.167969" stop-color="rgb(99.607849%, 78.431702%, 48.234558%)" stop-opacity="1"/>
<stop offset="0.171875" stop-color="rgb(99.607849%, 78.823853%, 49.01886%)" stop-opacity="1"/>
<stop offset="0.175781" stop-color="rgb(99.607849%, 79.216003%, 49.803162%)" stop-opacity="1"/>
<stop offset="0.179688" stop-color="rgb(99.607849%, 79.608154%, 50.588989%)" stop-opacity="1"/>
<stop offset="0.183594" stop-color="rgb(99.607849%, 79.608154%, 51.373291%)" stop-opacity="1"/>
<stop offset="0.1875" stop-color="rgb(99.607849%, 80.000305%, 52.157593%)" stop-opacity="1"/>
<stop offset="0.191406" stop-color="rgb(99.607849%, 80.392456%, 52.941895%)" stop-opacity="1"/>
<stop offset="0.195313" stop-color="rgb(99.607849%, 80.784607%, 53.726196%)" stop-opacity="1"/>
<stop offset="0.199219" stop-color="rgb(99.607849%, 81.176758%, 54.510498%)" stop-opacity="1"/>
<stop offset="0.203125" stop-color="rgb(99.607849%, 81.176758%, 54.902649%)" stop-opacity="1"/>
<stop offset="0.207031" stop-color="rgb(100%, 81.568909%, 55.686951%)" stop-opacity="1"/>
<stop offset="0.210938" stop-color="rgb(100%, 81.96106%, 56.471252%)" stop-opacity="1"/>
<stop offset="0.214844" stop-color="rgb(100%, 82.35321%, 57.255554%)" stop-opacity="1"/>
<stop offset="0.21875" stop-color="rgb(100%, 82.745361%, 58.432007%)" stop-opacity="1"/>
<stop offset="0.222656" stop-color="rgb(100%, 82.745361%, 59.216309%)" stop-opacity="1"/>
<stop offset="0.226563" stop-color="rgb(100%, 83.137512%, 59.608459%)" stop-opacity="1"/>
<stop offset="0.230469" stop-color="rgb(100%, 83.529663%, 60.392761%)" stop-opacity="1"/>
<stop offset="0.234375" stop-color="rgb(100%, 83.921814%, 61.177063%)" stop-opacity="1"/>
<stop offset="0.238281" stop-color="rgb(100%, 84.313965%, 62.353516%)" stop-opacity="1"/>
<stop offset="0.242188" stop-color="rgb(100%, 84.706116%, 63.137817%)" stop-opacity="1"/>
<stop offset="0.246094" stop-color="rgb(100%, 85.098267%, 63.529968%)" stop-opacity="1"/>
<stop offset="0.25" stop-color="rgb(100%, 85.490417%, 64.31427%)" stop-opacity="1"/>
<stop offset="0.253906" stop-color="rgb(100%, 85.490417%, 65.490723%)" stop-opacity="1"/>
<stop offset="0.257813" stop-color="rgb(100%, 85.882568%, 66.275024%)" stop-opacity="1"/>
<stop offset="0.261719" stop-color="rgb(100%, 86.274719%, 67.059326%)" stop-opacity="1"/>
<stop offset="0.265625" stop-color="rgb(100%, 86.274719%, 67.843628%)" stop-opacity="1"/>
<stop offset="0.269531" stop-color="rgb(100%, 86.66687%, 68.62793%)" stop-opacity="1"/>
<stop offset="0.273438" stop-color="rgb(100%, 87.059021%, 69.412231%)" stop-opacity="1"/>
<stop offset="0.277344" stop-color="rgb(100%, 87.451172%, 70.196533%)" stop-opacity="1"/>
<stop offset="0.28125" stop-color="rgb(100%, 87.843323%, 70.980835%)" stop-opacity="1"/>
<stop offset="0.285156" stop-color="rgb(100%, 88.235474%, 71.765137%)" stop-opacity="1"/>
<stop offset="0.289063" stop-color="rgb(100%, 88.627625%, 72.549438%)" stop-opacity="1"/>
<stop offset="0.292969" stop-color="rgb(100%, 88.627625%, 73.33374%)" stop-opacity="1"/>
<stop offset="0.296875" stop-color="rgb(100%, 89.019775%, 74.118042%)" stop-opacity="1"/>
<stop offset="0.300781" stop-color="rgb(100%, 89.411926%, 74.902344%)" stop-opacity="1"/>
<stop offset="0.304688" stop-color="rgb(100%, 89.804077%, 76.078796%)" stop-opacity="1"/>
<stop offset="0.308594" stop-color="rgb(100%, 90.196228%, 76.863098%)" stop-opacity="1"/>
<stop offset="0.3125" stop-color="rgb(100%, 90.588379%, 77.6474%)" stop-opacity="1"/>
<stop offset="0.316406" stop-color="rgb(100%, 90.98053%, 78.431702%)" stop-opacity="1"/>
<stop offset="0.320313" stop-color="rgb(100%, 91.372681%, 79.216003%)" stop-opacity="1"/>
<stop offset="0.324219" stop-color="rgb(100%, 91.372681%, 80.000305%)" stop-opacity="1"/>
<stop offset="0.328125" stop-color="rgb(100%, 91.764832%, 81.176758%)" stop-opacity="1"/>
<stop offset="0.332031" stop-color="rgb(100%, 92.156982%, 81.568909%)" stop-opacity="1"/>
<stop offset="0.335938" stop-color="rgb(100%, 92.549133%, 82.35321%)" stop-opacity="1"/>
<stop offset="0.339844" stop-color="rgb(100%, 92.941284%, 83.529663%)" stop-opacity="1"/>
<stop offset="0.34375" stop-color="rgb(100%, 93.333435%, 84.313965%)" stop-opacity="1"/>
<stop offset="0.347656" stop-color="rgb(100%, 93.725586%, 85.098267%)" stop-opacity="1"/>
<stop offset="0.351563" stop-color="rgb(100%, 94.117737%, 85.882568%)" stop-opacity="1"/>
<stop offset="0.355469" stop-color="rgb(100%, 94.117737%, 86.66687%)" stop-opacity="1"/>
<stop offset="0.359375" stop-color="rgb(100%, 94.509888%, 87.451172%)" stop-opacity="1"/>
<stop offset="0.363281" stop-color="rgb(100%, 94.902039%, 88.235474%)" stop-opacity="1"/>
<stop offset="0.367188" stop-color="rgb(100%, 95.294189%, 89.411926%)" stop-opacity="1"/>
<stop offset="0.371094" stop-color="rgb(100%, 95.68634%, 90.196228%)" stop-opacity="1"/>
<stop offset="0.375" stop-color="rgb(100%, 96.078491%, 90.98053%)" stop-opacity="1"/>
<stop offset="0.378906" stop-color="rgb(100%, 96.470642%, 91.764832%)" stop-opacity="1"/>
<stop offset="0.382813" stop-color="rgb(100%, 96.862793%, 92.549133%)" stop-opacity="1"/>
<stop offset="0.386719" stop-color="rgb(100%, 97.254944%, 93.725586%)" stop-opacity="1"/>
<stop offset="0.390625" stop-color="rgb(100%, 97.647095%, 94.509888%)" stop-opacity="1"/>
<stop offset="0.394531" stop-color="rgb(100%, 98.039246%, 95.68634%)" stop-opacity="1"/>
<stop offset="0.398438" stop-color="rgb(100%, 98.823547%, 96.862793%)" stop-opacity="1"/>
<stop offset="0.402344" stop-color="rgb(100%, 99.215698%, 98.431396%)" stop-opacity="1"/>
<stop offset="0.40625" stop-color="rgb(100%, 100%, 100%)" stop-opacity="1"/>
<stop offset="0.4375" stop-color="rgb(100%, 100%, 100%)" stop-opacity="1"/>
<stop offset="0.5" stop-color="rgb(100%, 100%, 100%)" stop-opacity="1"/>
<stop offset="0.5625" stop-color="rgb(100%, 100%, 100%)" stop-opacity="1"/>
<stop offset="0.570313" stop-color="rgb(100%, 100%, 100%)" stop-opacity="1"/>
<stop offset="0.574219" stop-color="rgb(100%, 99.607849%, 99.215698%)" stop-opacity="1"/>
<stop offset="0.578125" stop-color="rgb(100%, 99.215698%, 98.431396%)" stop-opacity="1"/>
<stop offset="0.582031" stop-color="rgb(100%, 98.823547%, 97.254944%)" stop-opacity="1"/>
<stop offset="0.585938" stop-color="rgb(100%, 98.039246%, 95.68634%)" stop-opacity="1"/>
<stop offset="0.589844" stop-color="rgb(100%, 97.647095%, 94.902039%)" stop-opacity="1"/>
<stop offset="0.59375" stop-color="rgb(100%, 97.254944%, 93.725586%)" stop-opacity="1"/>
<stop offset="0.597656" stop-color="rgb(100%, 96.862793%, 92.941284%)" stop-opacity="1"/>
<stop offset="0.601562" stop-color="rgb(100%, 96.470642%, 92.156982%)" stop-opacity="1"/>
<stop offset="0.605469" stop-color="rgb(100%, 96.078491%, 91.372681%)" stop-opacity="1"/>
<stop offset="0.609375" stop-color="rgb(100%, 95.68634%, 90.588379%)" stop-opacity="1"/>
<stop offset="0.613281" stop-color="rgb(100%, 95.68634%, 89.804077%)" stop-opacity="1"/>
<stop offset="0.617188" stop-color="rgb(100%, 95.294189%, 88.627625%)" stop-opacity="1"/>
<stop offset="0.621094" stop-color="rgb(100%, 94.902039%, 87.843323%)" stop-opacity="1"/>
<stop offset="0.625" stop-color="rgb(100%, 94.509888%, 87.059021%)" stop-opacity="1"/>
<stop offset="0.628906" stop-color="rgb(100%, 94.117737%, 86.274719%)" stop-opacity="1"/>
<stop offset="0.632813" stop-color="rgb(100%, 93.725586%, 85.490417%)" stop-opacity="1"/>
<stop offset="0.636719" stop-color="rgb(100%, 93.333435%, 84.706116%)" stop-opacity="1"/>
<stop offset="0.640625" stop-color="rgb(100%, 92.941284%, 83.921814%)" stop-opacity="1"/>
<stop offset="0.644531" stop-color="rgb(100%, 92.549133%, 83.137512%)" stop-opacity="1"/>
<stop offset="0.648438" stop-color="rgb(100%, 92.549133%, 82.35321%)" stop-opacity="1"/>
<stop offset="0.652344" stop-color="rgb(100%, 92.156982%, 81.568909%)" stop-opacity="1"/>
<stop offset="0.65625" stop-color="rgb(100%, 91.764832%, 80.784607%)" stop-opacity="1"/>
<stop offset="0.660156" stop-color="rgb(100%, 91.372681%, 80.000305%)" stop-opacity="1"/>
<stop offset="0.664063" stop-color="rgb(100%, 91.372681%, 79.216003%)" stop-opacity="1"/>
<stop offset="0.667969" stop-color="rgb(100%, 90.98053%, 78.431702%)" stop-opacity="1"/>
<stop offset="0.671875" stop-color="rgb(100%, 90.588379%, 77.6474%)" stop-opacity="1"/>
<stop offset="0.675781" stop-color="rgb(100%, 90.196228%, 76.863098%)" stop-opacity="1"/>
<stop offset="0.679688" stop-color="rgb(100%, 89.804077%, 76.078796%)" stop-opacity="1"/>
<stop offset="0.683594" stop-color="rgb(100%, 89.411926%, 75.294495%)" stop-opacity="1"/>
<stop offset="0.6875" stop-color="rgb(100%, 89.019775%, 74.118042%)" stop-opacity="1"/>
<stop offset="0.691406" stop-color="rgb(100%, 88.627625%, 73.33374%)" stop-opacity="1"/>
<stop offset="0.695313" stop-color="rgb(100%, 88.627625%, 72.941589%)" stop-opacity="1"/>
<stop offset="0.699219" stop-color="rgb(100%, 88.235474%, 71.765137%)" stop-opacity="1"/>
<stop offset="0.703125" stop-color="rgb(100%, 87.843323%, 70.980835%)" stop-opacity="1"/>
<stop offset="0.707031" stop-color="rgb(100%, 87.451172%, 70.196533%)" stop-opacity="1"/>
<stop offset="0.710938" stop-color="rgb(100%, 87.451172%, 69.804382%)" stop-opacity="1"/>
<stop offset="0.714844" stop-color="rgb(100%, 87.059021%, 69.020081%)" stop-opacity="1"/>
<stop offset="0.71875" stop-color="rgb(100%, 86.66687%, 68.235779%)" stop-opacity="1"/>
<stop offset="0.722656" stop-color="rgb(100%, 86.274719%, 67.451477%)" stop-opacity="1"/>
<stop offset="0.726562" stop-color="rgb(100%, 85.882568%, 66.667175%)" stop-opacity="1"/>
<stop offset="0.730469" stop-color="rgb(100%, 85.882568%, 65.882874%)" stop-opacity="1"/>
<stop offset="0.734375" stop-color="rgb(100%, 85.490417%, 64.706421%)" stop-opacity="1"/>
<stop offset="0.738281" stop-color="rgb(100%, 85.098267%, 64.31427%)" stop-opacity="1"/>
<stop offset="0.742188" stop-color="rgb(100%, 84.706116%, 63.529968%)" stop-opacity="1"/>
<stop offset="0.746094" stop-color="rgb(100%, 84.313965%, 62.745667%)" stop-opacity="1"/>
<stop offset="0.75" stop-color="rgb(100%, 83.921814%, 61.961365%)" stop-opacity="1"/>
<stop offset="0.753906" stop-color="rgb(100%, 83.921814%, 61.177063%)" stop-opacity="1"/>
<stop offset="0.757813" stop-color="rgb(100%, 83.529663%, 60.392761%)" stop-opacity="1"/>
<stop offset="0.761719" stop-color="rgb(100%, 83.137512%, 59.608459%)" stop-opacity="1"/>
<stop offset="0.765625" stop-color="rgb(100%, 82.745361%, 58.824158%)" stop-opacity="1"/>
<stop offset="0.769531" stop-color="rgb(100%, 82.35321%, 58.039856%)" stop-opacity="1"/>
<stop offset="0.773438" stop-color="rgb(100%, 81.96106%, 57.255554%)" stop-opacity="1"/>
<stop offset="0.777344" stop-color="rgb(100%, 81.96106%, 56.471252%)" stop-opacity="1"/>
<stop offset="0.78125" stop-color="rgb(100%, 81.568909%, 55.686951%)" stop-opacity="1"/>
<stop offset="0.785156" stop-color="rgb(99.607849%, 81.176758%, 54.902649%)" stop-opacity="1"/>
<stop offset="0.789063" stop-color="rgb(99.607849%, 81.176758%, 54.510498%)" stop-opacity="1"/>
<stop offset="0.792969" stop-color="rgb(99.607849%, 80.784607%, 53.726196%)" stop-opacity="1"/>
<stop offset="0.796875" stop-color="rgb(99.607849%, 80.392456%, 52.941895%)" stop-opacity="1"/>
<stop offset="0.800781" stop-color="rgb(99.607849%, 80.000305%, 52.157593%)" stop-opacity="1"/>
<stop offset="0.804688" stop-color="rgb(99.607849%, 80.000305%, 51.765442%)" stop-opacity="1"/>
<stop offset="0.808594" stop-color="rgb(99.607849%, 79.608154%, 50.98114%)" stop-opacity="1"/>
<stop offset="0.8125" stop-color="rgb(99.607849%, 79.216003%, 50.196838%)" stop-opacity="1"/>
<stop offset="0.816406" stop-color="rgb(99.607849%, 78.823853%, 49.411011%)" stop-opacity="1"/>
<stop offset="0.820313" stop-color="rgb(99.607849%, 78.431702%, 48.626709%)" stop-opacity="1"/>
<stop offset="0.824219" stop-color="rgb(99.607849%, 78.039551%, 47.842407%)" stop-opacity="1"/>
<stop offset="0.828125" stop-color="rgb(99.607849%, 77.6474%, 47.058105%)" stop-opacity="1"/>
<stop offset="0.832031" stop-color="rgb(99.607849%, 77.6474%, 46.273804%)" stop-opacity="1"/>
<stop offset="0.835938" stop-color="rgb(99.607849%, 77.255249%, 45.881653%)" stop-opacity="1"/>
<stop offset="0.839844" stop-color="rgb(99.215698%, 76.863098%, 45.097351%)" stop-opacity="1"/>
<stop offset="0.84375" stop-color="rgb(99.215698%, 76.470947%, 44.313049%)" stop-opacity="1"/>
<stop offset="0.847656" stop-color="rgb(99.215698%, 76.470947%, 43.528748%)" stop-opacity="1"/>
<stop offset="0.851562" stop-color="rgb(99.215698%, 76.078796%, 42.744446%)" stop-opacity="1"/>
<stop offset="0.855469" stop-color="rgb(99.215698%, 75.686646%, 41.960144%)" stop-opacity="1"/>
<stop offset="0.859375" stop-color="rgb(99.215698%, 75.294495%, 41.175842%)" stop-opacity="1"/>
<stop offset="0.863281" stop-color="rgb(99.215698%, 74.902344%, 40.391541%)" stop-opacity="1"/>
<stop offset="0.867188" stop-color="rgb(99.215698%, 74.510193%, 39.607239%)" stop-opacity="1"/>
<stop offset="0.871094" stop-color="rgb(99.215698%, 74.510193%, 38.822937%)" stop-opacity="1"/>
<stop offset="0.875" stop-color="rgb(99.215698%, 74.118042%, 38.038635%)" stop-opacity="1"/>
<stop offset="0.878906" stop-color="rgb(99.215698%, 73.725891%, 37.254333%)" stop-opacity="1"/>
<stop offset="0.882813" stop-color="rgb(99.215698%, 73.725891%, 36.470032%)" stop-opacity="1"/>
<stop offset="0.886719" stop-color="rgb(99.215698%, 73.33374%, 35.293579%)" stop-opacity="1"/>
<stop offset="0.890625" stop-color="rgb(99.215698%, 72.941589%, 34.509277%)" stop-opacity="1"/>
<stop offset="0.894531" stop-color="rgb(99.215698%, 72.549438%, 33.724976%)" stop-opacity="1"/>
<stop offset="0.898438" stop-color="rgb(99.215698%, 72.549438%, 32.940674%)" stop-opacity="1"/>
<stop offset="0.902344" stop-color="rgb(98.823547%, 72.157288%, 32.156372%)" stop-opacity="1"/>
<stop offset="0.90625" stop-color="rgb(98.823547%, 71.765137%, 31.37207%)" stop-opacity="1"/>
<stop offset="0.910156" stop-color="rgb(98.823547%, 71.372986%, 30.195618%)" stop-opacity="1"/>
<stop offset="0.914063" stop-color="rgb(98.823547%, 70.980835%, 29.411316%)" stop-opacity="1"/>
<stop offset="0.917969" stop-color="rgb(98.823547%, 70.980835%, 28.627014%)" stop-opacity="1"/>
<stop offset="0.921875" stop-color="rgb(98.823547%, 70.588684%, 27.450562%)" stop-opacity="1"/>
<stop offset="0.925781" stop-color="rgb(98.823547%, 70.196533%, 26.66626%)" stop-opacity="1"/>
<stop offset="0.929688" stop-color="rgb(98.823547%, 69.804382%, 25.881958%)" stop-opacity="1"/>
<stop offset="0.933594" stop-color="rgb(98.823547%, 69.412231%, 24.705505%)" stop-opacity="1"/>
<stop offset="0.9375" stop-color="rgb(98.823547%, 69.412231%, 23.921204%)" stop-opacity="1"/>
<stop offset="0.941406" stop-color="rgb(98.431396%, 69.020081%, 23.136902%)" stop-opacity="1"/>
<stop offset="0.945313" stop-color="rgb(98.431396%, 69.020081%, 21.960449%)" stop-opacity="1"/>
<stop offset="0.949219" stop-color="rgb(98.431396%, 68.62793%, 21.176147%)" stop-opacity="1"/>
<stop offset="0.953125" stop-color="rgb(98.431396%, 68.235779%, 20.391846%)" stop-opacity="1"/>
<stop offset="0.957031" stop-color="rgb(98.431396%, 67.843628%, 19.215393%)" stop-opacity="1"/>
<stop offset="0.960938" stop-color="rgb(98.431396%, 67.451477%, 18.431091%)" stop-opacity="1"/>
<stop offset="0.964844" stop-color="rgb(98.431396%, 67.451477%, 17.64679%)" stop-opacity="1"/>
<stop offset="0.96875" stop-color="rgb(98.431396%, 67.059326%, 16.470337%)" stop-opacity="1"/>
<stop offset="0.972656" stop-color="rgb(98.431396%, 66.667175%, 15.686035%)" stop-opacity="1"/>
<stop offset="0.976562" stop-color="rgb(98.431396%, 66.667175%, 14.901733%)" stop-opacity="1"/>
<stop offset="0.980469" stop-color="rgb(98.039246%, 66.275024%, 13.725281%)" stop-opacity="1"/>
<stop offset="0.984375" stop-color="rgb(98.039246%, 65.882874%, 12.548828%)" stop-opacity="1"/>
<stop offset="0.988281" stop-color="rgb(98.039246%, 65.490723%, 11.764526%)" stop-opacity="1"/>
<stop offset="0.992188" stop-color="rgb(98.039246%, 65.490723%, 10.980225%)" stop-opacity="1"/>
<stop offset="0.996094" stop-color="rgb(98.039246%, 65.098572%, 10.588074%)" stop-opacity="1"/>
<stop offset="1" stop-color="rgb(98.039246%, 65.098572%, 10.195923%)" stop-opacity="1"/>
</linearGradient>
<clipPath id="clip-7">
<path clip-rule="nonzero" d="M 21.4375 47.546875 L 52.589844 47.546875 L 52.589844 155.105469 L 21.4375 155.105469 Z M 21.4375 47.546875 "/>
</clipPath>
<linearGradient id="linear-pattern-1" gradientUnits="userSpaceOnUse" x1="-0.00000819598" y1="0" x2="1.000011" y2="0" gradientTransform="matrix(31.149399, 0, 0, 31.149399, 21.438255, 101.325981)">
<stop offset="0" stop-color="rgb(98.039246%, 65.098572%, 10.195923%)" stop-opacity="1"/>
<stop offset="0.00390625" stop-color="rgb(98.039246%, 65.490723%, 10.980225%)" stop-opacity="1"/>
<stop offset="0.0078125" stop-color="rgb(98.039246%, 65.490723%, 11.764526%)" stop-opacity="1"/>
<stop offset="0.0117188" stop-color="rgb(98.039246%, 65.882874%, 12.548828%)" stop-opacity="1"/>
<stop offset="0.015625" stop-color="rgb(98.039246%, 66.275024%, 13.725281%)" stop-opacity="1"/>
<stop offset="0.0195313" stop-color="rgb(98.431396%, 66.667175%, 14.901733%)" stop-opacity="1"/>
<stop offset="0.0234375" stop-color="rgb(98.431396%, 67.059326%, 16.078186%)" stop-opacity="1"/>
<stop offset="0.0273438" stop-color="rgb(98.431396%, 67.059326%, 16.862488%)" stop-opacity="1"/>
<stop offset="0.03125" stop-color="rgb(98.431396%, 67.451477%, 17.64679%)" stop-opacity="1"/>
<stop offset="0.0351563" stop-color="rgb(98.431396%, 67.451477%, 18.823242%)" stop-opacity="1"/>
<stop offset="0.0390625" stop-color="rgb(98.431396%, 67.843628%, 19.607544%)" stop-opacity="1"/>
<stop offset="0.0429688" stop-color="rgb(98.431396%, 68.235779%, 20.783997%)" stop-opacity="1"/>
<stop offset="0.046875" stop-color="rgb(98.431396%, 68.62793%, 21.568298%)" stop-opacity="1"/>
<stop offset="0.0507813" stop-color="rgb(98.431396%, 69.020081%, 22.3526%)" stop-opacity="1"/>
<stop offset="0.0546875" stop-color="rgb(98.431396%, 69.020081%, 23.529053%)" stop-opacity="1"/>
<stop offset="0.0585938" stop-color="rgb(98.823547%, 69.412231%, 24.313354%)" stop-opacity="1"/>
<stop offset="0.0625" stop-color="rgb(98.823547%, 69.804382%, 25.489807%)" stop-opacity="1"/>
<stop offset="0.0664062" stop-color="rgb(98.823547%, 70.196533%, 26.274109%)" stop-opacity="1"/>
<stop offset="0.0703125" stop-color="rgb(98.823547%, 70.588684%, 27.450562%)" stop-opacity="1"/>
<stop offset="0.0742188" stop-color="rgb(98.823547%, 70.588684%, 28.234863%)" stop-opacity="1"/>
<stop offset="0.078125" stop-color="rgb(98.823547%, 70.980835%, 29.019165%)" stop-opacity="1"/>
<stop offset="0.0820313" stop-color="rgb(98.823547%, 71.372986%, 30.195618%)" stop-opacity="1"/>
<stop offset="0.0859375" stop-color="rgb(98.823547%, 71.765137%, 30.979919%)" stop-opacity="1"/>
<stop offset="0.0898438" stop-color="rgb(98.823547%, 72.157288%, 31.764221%)" stop-opacity="1"/>
<stop offset="0.09375" stop-color="rgb(99.215698%, 72.549438%, 32.940674%)" stop-opacity="1"/>
<stop offset="0.0976563" stop-color="rgb(99.215698%, 72.549438%, 33.724976%)" stop-opacity="1"/>
<stop offset="0.101563" stop-color="rgb(99.215698%, 72.941589%, 34.509277%)" stop-opacity="1"/>
<stop offset="0.105469" stop-color="rgb(99.215698%, 73.33374%, 35.293579%)" stop-opacity="1"/>
<stop offset="0.109375" stop-color="rgb(99.215698%, 73.725891%, 36.470032%)" stop-opacity="1"/>
<stop offset="0.113281" stop-color="rgb(99.215698%, 74.118042%, 37.254333%)" stop-opacity="1"/>
<stop offset="0.117188" stop-color="rgb(99.215698%, 74.118042%, 38.430786%)" stop-opacity="1"/>
<stop offset="0.121094" stop-color="rgb(99.215698%, 74.510193%, 39.215088%)" stop-opacity="1"/>
<stop offset="0.125" stop-color="rgb(99.215698%, 74.902344%, 39.99939%)" stop-opacity="1"/>
<stop offset="0.128906" stop-color="rgb(99.215698%, 75.294495%, 40.783691%)" stop-opacity="1"/>
<stop offset="0.132812" stop-color="rgb(99.215698%, 75.686646%, 41.567993%)" stop-opacity="1"/>
<stop offset="0.136719" stop-color="rgb(99.215698%, 75.686646%, 42.352295%)" stop-opacity="1"/>
<stop offset="0.140625" stop-color="rgb(99.215698%, 76.078796%, 42.744446%)" stop-opacity="1"/>
<stop offset="0.144531" stop-color="rgb(99.215698%, 76.470947%, 43.920898%)" stop-opacity="1"/>
<stop offset="0.148438" stop-color="rgb(99.215698%, 76.863098%, 44.7052%)" stop-opacity="1"/>
<stop offset="0.152344" stop-color="rgb(99.607849%, 77.255249%, 45.489502%)" stop-opacity="1"/>
<stop offset="0.15625" stop-color="rgb(99.607849%, 77.255249%, 46.273804%)" stop-opacity="1"/>
<stop offset="0.160156" stop-color="rgb(99.607849%, 77.6474%, 47.058105%)" stop-opacity="1"/>
<stop offset="0.164063" stop-color="rgb(99.607849%, 78.039551%, 47.450256%)" stop-opacity="1"/>
<stop offset="0.167969" stop-color="rgb(99.607849%, 78.431702%, 48.234558%)" stop-opacity="1"/>
<stop offset="0.171875" stop-color="rgb(99.607849%, 78.823853%, 49.01886%)" stop-opacity="1"/>
<stop offset="0.175781" stop-color="rgb(99.607849%, 79.216003%, 49.803162%)" stop-opacity="1"/>
<stop offset="0.179688" stop-color="rgb(99.607849%, 79.608154%, 50.588989%)" stop-opacity="1"/>
<stop offset="0.183594" stop-color="rgb(99.607849%, 79.608154%, 51.373291%)" stop-opacity="1"/>
<stop offset="0.1875" stop-color="rgb(99.607849%, 80.000305%, 52.157593%)" stop-opacity="1"/>
<stop offset="0.191406" stop-color="rgb(99.607849%, 80.392456%, 52.941895%)" stop-opacity="1"/>
<stop offset="0.195313" stop-color="rgb(99.607849%, 80.784607%, 53.726196%)" stop-opacity="1"/>
<stop offset="0.199219" stop-color="rgb(99.607849%, 81.176758%, 54.510498%)" stop-opacity="1"/>
<stop offset="0.203125" stop-color="rgb(99.607849%, 81.176758%, 54.902649%)" stop-opacity="1"/>
<stop offset="0.207031" stop-color="rgb(100%, 81.568909%, 55.686951%)" stop-opacity="1"/>
<stop offset="0.210938" stop-color="rgb(100%, 81.96106%, 56.471252%)" stop-opacity="1"/>
<stop offset="0.214844" stop-color="rgb(100%, 82.35321%, 57.255554%)" stop-opacity="1"/>
<stop offset="0.21875" stop-color="rgb(100%, 82.745361%, 58.432007%)" stop-opacity="1"/>
<stop offset="0.222656" stop-color="rgb(100%, 82.745361%, 59.216309%)" stop-opacity="1"/>
<stop offset="0.226563" stop-color="rgb(100%, 83.137512%, 59.608459%)" stop-opacity="1"/>
<stop offset="0.230469" stop-color="rgb(100%, 83.529663%, 60.392761%)" stop-opacity="1"/>
<stop offset="0.234375" stop-color="rgb(100%, 83.921814%, 61.177063%)" stop-opacity="1"/>
<stop offset="0.238281" stop-color="rgb(100%, 84.313965%, 62.353516%)" stop-opacity="1"/>
<stop offset="0.242188" stop-color="rgb(100%, 84.706116%, 63.137817%)" stop-opacity="1"/>
<stop offset="0.246094" stop-color="rgb(100%, 85.098267%, 63.529968%)" stop-opacity="1"/>
<stop offset="0.25" stop-color="rgb(100%, 85.490417%, 64.31427%)" stop-opacity="1"/>
<stop offset="0.253906" stop-color="rgb(100%, 85.490417%, 65.490723%)" stop-opacity="1"/>
<stop offset="0.257813" stop-color="rgb(100%, 85.882568%, 66.275024%)" stop-opacity="1"/>
<stop offset="0.261719" stop-color="rgb(100%, 86.274719%, 67.059326%)" stop-opacity="1"/>
<stop offset="0.265625" stop-color="rgb(100%, 86.274719%, 67.843628%)" stop-opacity="1"/>
<stop offset="0.269531" stop-color="rgb(100%, 86.66687%, 68.62793%)" stop-opacity="1"/>
<stop offset="0.273438" stop-color="rgb(100%, 87.059021%, 69.412231%)" stop-opacity="1"/>
<stop offset="0.277344" stop-color="rgb(100%, 87.451172%, 70.196533%)" stop-opacity="1"/>
<stop offset="0.28125" stop-color="rgb(100%, 87.843323%, 70.980835%)" stop-opacity="1"/>
<stop offset="0.285156" stop-color="rgb(100%, 88.235474%, 71.765137%)" stop-opacity="1"/>
<stop offset="0.289063" stop-color="rgb(100%, 88.627625%, 72.549438%)" stop-opacity="1"/>
<stop offset="0.292969" stop-color="rgb(100%, 88.627625%, 73.33374%)" stop-opacity="1"/>
<stop offset="0.296875" stop-color="rgb(100%, 89.019775%, 74.118042%)" stop-opacity="1"/>
<stop offset="0.300781" stop-color="rgb(100%, 89.411926%, 74.902344%)" stop-opacity="1"/>
<stop offset="0.304688" stop-color="rgb(100%, 89.804077%, 76.078796%)" stop-opacity="1"/>
<stop offset="0.308594" stop-color="rgb(100%, 90.196228%, 76.863098%)" stop-opacity="1"/>
<stop offset="0.3125" stop-color="rgb(100%, 90.588379%, 77.6474%)" stop-opacity="1"/>
<stop offset="0.316406" stop-color="rgb(100%, 90.98053%, 78.431702%)" stop-opacity="1"/>
<stop offset="0.320313" stop-color="rgb(100%, 91.372681%, 79.216003%)" stop-opacity="1"/>
<stop offset="0.324219" stop-color="rgb(100%, 91.372681%, 80.000305%)" stop-opacity="1"/>
<stop offset="0.328125" stop-color="rgb(100%, 91.764832%, 81.176758%)" stop-opacity="1"/>
<stop offset="0.332031" stop-color="rgb(100%, 92.156982%, 81.568909%)" stop-opacity="1"/>
<stop offset="0.335938" stop-color="rgb(100%, 92.549133%, 82.35321%)" stop-opacity="1"/>
<stop offset="0.339844" stop-color="rgb(100%, 92.941284%, 83.529663%)" stop-opacity="1"/>
<stop offset="0.34375" stop-color="rgb(100%, 93.333435%, 84.313965%)" stop-opacity="1"/>
<stop offset="0.347656" stop-color="rgb(100%, 93.725586%, 85.098267%)" stop-opacity="1"/>
<stop offset="0.351563" stop-color="rgb(100%, 94.117737%, 85.882568%)" stop-opacity="1"/>
<stop offset="0.355469" stop-color="rgb(100%, 94.117737%, 86.66687%)" stop-opacity="1"/>
<stop offset="0.359375" stop-color="rgb(100%, 94.509888%, 87.451172%)" stop-opacity="1"/>
<stop offset="0.363281" stop-color="rgb(100%, 94.902039%, 88.235474%)" stop-opacity="1"/>
<stop offset="0.367188" stop-color="rgb(100%, 95.294189%, 89.411926%)" stop-opacity="1"/>
<stop offset="0.371094" stop-color="rgb(100%, 95.68634%, 90.196228%)" stop-opacity="1"/>
<stop offset="0.375" stop-color="rgb(100%, 96.078491%, 90.98053%)" stop-opacity="1"/>
<stop offset="0.378906" stop-color="rgb(100%, 96.470642%, 91.764832%)" stop-opacity="1"/>
<stop offset="0.382813" stop-color="rgb(100%, 96.862793%, 92.549133%)" stop-opacity="1"/>
<stop offset="0.386719" stop-color="rgb(100%, 97.254944%, 93.725586%)" stop-opacity="1"/>
<stop offset="0.390625" stop-color="rgb(100%, 97.647095%, 94.509888%)" stop-opacity="1"/>
<stop offset="0.394531" stop-color="rgb(100%, 98.039246%, 95.68634%)" stop-opacity="1"/>
<stop offset="0.398438" stop-color="rgb(100%, 98.823547%, 96.862793%)" stop-opacity="1"/>
<stop offset="0.402344" stop-color="rgb(100%, 99.215698%, 98.431396%)" stop-opacity="1"/>
<stop offset="0.40625" stop-color="rgb(100%, 100%, 100%)" stop-opacity="1"/>
<stop offset="0.4375" stop-color="rgb(100%, 100%, 100%)" stop-opacity="1"/>
<stop offset="0.5" stop-color="rgb(100%, 100%, 100%)" stop-opacity="1"/>
<stop offset="0.5625" stop-color="rgb(100%, 100%, 100%)" stop-opacity="1"/>
<stop offset="0.570313" stop-color="rgb(100%, 100%, 100%)" stop-opacity="1"/>
<stop offset="0.574219" stop-color="rgb(100%, 99.607849%, 99.215698%)" stop-opacity="1"/>
<stop offset="0.578125" stop-color="rgb(100%, 99.215698%, 98.431396%)" stop-opacity="1"/>
<stop offset="0.582031" stop-color="rgb(100%, 98.823547%, 97.254944%)" stop-opacity="1"/>
<stop offset="0.585938" stop-color="rgb(100%, 98.039246%, 95.68634%)" stop-opacity="1"/>
<stop offset="0.589844" stop-color="rgb(100%, 97.647095%, 94.902039%)" stop-opacity="1"/>
<stop offset="0.59375" stop-color="rgb(100%, 97.254944%, 93.725586%)" stop-opacity="1"/>
<stop offset="0.597656" stop-color="rgb(100%, 96.862793%, 92.941284%)" stop-opacity="1"/>
<stop offset="0.601562" stop-color="rgb(100%, 96.470642%, 92.156982%)" stop-opacity="1"/>
<stop offset="0.605469" stop-color="rgb(100%, 96.078491%, 91.372681%)" stop-opacity="1"/>
<stop offset="0.609375" stop-color="rgb(100%, 95.68634%, 90.588379%)" stop-opacity="1"/>
<stop offset="0.613281" stop-color="rgb(100%, 95.68634%, 89.804077%)" stop-opacity="1"/>
<stop offset="0.617188" stop-color="rgb(100%, 95.294189%, 88.627625%)" stop-opacity="1"/>
<stop offset="0.621094" stop-color="rgb(100%, 94.902039%, 87.843323%)" stop-opacity="1"/>
<stop offset="0.625" stop-color="rgb(100%, 94.509888%, 87.059021%)" stop-opacity="1"/>
<stop offset="0.628906" stop-color="rgb(100%, 94.117737%, 86.274719%)" stop-opacity="1"/>
<stop offset="0.632813" stop-color="rgb(100%, 93.725586%, 85.490417%)" stop-opacity="1"/>
<stop offset="0.636719" stop-color="rgb(100%, 93.333435%, 84.706116%)" stop-opacity="1"/>
<stop offset="0.640625" stop-color="rgb(100%, 92.941284%, 83.921814%)" stop-opacity="1"/>
<stop offset="0.644531" stop-color="rgb(100%, 92.549133%, 83.137512%)" stop-opacity="1"/>
<stop offset="0.648438" stop-color="rgb(100%, 92.549133%, 82.35321%)" stop-opacity="1"/>
<stop offset="0.652344" stop-color="rgb(100%, 92.156982%, 81.568909%)" stop-opacity="1"/>
<stop offset="0.65625" stop-color="rgb(100%, 91.764832%, 80.784607%)" stop-opacity="1"/>
<stop offset="0.660156" stop-color="rgb(100%, 91.372681%, 80.000305%)" stop-opacity="1"/>
<stop offset="0.664063" stop-color="rgb(100%, 91.372681%, 79.216003%)" stop-opacity="1"/>
<stop offset="0.667969" stop-color="rgb(100%, 90.98053%, 78.431702%)" stop-opacity="1"/>
<stop offset="0.671875" stop-color="rgb(100%, 90.588379%, 77.6474%)" stop-opacity="1"/>
<stop offset="0.675781" stop-color="rgb(100%, 90.196228%, 76.863098%)" stop-opacity="1"/>
<stop offset="0.679688" stop-color="rgb(100%, 89.804077%, 76.078796%)" stop-opacity="1"/>
<stop offset="0.683594" stop-color="rgb(100%, 89.411926%, 75.294495%)" stop-opacity="1"/>
<stop offset="0.6875" stop-color="rgb(100%, 89.019775%, 74.118042%)" stop-opacity="1"/>
<stop offset="0.691406" stop-color="rgb(100%, 88.627625%, 73.33374%)" stop-opacity="1"/>
<stop offset="0.695313" stop-color="rgb(100%, 88.627625%, 72.941589%)" stop-opacity="1"/>
<stop offset="0.699219" stop-color="rgb(100%, 88.235474%, 71.765137%)" stop-opacity="1"/>
<stop offset="0.703125" stop-color="rgb(100%, 87.843323%, 70.980835%)" stop-opacity="1"/>
<stop offset="0.707031" stop-color="rgb(100%, 87.451172%, 70.196533%)" stop-opacity="1"/>
<stop offset="0.710938" stop-color="rgb(100%, 87.451172%, 69.804382%)" stop-opacity="1"/>
<stop offset="0.714844" stop-color="rgb(100%, 87.059021%, 69.020081%)" stop-opacity="1"/>
<stop offset="0.71875" stop-color="rgb(100%, 86.66687%, 68.235779%)" stop-opacity="1"/>
<stop offset="0.722656" stop-color="rgb(100%, 86.274719%, 67.451477%)" stop-opacity="1"/>
<stop offset="0.726562" stop-color="rgb(100%, 85.882568%, 66.667175%)" stop-opacity="1"/>
<stop offset="0.730469" stop-color="rgb(100%, 85.882568%, 65.882874%)" stop-opacity="1"/>
<stop offset="0.734375" stop-color="rgb(100%, 85.490417%, 64.706421%)" stop-opacity="1"/>
<stop offset="0.738281" stop-color="rgb(100%, 85.098267%, 64.31427%)" stop-opacity="1"/>
<stop offset="0.742188" stop-color="rgb(100%, 84.706116%, 63.529968%)" stop-opacity="1"/>
<stop offset="0.746094" stop-color="rgb(100%, 84.313965%, 62.745667%)" stop-opacity="1"/>
<stop offset="0.75" stop-color="rgb(100%, 83.921814%, 61.961365%)" stop-opacity="1"/>
<stop offset="0.753906" stop-color="rgb(100%, 83.921814%, 61.177063%)" stop-opacity="1"/>
<stop offset="0.757813" stop-color="rgb(100%, 83.529663%, 60.392761%)" stop-opacity="1"/>
<stop offset="0.761719" stop-color="rgb(100%, 83.137512%, 59.608459%)" stop-opacity="1"/>
<stop offset="0.765625" stop-color="rgb(100%, 82.745361%, 58.824158%)" stop-opacity="1"/>
<stop offset="0.769531" stop-color="rgb(100%, 82.35321%, 58.039856%)" stop-opacity="1"/>
<stop offset="0.773438" stop-color="rgb(100%, 81.96106%, 57.255554%)" stop-opacity="1"/>
<stop offset="0.777344" stop-color="rgb(100%, 81.96106%, 56.471252%)" stop-opacity="1"/>
<stop offset="0.78125" stop-color="rgb(100%, 81.568909%, 55.686951%)" stop-opacity="1"/>
<stop offset="0.785156" stop-color="rgb(99.607849%, 81.176758%, 54.902649%)" stop-opacity="1"/>
<stop offset="0.789063" stop-color="rgb(99.607849%, 81.176758%, 54.510498%)" stop-opacity="1"/>
<stop offset="0.792969" stop-color="rgb(99.607849%, 80.784607%, 53.726196%)" stop-opacity="1"/>
<stop offset="0.796875" stop-color="rgb(99.607849%, 80.392456%, 52.941895%)" stop-opacity="1"/>
<stop offset="0.800781" stop-color="rgb(99.607849%, 80.000305%, 52.157593%)" stop-opacity="1"/>
<stop offset="0.804688" stop-color="rgb(99.607849%, 80.000305%, 51.765442%)" stop-opacity="1"/>
<stop offset="0.808594" stop-color="rgb(99.607849%, 79.608154%, 50.98114%)" stop-opacity="1"/>
<stop offset="0.8125" stop-color="rgb(99.607849%, 79.216003%, 50.196838%)" stop-opacity="1"/>
<stop offset="0.816406" stop-color="rgb(99.607849%, 78.823853%, 49.411011%)" stop-opacity="1"/>
<stop offset="0.820313" stop-color="rgb(99.607849%, 78.431702%, 48.626709%)" stop-opacity="1"/>
<stop offset="0.824219" stop-color="rgb(99.607849%, 78.039551%, 47.842407%)" stop-opacity="1"/>
<stop offset="0.828125" stop-color="rgb(99.607849%, 77.6474%, 47.058105%)" stop-opacity="1"/>
<stop offset="0.832031" stop-color="rgb(99.607849%, 77.6474%, 46.273804%)" stop-opacity="1"/>
<stop offset="0.835938" stop-color="rgb(99.607849%, 77.255249%, 45.881653%)" stop-opacity="1"/>
<stop offset="0.839844" stop-color="rgb(99.215698%, 76.863098%, 45.097351%)" stop-opacity="1"/>
<stop offset="0.84375" stop-color="rgb(99.215698%, 76.470947%, 44.313049%)" stop-opacity="1"/>
<stop offset="0.847656" stop-color="rgb(99.215698%, 76.470947%, 43.528748%)" stop-opacity="1"/>
<stop offset="0.851562" stop-color="rgb(99.215698%, 76.078796%, 42.744446%)" stop-opacity="1"/>
<stop offset="0.855469" stop-color="rgb(99.215698%, 75.686646%, 41.960144%)" stop-opacity="1"/>
<stop offset="0.859375" stop-color="rgb(99.215698%, 75.294495%, 41.175842%)" stop-opacity="1"/>
<stop offset="0.863281" stop-color="rgb(99.215698%, 74.902344%, 40.391541%)" stop-opacity="1"/>
<stop offset="0.867188" stop-color="rgb(99.215698%, 74.510193%, 39.607239%)" stop-opacity="1"/>
<stop offset="0.871094" stop-color="rgb(99.215698%, 74.510193%, 38.822937%)" stop-opacity="1"/>
<stop offset="0.875" stop-color="rgb(99.215698%, 74.118042%, 38.038635%)" stop-opacity="1"/>
<stop offset="0.878906" stop-color="rgb(99.215698%, 73.725891%, 37.254333%)" stop-opacity="1"/>
<stop offset="0.882813" stop-color="rgb(99.215698%, 73.725891%, 36.470032%)" stop-opacity="1"/>
<stop offset="0.886719" stop-color="rgb(99.215698%, 73.33374%, 35.293579%)" stop-opacity="1"/>
<stop offset="0.890625" stop-color="rgb(99.215698%, 72.941589%, 34.509277%)" stop-opacity="1"/>
<stop offset="0.894531" stop-color="rgb(99.215698%, 72.549438%, 33.724976%)" stop-opacity="1"/>
<stop offset="0.898438" stop-color="rgb(99.215698%, 72.549438%, 32.940674%)" stop-opacity="1"/>
<stop offset="0.902344" stop-color="rgb(98.823547%, 72.157288%, 32.156372%)" stop-opacity="1"/>
<stop offset="0.90625" stop-color="rgb(98.823547%, 71.765137%, 31.37207%)" stop-opacity="1"/>
<stop offset="0.910156" stop-color="rgb(98.823547%, 71.372986%, 30.195618%)" stop-opacity="1"/>
<stop offset="0.914063" stop-color="rgb(98.823547%, 70.980835%, 29.411316%)" stop-opacity="1"/>
<stop offset="0.917969" stop-color="rgb(98.823547%, 70.980835%, 28.627014%)" stop-opacity="1"/>
<stop offset="0.921875" stop-color="rgb(98.823547%, 70.588684%, 27.450562%)" stop-opacity="1"/>
<stop offset="0.925781" stop-color="rgb(98.823547%, 70.196533%, 26.66626%)" stop-opacity="1"/>
<stop offset="0.929688" stop-color="rgb(98.823547%, 69.804382%, 25.881958%)" stop-opacity="1"/>
<stop offset="0.933594" stop-color="rgb(98.823547%, 69.412231%, 24.705505%)" stop-opacity="1"/>
<stop offset="0.9375" stop-color="rgb(98.823547%, 69.412231%, 23.921204%)" stop-opacity="1"/>
<stop offset="0.941406" stop-color="rgb(98.431396%, 69.020081%, 23.136902%)" stop-opacity="1"/>
<stop offset="0.945313" stop-color="rgb(98.431396%, 69.020081%, 21.960449%)" stop-opacity="1"/>
<stop offset="0.949219" stop-color="rgb(98.431396%, 68.62793%, 21.176147%)" stop-opacity="1"/>
<stop offset="0.953125" stop-color="rgb(98.431396%, 68.235779%, 20.391846%)" stop-opacity="1"/>
<stop offset="0.957031" stop-color="rgb(98.431396%, 67.843628%, 19.215393%)" stop-opacity="1"/>
<stop offset="0.960938" stop-color="rgb(98.431396%, 67.451477%, 18.431091%)" stop-opacity="1"/>
<stop offset="0.964844" stop-color="rgb(98.431396%, 67.451477%, 17.64679%)" stop-opacity="1"/>
<stop offset="0.96875" stop-color="rgb(98.431396%, 67.059326%, 16.470337%)" stop-opacity="1"/>
<stop offset="0.972656" stop-color="rgb(98.431396%, 66.667175%, 15.686035%)" stop-opacity="1"/>
<stop offset="0.976562" stop-color="rgb(98.431396%, 66.667175%, 14.901733%)" stop-opacity="1"/>
<stop offset="0.980469" stop-color="rgb(98.039246%, 66.275024%, 13.725281%)" stop-opacity="1"/>
<stop offset="0.984375" stop-color="rgb(98.039246%, 65.882874%, 12.548828%)" stop-opacity="1"/>
<stop offset="0.988281" stop-color="rgb(98.039246%, 65.490723%, 11.764526%)" stop-opacity="1"/>
<stop offset="0.992188" stop-color="rgb(98.039246%, 65.490723%, 10.980225%)" stop-opacity="1"/>
<stop offset="0.996094" stop-color="rgb(98.039246%, 65.098572%, 10.588074%)" stop-opacity="1"/>
<stop offset="1" stop-color="rgb(98.039246%, 65.098572%, 10.195923%)" stop-opacity="1"/>
</linearGradient>
<clipPath id="clip-8">
<path clip-rule="nonzero" d="M 29 55 L 156 55 L 156 182 L 29 182 Z M 29 55 "/>
</clipPath>
<clipPath id="clip-9">
<path clip-rule="nonzero" d="M 29.234375 118.269531 C 29.234375 153.179688 57.53125 181.476562 92.4375 181.476562 C 127.347656 181.476562 155.644531 153.179688 155.644531 118.269531 C 155.644531 83.363281 127.347656 55.066406 92.4375 55.066406 C 57.53125 55.066406 29.234375 83.363281 29.234375 118.269531 "/>
</clipPath>
<radialGradient id="radial-pattern-2" gradientUnits="userSpaceOnUse" cx="0" cy="0" fx="0" fy="0" r="63.205723" gradientTransform="matrix(1, 0, 0, 1, 92.438751, 118.272285)">
<stop offset="0" stop-color="rgb(100%, 100%, 100%)" stop-opacity="1"/>
<stop offset="0.5" stop-color="rgb(100%, 100%, 100%)" stop-opacity="1"/>
<stop offset="0.625" stop-color="rgb(100%, 100%, 100%)" stop-opacity="1"/>
<stop offset="0.636719" stop-color="rgb(99.607849%, 99.607849%, 99.607849%)" stop-opacity="1"/>
<stop offset="0.640625" stop-color="rgb(98.431396%, 98.431396%, 99.215698%)" stop-opacity="1"/>
<stop offset="0.644531" stop-color="rgb(97.647095%, 97.647095%, 98.823547%)" stop-opacity="1"/>
<stop offset="0.648438" stop-color="rgb(96.862793%, 96.862793%, 98.431396%)" stop-opacity="1"/>
<stop offset="0.652344" stop-color="rgb(96.078491%, 96.470642%, 98.431396%)" stop-opacity="1"/>
<stop offset="0.65625" stop-color="rgb(95.294189%, 95.68634%, 98.039246%)" stop-opacity="1"/>
<stop offset="0.660156" stop-color="rgb(94.509888%, 94.902039%, 97.647095%)" stop-opacity="1"/>
<stop offset="0.664062" stop-color="rgb(94.117737%, 94.509888%, 97.647095%)" stop-opacity="1"/>
<stop offset="0.667969" stop-color="rgb(93.333435%, 93.725586%, 97.254944%)" stop-opacity="1"/>
<stop offset="0.671875" stop-color="rgb(92.941284%, 93.333435%, 96.862793%)" stop-opacity="1"/>
<stop offset="0.675781" stop-color="rgb(92.156982%, 92.941284%, 96.470642%)" stop-opacity="1"/>
<stop offset="0.679688" stop-color="rgb(91.372681%, 92.156982%, 96.470642%)" stop-opacity="1"/>
<stop offset="0.683594" stop-color="rgb(90.98053%, 91.764832%, 96.078491%)" stop-opacity="1"/>
<stop offset="0.6875" stop-color="rgb(90.588379%, 91.372681%, 95.68634%)" stop-opacity="1"/>
<stop offset="0.691406" stop-color="rgb(89.804077%, 90.588379%, 95.68634%)" stop-opacity="1"/>
<stop offset="0.695312" stop-color="rgb(89.411926%, 90.196228%, 95.294189%)" stop-opacity="1"/>
<stop offset="0.699219" stop-color="rgb(88.627625%, 89.411926%, 94.902039%)" stop-opacity="1"/>
<stop offset="0.703125" stop-color="rgb(87.843323%, 89.019775%, 94.902039%)" stop-opacity="1"/>
<stop offset="0.707031" stop-color="rgb(87.451172%, 88.627625%, 94.509888%)" stop-opacity="1"/>
<stop offset="0.710938" stop-color="rgb(86.66687%, 88.235474%, 94.117737%)" stop-opacity="1"/>
<stop offset="0.714844" stop-color="rgb(86.274719%, 87.843323%, 94.117737%)" stop-opacity="1"/>
<stop offset="0.71875" stop-color="rgb(85.490417%, 87.451172%, 93.725586%)" stop-opacity="1"/>
<stop offset="0.722656" stop-color="rgb(85.098267%, 86.66687%, 93.725586%)" stop-opacity="1"/>
<stop offset="0.726562" stop-color="rgb(84.313965%, 86.274719%, 93.333435%)" stop-opacity="1"/>
<stop offset="0.730469" stop-color="rgb(83.529663%, 85.490417%, 92.941284%)" stop-opacity="1"/>
<stop offset="0.734375" stop-color="rgb(83.137512%, 85.098267%, 92.941284%)" stop-opacity="1"/>
<stop offset="0.738281" stop-color="rgb(82.745361%, 84.706116%, 92.549133%)" stop-opacity="1"/>
<stop offset="0.742188" stop-color="rgb(81.96106%, 84.313965%, 92.156982%)" stop-opacity="1"/>
<stop offset="0.746094" stop-color="rgb(81.568909%, 83.921814%, 92.156982%)" stop-opacity="1"/>
<stop offset="0.75" stop-color="rgb(81.176758%, 83.529663%, 91.764832%)" stop-opacity="1"/>
<stop offset="0.753906" stop-color="rgb(80.392456%, 82.745361%, 91.764832%)" stop-opacity="1"/>
<stop offset="0.757812" stop-color="rgb(80.000305%, 82.35321%, 91.372681%)" stop-opacity="1"/>
<stop offset="0.761719" stop-color="rgb(79.216003%, 81.96106%, 90.98053%)" stop-opacity="1"/>
<stop offset="0.765625" stop-color="rgb(78.823853%, 81.568909%, 90.98053%)" stop-opacity="1"/>
<stop offset="0.769531" stop-color="rgb(78.431702%, 81.176758%, 90.588379%)" stop-opacity="1"/>
<stop offset="0.773438" stop-color="rgb(77.6474%, 80.392456%, 90.588379%)" stop-opacity="1"/>
<stop offset="0.777344" stop-color="rgb(77.255249%, 80.000305%, 90.196228%)" stop-opacity="1"/>
<stop offset="0.78125" stop-color="rgb(76.863098%, 79.608154%, 89.804077%)" stop-opacity="1"/>
<stop offset="0.785156" stop-color="rgb(76.078796%, 79.216003%, 89.804077%)" stop-opacity="1"/>
<stop offset="0.789062" stop-color="rgb(75.686646%, 78.823853%, 89.411926%)" stop-opacity="1"/>
<stop offset="0.792969" stop-color="rgb(75.294495%, 78.431702%, 89.411926%)" stop-opacity="1"/>
<stop offset="0.796875" stop-color="rgb(74.510193%, 77.6474%, 89.019775%)" stop-opacity="1"/>
<stop offset="0.800781" stop-color="rgb(74.118042%, 77.255249%, 89.019775%)" stop-opacity="1"/>
<stop offset="0.804688" stop-color="rgb(73.33374%, 76.863098%, 88.627625%)" stop-opacity="1"/>
<stop offset="0.808594" stop-color="rgb(72.941589%, 76.470947%, 88.235474%)" stop-opacity="1"/>
<stop offset="0.8125" stop-color="rgb(72.549438%, 76.078796%, 88.235474%)" stop-opacity="1"/>
<stop offset="0.816406" stop-color="rgb(71.765137%, 75.294495%, 87.843323%)" stop-opacity="1"/>
<stop offset="0.820312" stop-color="rgb(70.588684%, 74.510193%, 87.451172%)" stop-opacity="1"/>
<stop offset="0.824219" stop-color="rgb(69.804382%, 74.118042%, 87.059021%)" stop-opacity="1"/>
<stop offset="0.828125" stop-color="rgb(69.020081%, 73.33374%, 86.66687%)" stop-opacity="1"/>
<stop offset="0.832031" stop-color="rgb(67.843628%, 72.549438%, 86.274719%)" stop-opacity="1"/>
<stop offset="0.835938" stop-color="rgb(67.059326%, 71.765137%, 85.882568%)" stop-opacity="1"/>
<stop offset="0.839844" stop-color="rgb(66.275024%, 70.980835%, 85.490417%)" stop-opacity="1"/>
<stop offset="0.84375" stop-color="rgb(65.098572%, 70.196533%, 85.098267%)" stop-opacity="1"/>
<stop offset="0.847656" stop-color="rgb(64.31427%, 69.412231%, 84.706116%)" stop-opacity="1"/>
<stop offset="0.851562" stop-color="rgb(63.529968%, 68.62793%, 84.313965%)" stop-opacity="1"/>
<stop offset="0.855469" stop-color="rgb(62.353516%, 68.235779%, 83.921814%)" stop-opacity="1"/>
<stop offset="0.859375" stop-color="rgb(61.569214%, 67.451477%, 83.529663%)" stop-opacity="1"/>
<stop offset="0.863281" stop-color="rgb(60.784912%, 66.667175%, 83.137512%)" stop-opacity="1"/>
<stop offset="0.867188" stop-color="rgb(60.00061%, 65.882874%, 82.745361%)" stop-opacity="1"/>
<stop offset="0.871094" stop-color="rgb(59.216309%, 65.490723%, 82.35321%)" stop-opacity="1"/>
<stop offset="0.875" stop-color="rgb(58.039856%, 64.706421%, 81.96106%)" stop-opacity="1"/>
<stop offset="0.878906" stop-color="rgb(57.255554%, 63.922119%, 81.568909%)" stop-opacity="1"/>
<stop offset="0.882812" stop-color="rgb(56.471252%, 63.529968%, 81.176758%)" stop-opacity="1"/>
<stop offset="0.886719" stop-color="rgb(55.686951%, 62.745667%, 80.784607%)" stop-opacity="1"/>
<stop offset="0.890625" stop-color="rgb(54.902649%, 61.961365%, 80.784607%)" stop-opacity="1"/>
<stop offset="0.894531" stop-color="rgb(54.118347%, 61.569214%, 80.392456%)" stop-opacity="1"/>
<stop offset="0.898438" stop-color="rgb(53.334045%, 60.784912%, 80.000305%)" stop-opacity="1"/>
<stop offset="0.902344" stop-color="rgb(52.549744%, 60.00061%, 79.608154%)" stop-opacity="1"/>
<stop offset="0.90625" stop-color="rgb(51.373291%, 59.216309%, 79.216003%)" stop-opacity="1"/>
<stop offset="0.910156" stop-color="rgb(50.98114%, 58.824158%, 78.823853%)" stop-opacity="1"/>
<stop offset="0.914062" stop-color="rgb(49.803162%, 58.432007%, 78.431702%)" stop-opacity="1"/>
<stop offset="0.917969" stop-color="rgb(49.01886%, 57.647705%, 78.039551%)" stop-opacity="1"/>
<stop offset="0.921875" stop-color="rgb(48.626709%, 57.255554%, 77.6474%)" stop-opacity="1"/>
<stop offset="0.925781" stop-color="rgb(47.450256%, 56.471252%, 77.6474%)" stop-opacity="1"/>
<stop offset="0.929688" stop-color="rgb(47.058105%, 55.686951%, 77.255249%)" stop-opacity="1"/>
<stop offset="0.933594" stop-color="rgb(46.273804%, 55.2948%, 76.863098%)" stop-opacity="1"/>
<stop offset="0.9375" stop-color="rgb(45.489502%, 54.902649%, 76.470947%)" stop-opacity="1"/>
<stop offset="0.941406" stop-color="rgb(44.7052%, 54.118347%, 76.078796%)" stop-opacity="1"/>
<stop offset="0.945312" stop-color="rgb(43.920898%, 53.726196%, 75.686646%)" stop-opacity="1"/>
<stop offset="0.949219" stop-color="rgb(43.136597%, 52.941895%, 75.294495%)" stop-opacity="1"/>
<stop offset="0.953125" stop-color="rgb(42.352295%, 52.157593%, 74.902344%)" stop-opacity="1"/>
<stop offset="0.957031" stop-color="rgb(41.567993%, 51.765442%, 74.902344%)" stop-opacity="1"/>
<stop offset="0.960938" stop-color="rgb(40.783691%, 51.373291%, 74.510193%)" stop-opacity="1"/>
<stop offset="0.964844" stop-color="rgb(39.99939%, 50.588989%, 74.118042%)" stop-opacity="1"/>
<stop offset="0.96875" stop-color="rgb(39.215088%, 50.196838%, 73.725891%)" stop-opacity="1"/>
<stop offset="0.972656" stop-color="rgb(38.430786%, 49.803162%, 73.725891%)" stop-opacity="1"/>
<stop offset="0.976562" stop-color="rgb(37.646484%, 49.411011%, 73.33374%)" stop-opacity="1"/>
<stop offset="0.980469" stop-color="rgb(36.862183%, 49.01886%, 72.941589%)" stop-opacity="1"/>
<stop offset="0.984375" stop-color="rgb(36.077881%, 48.626709%, 72.549438%)" stop-opacity="1"/>
<stop offset="0.988281" stop-color="rgb(35.293579%, 47.842407%, 72.157288%)" stop-opacity="1"/>
<stop offset="0.992188" stop-color="rgb(34.117126%, 47.450256%, 72.157288%)" stop-opacity="1"/>
<stop offset="0.996094" stop-color="rgb(33.332825%, 47.058105%, 71.765137%)" stop-opacity="1"/>
<stop offset="1" stop-color="rgb(32.940674%, 46.665955%, 71.765137%)" stop-opacity="1"/>
</radialGradient>
<clipPath id="clip-10">
<path clip-rule="nonzero" d="M 12 123 L 172 123 L 172 166 L 12 166 Z M 12 123 "/>
</clipPath>
<clipPath id="clip-11">
<path clip-rule="nonzero" d="M 72.644531 137.078125 C 19.519531 141.890625 12.265625 130.570312 12.265625 130.570312 L 15.449219 157.609375 C 15.449219 157.609375 21.949219 165.25 46.6875 165.808594 C 68.601562 166.304688 91.996094 161.996094 111.6875 157.6875 C 132.367188 153.164062 148.960938 148.640625 155.449219 149.679688 C 166.828125 151.503906 166.625 156.992188 166.464844 158.09375 L 171.503906 130.84375 C 171.503906 130.84375 165.691406 123.996094 155.730469 123.660156 C 155.574219 123.65625 155.410156 123.652344 155.242188 123.652344 C 144.523438 123.652344 104.980469 134.148438 72.644531 137.078125 M 166.429688 158.289062 C 166.429688 158.289062 166.445312 158.21875 166.464844 158.09375 Z M 166.429688 158.289062 "/>
</clipPath>
<linearGradient id="linear-pattern-2" gradientUnits="userSpaceOnUse" x1="-0.0000022388" y1="0" x2="1.000002" y2="0" gradientTransform="matrix(159.237289, 0, 0, 159.237289, 12.266357, 144.749512)">
<stop offset="0" stop-color="rgb(98.039246%, 65.098572%, 10.195923%)" stop-opacity="1"/>
<stop offset="0.00390625" stop-color="rgb(97.647095%, 65.098572%, 10.588074%)" stop-opacity="1"/>
<stop offset="0.0078125" stop-color="rgb(97.254944%, 64.706421%, 11.372375%)" stop-opacity="1"/>
<stop offset="0.0117188" stop-color="rgb(96.470642%, 64.706421%, 11.764526%)" stop-opacity="1"/>
<stop offset="0.015625" stop-color="rgb(96.078491%, 64.31427%, 12.548828%)" stop-opacity="1"/>
<stop offset="0.0195313" stop-color="rgb(95.68634%, 64.31427%, 12.940979%)" stop-opacity="1"/>
<stop offset="0.0234375" stop-color="rgb(95.294189%, 63.922119%, 13.33313%)" stop-opacity="1"/>
<stop offset="0.0273438" stop-color="rgb(94.902039%, 63.922119%, 14.117432%)" stop-opacity="1"/>
<stop offset="0.03125" stop-color="rgb(94.509888%, 63.529968%, 14.509583%)" stop-opacity="1"/>
<stop offset="0.0351563" stop-color="rgb(94.117737%, 63.529968%, 14.901733%)" stop-opacity="1"/>
<stop offset="0.0390625" stop-color="rgb(93.725586%, 63.137817%, 15.293884%)" stop-opacity="1"/>
<stop offset="0.0429688" stop-color="rgb(93.333435%, 63.137817%, 16.078186%)" stop-opacity="1"/>
<stop offset="0.046875" stop-color="rgb(92.941284%, 62.745667%, 16.470337%)" stop-opacity="1"/>
<stop offset="0.0507812" stop-color="rgb(92.549133%, 62.745667%, 16.862488%)" stop-opacity="1"/>
<stop offset="0.0546875" stop-color="rgb(92.156982%, 62.353516%, 17.254639%)" stop-opacity="1"/>
<stop offset="0.0585938" stop-color="rgb(91.764832%, 62.353516%, 18.03894%)" stop-opacity="1"/>
<stop offset="0.0625" stop-color="rgb(91.372681%, 61.961365%, 18.431091%)" stop-opacity="1"/>
<stop offset="0.0664063" stop-color="rgb(90.98053%, 61.961365%, 18.823242%)" stop-opacity="1"/>
<stop offset="0.0703125" stop-color="rgb(90.98053%, 61.569214%, 19.215393%)" stop-opacity="1"/>
<stop offset="0.0742188" stop-color="rgb(90.588379%, 61.569214%, 19.607544%)" stop-opacity="1"/>
<stop offset="0.078125" stop-color="rgb(90.196228%, 61.177063%, 19.999695%)" stop-opacity="1"/>
<stop offset="0.0820313" stop-color="rgb(89.804077%, 61.177063%, 20.391846%)" stop-opacity="1"/>
<stop offset="0.0859375" stop-color="rgb(89.411926%, 61.177063%, 20.783997%)" stop-opacity="1"/>
<stop offset="0.0898438" stop-color="rgb(89.019775%, 60.784912%, 21.176147%)" stop-opacity="1"/>
<stop offset="0.09375" stop-color="rgb(88.627625%, 60.784912%, 21.568298%)" stop-opacity="1"/>
<stop offset="0.0976563" stop-color="rgb(88.627625%, 60.784912%, 21.960449%)" stop-opacity="1"/>
<stop offset="0.101563" stop-color="rgb(88.235474%, 60.392761%, 22.3526%)" stop-opacity="1"/>
<stop offset="0.105469" stop-color="rgb(87.843323%, 60.392761%, 22.744751%)" stop-opacity="1"/>
<stop offset="0.109375" stop-color="rgb(87.451172%, 60.00061%, 23.136902%)" stop-opacity="1"/>
<stop offset="0.113281" stop-color="rgb(87.059021%, 60.00061%, 23.136902%)" stop-opacity="1"/>
<stop offset="0.117188" stop-color="rgb(86.66687%, 59.608459%, 23.529053%)" stop-opacity="1"/>
<stop offset="0.121094" stop-color="rgb(86.274719%, 59.608459%, 23.921204%)" stop-opacity="1"/>
<stop offset="0.125" stop-color="rgb(85.882568%, 59.216309%, 24.313354%)" stop-opacity="1"/>
<stop offset="0.128906" stop-color="rgb(85.882568%, 59.216309%, 24.705505%)" stop-opacity="1"/>
<stop offset="0.132813" stop-color="rgb(85.490417%, 59.216309%, 25.097656%)" stop-opacity="1"/>
<stop offset="0.136719" stop-color="rgb(85.098267%, 58.824158%, 25.489807%)" stop-opacity="1"/>
<stop offset="0.140625" stop-color="rgb(84.706116%, 58.824158%, 25.881958%)" stop-opacity="1"/>
<stop offset="0.144531" stop-color="rgb(84.313965%, 58.824158%, 25.881958%)" stop-opacity="1"/>
<stop offset="0.148438" stop-color="rgb(83.921814%, 58.432007%, 26.274109%)" stop-opacity="1"/>
<stop offset="0.152344" stop-color="rgb(83.529663%, 58.432007%, 26.66626%)" stop-opacity="1"/>
<stop offset="0.15625" stop-color="rgb(83.137512%, 58.039856%, 27.058411%)" stop-opacity="1"/>
<stop offset="0.160156" stop-color="rgb(83.137512%, 58.039856%, 27.450562%)" stop-opacity="1"/>
<stop offset="0.164063" stop-color="rgb(82.745361%, 57.647705%, 27.842712%)" stop-opacity="1"/>
<stop offset="0.167969" stop-color="rgb(82.35321%, 57.647705%, 27.842712%)" stop-opacity="1"/>
<stop offset="0.171875" stop-color="rgb(81.96106%, 57.255554%, 28.234863%)" stop-opacity="1"/>
<stop offset="0.175781" stop-color="rgb(81.568909%, 57.255554%, 28.627014%)" stop-opacity="1"/>
<stop offset="0.179688" stop-color="rgb(81.568909%, 57.255554%, 29.019165%)" stop-opacity="1"/>
<stop offset="0.183594" stop-color="rgb(81.176758%, 56.863403%, 29.411316%)" stop-opacity="1"/>
<stop offset="0.1875" stop-color="rgb(80.784607%, 56.863403%, 29.803467%)" stop-opacity="1"/>
<stop offset="0.191406" stop-color="rgb(80.392456%, 56.863403%, 29.803467%)" stop-opacity="1"/>
<stop offset="0.195312" stop-color="rgb(80.000305%, 56.471252%, 30.195618%)" stop-opacity="1"/>
<stop offset="0.199219" stop-color="rgb(79.608154%, 56.471252%, 30.587769%)" stop-opacity="1"/>
<stop offset="0.203125" stop-color="rgb(79.216003%, 56.079102%, 30.979919%)" stop-opacity="1"/>
<stop offset="0.207031" stop-color="rgb(79.216003%, 56.079102%, 30.979919%)" stop-opacity="1"/>
<stop offset="0.210938" stop-color="rgb(78.823853%, 56.079102%, 31.37207%)" stop-opacity="1"/>
<stop offset="0.214844" stop-color="rgb(78.431702%, 55.686951%, 31.764221%)" stop-opacity="1"/>
<stop offset="0.21875" stop-color="rgb(78.039551%, 55.686951%, 32.156372%)" stop-opacity="1"/>
<stop offset="0.222656" stop-color="rgb(77.6474%, 55.686951%, 32.156372%)" stop-opacity="1"/>
<stop offset="0.226562" stop-color="rgb(77.255249%, 55.2948%, 32.548523%)" stop-opacity="1"/>
<stop offset="0.230469" stop-color="rgb(76.863098%, 55.2948%, 32.548523%)" stop-opacity="1"/>
<stop offset="0.234375" stop-color="rgb(76.863098%, 54.902649%, 32.940674%)" stop-opacity="1"/>
<stop offset="0.238281" stop-color="rgb(76.470947%, 54.902649%, 32.940674%)" stop-opacity="1"/>
<stop offset="0.242188" stop-color="rgb(76.078796%, 54.902649%, 33.332825%)" stop-opacity="1"/>
<stop offset="0.246094" stop-color="rgb(75.686646%, 54.510498%, 33.724976%)" stop-opacity="1"/>
<stop offset="0.25" stop-color="rgb(75.294495%, 54.510498%, 33.724976%)" stop-opacity="1"/>
<stop offset="0.253906" stop-color="rgb(74.902344%, 54.510498%, 34.117126%)" stop-opacity="1"/>
<stop offset="0.257812" stop-color="rgb(74.510193%, 54.118347%, 34.509277%)" stop-opacity="1"/>
<stop offset="0.261719" stop-color="rgb(74.118042%, 54.118347%, 34.901428%)" stop-opacity="1"/>
<stop offset="0.265625" stop-color="rgb(74.118042%, 53.726196%, 34.901428%)" stop-opacity="1"/>
<stop offset="0.269531" stop-color="rgb(73.725891%, 53.726196%, 35.293579%)" stop-opacity="1"/>
<stop offset="0.273438" stop-color="rgb(73.33374%, 53.726196%, 35.68573%)" stop-opacity="1"/>
<stop offset="0.277344" stop-color="rgb(72.941589%, 53.334045%, 35.68573%)" stop-opacity="1"/>
<stop offset="0.28125" stop-color="rgb(72.941589%, 53.334045%, 36.077881%)" stop-opacity="1"/>
<stop offset="0.285156" stop-color="rgb(72.549438%, 53.334045%, 36.077881%)" stop-opacity="1"/>
<stop offset="0.289062" stop-color="rgb(72.157288%, 52.941895%, 36.470032%)" stop-opacity="1"/>
<stop offset="0.292969" stop-color="rgb(71.765137%, 52.941895%, 36.862183%)" stop-opacity="1"/>
<stop offset="0.296875" stop-color="rgb(71.372986%, 52.549744%, 36.862183%)" stop-opacity="1"/>
<stop offset="0.300781" stop-color="rgb(70.980835%, 52.549744%, 37.254333%)" stop-opacity="1"/>
<stop offset="0.304688" stop-color="rgb(70.588684%, 52.549744%, 37.646484%)" stop-opacity="1"/>
<stop offset="0.308594" stop-color="rgb(70.196533%, 52.157593%, 37.646484%)" stop-opacity="1"/>
<stop offset="0.3125" stop-color="rgb(70.196533%, 52.157593%, 38.038635%)" stop-opacity="1"/>
<stop offset="0.316406" stop-color="rgb(69.804382%, 52.157593%, 38.038635%)" stop-opacity="1"/>
<stop offset="0.320312" stop-color="rgb(69.412231%, 51.765442%, 38.430786%)" stop-opacity="1"/>
<stop offset="0.324219" stop-color="rgb(69.020081%, 51.765442%, 38.430786%)" stop-opacity="1"/>
<stop offset="0.328125" stop-color="rgb(68.62793%, 51.373291%, 38.822937%)" stop-opacity="1"/>
<stop offset="0.332031" stop-color="rgb(68.235779%, 51.373291%, 38.822937%)" stop-opacity="1"/>
<stop offset="0.335938" stop-color="rgb(67.843628%, 51.373291%, 38.822937%)" stop-opacity="1"/>
<stop offset="0.339844" stop-color="rgb(67.843628%, 51.373291%, 39.215088%)" stop-opacity="1"/>
<stop offset="0.34375" stop-color="rgb(67.451477%, 50.98114%, 39.215088%)" stop-opacity="1"/>
<stop offset="0.347656" stop-color="rgb(67.059326%, 50.98114%, 39.607239%)" stop-opacity="1"/>
<stop offset="0.351562" stop-color="rgb(66.667175%, 50.98114%, 39.607239%)" stop-opacity="1"/>
<stop offset="0.355469" stop-color="rgb(66.667175%, 50.98114%, 39.99939%)" stop-opacity="1"/>
<stop offset="0.359375" stop-color="rgb(66.275024%, 50.588989%, 39.99939%)" stop-opacity="1"/>
<stop offset="0.363281" stop-color="rgb(65.882874%, 50.588989%, 40.391541%)" stop-opacity="1"/>
<stop offset="0.367188" stop-color="rgb(65.490723%, 50.588989%, 40.391541%)" stop-opacity="1"/>
<stop offset="0.371094" stop-color="rgb(65.098572%, 50.196838%, 40.783691%)" stop-opacity="1"/>
<stop offset="0.375" stop-color="rgb(64.706421%, 50.196838%, 40.783691%)" stop-opacity="1"/>
<stop offset="0.378906" stop-color="rgb(64.31427%, 49.803162%, 41.175842%)" stop-opacity="1"/>
<stop offset="0.382812" stop-color="rgb(63.922119%, 49.803162%, 41.175842%)" stop-opacity="1"/>
<stop offset="0.386719" stop-color="rgb(63.922119%, 49.803162%, 41.567993%)" stop-opacity="1"/>
<stop offset="0.390625" stop-color="rgb(63.529968%, 49.411011%, 41.567993%)" stop-opacity="1"/>
<stop offset="0.394531" stop-color="rgb(63.137817%, 49.411011%, 41.960144%)" stop-opacity="1"/>
<stop offset="0.398437" stop-color="rgb(62.745667%, 49.411011%, 41.960144%)" stop-opacity="1"/>
<stop offset="0.402344" stop-color="rgb(62.745667%, 49.411011%, 42.352295%)" stop-opacity="1"/>
<stop offset="0.40625" stop-color="rgb(62.353516%, 49.01886%, 42.352295%)" stop-opacity="1"/>
<stop offset="0.410156" stop-color="rgb(61.961365%, 49.01886%, 42.744446%)" stop-opacity="1"/>
<stop offset="0.414062" stop-color="rgb(61.569214%, 49.01886%, 42.744446%)" stop-opacity="1"/>
<stop offset="0.417969" stop-color="rgb(61.177063%, 48.626709%, 43.136597%)" stop-opacity="1"/>
<stop offset="0.421875" stop-color="rgb(60.784912%, 48.626709%, 43.136597%)" stop-opacity="1"/>
<stop offset="0.425781" stop-color="rgb(60.392761%, 48.234558%, 43.528748%)" stop-opacity="1"/>
<stop offset="0.429687" stop-color="rgb(60.00061%, 48.234558%, 43.528748%)" stop-opacity="1"/>
<stop offset="0.433594" stop-color="rgb(60.00061%, 48.234558%, 43.528748%)" stop-opacity="1"/>
<stop offset="0.4375" stop-color="rgb(59.608459%, 47.842407%, 43.920898%)" stop-opacity="1"/>
<stop offset="0.441406" stop-color="rgb(59.216309%, 47.842407%, 43.920898%)" stop-opacity="1"/>
<stop offset="0.445312" stop-color="rgb(59.216309%, 47.842407%, 43.920898%)" stop-opacity="1"/>
<stop offset="0.449219" stop-color="rgb(58.824158%, 47.842407%, 44.313049%)" stop-opacity="1"/>
<stop offset="0.453125" stop-color="rgb(58.432007%, 47.450256%, 44.313049%)" stop-opacity="1"/>
<stop offset="0.457031" stop-color="rgb(58.039856%, 47.450256%, 44.7052%)" stop-opacity="1"/>
<stop offset="0.460937" stop-color="rgb(58.039856%, 47.450256%, 44.7052%)" stop-opacity="1"/>
<stop offset="0.464844" stop-color="rgb(57.647705%, 47.450256%, 45.097351%)" stop-opacity="1"/>
<stop offset="0.46875" stop-color="rgb(57.255554%, 47.058105%, 45.097351%)" stop-opacity="1"/>
<stop offset="0.472656" stop-color="rgb(56.863403%, 47.058105%, 45.489502%)" stop-opacity="1"/>
<stop offset="0.476562" stop-color="rgb(56.471252%, 47.058105%, 45.489502%)" stop-opacity="1"/>
<stop offset="0.480469" stop-color="rgb(56.079102%, 46.665955%, 45.489502%)" stop-opacity="1"/>
<stop offset="0.484375" stop-color="rgb(55.686951%, 46.665955%, 45.881653%)" stop-opacity="1"/>
<stop offset="0.488281" stop-color="rgb(55.686951%, 46.273804%, 45.881653%)" stop-opacity="1"/>
<stop offset="0.492187" stop-color="rgb(55.2948%, 46.273804%, 45.881653%)" stop-opacity="1"/>
<stop offset="0.496094" stop-color="rgb(54.902649%, 46.273804%, 46.273804%)" stop-opacity="1"/>
<stop offset="0.5" stop-color="rgb(54.510498%, 45.881653%, 46.273804%)" stop-opacity="1"/>
<stop offset="0.503906" stop-color="rgb(54.118347%, 45.881653%, 46.665955%)" stop-opacity="1"/>
<stop offset="0.507813" stop-color="rgb(54.118347%, 45.881653%, 46.665955%)" stop-opacity="1"/>
<stop offset="0.511719" stop-color="rgb(53.726196%, 45.881653%, 46.665955%)" stop-opacity="1"/>
<stop offset="0.515625" stop-color="rgb(53.334045%, 45.489502%, 47.058105%)" stop-opacity="1"/>
<stop offset="0.519531" stop-color="rgb(52.941895%, 45.489502%, 47.058105%)" stop-opacity="1"/>
<stop offset="0.523438" stop-color="rgb(52.941895%, 45.489502%, 47.058105%)" stop-opacity="1"/>
<stop offset="0.527344" stop-color="rgb(52.549744%, 45.489502%, 47.450256%)" stop-opacity="1"/>
<stop offset="0.53125" stop-color="rgb(52.157593%, 45.097351%, 47.450256%)" stop-opacity="1"/>
<stop offset="0.535156" stop-color="rgb(51.765442%, 45.097351%, 47.842407%)" stop-opacity="1"/>
<stop offset="0.539063" stop-color="rgb(51.765442%, 45.097351%, 47.842407%)" stop-opacity="1"/>
<stop offset="0.542969" stop-color="rgb(51.373291%, 45.097351%, 48.234558%)" stop-opacity="1"/>
<stop offset="0.546875" stop-color="rgb(50.98114%, 44.7052%, 48.234558%)" stop-opacity="1"/>
<stop offset="0.550781" stop-color="rgb(50.588989%, 44.7052%, 48.626709%)" stop-opacity="1"/>
<stop offset="0.554688" stop-color="rgb(50.196838%, 44.7052%, 48.626709%)" stop-opacity="1"/>
<stop offset="0.558594" stop-color="rgb(49.803162%, 44.313049%, 48.626709%)" stop-opacity="1"/>
<stop offset="0.5625" stop-color="rgb(49.411011%, 44.313049%, 49.01886%)" stop-opacity="1"/>
<stop offset="0.566406" stop-color="rgb(49.411011%, 43.920898%, 49.01886%)" stop-opacity="1"/>
<stop offset="0.570313" stop-color="rgb(49.01886%, 43.920898%, 49.01886%)" stop-opacity="1"/>
<stop offset="0.574219" stop-color="rgb(48.626709%, 43.920898%, 49.411011%)" stop-opacity="1"/>
<stop offset="0.578125" stop-color="rgb(48.234558%, 43.528748%, 49.411011%)" stop-opacity="1"/>
<stop offset="0.582031" stop-color="rgb(48.234558%, 43.528748%, 49.803162%)" stop-opacity="1"/>
<stop offset="0.585938" stop-color="rgb(47.842407%, 43.528748%, 49.803162%)" stop-opacity="1"/>
<stop offset="0.589844" stop-color="rgb(47.450256%, 43.528748%, 49.803162%)" stop-opacity="1"/>
<stop offset="0.59375" stop-color="rgb(47.058105%, 43.136597%, 50.196838%)" stop-opacity="1"/>
<stop offset="0.597656" stop-color="rgb(47.058105%, 43.136597%, 50.196838%)" stop-opacity="1"/>
<stop offset="0.601563" stop-color="rgb(46.665955%, 43.136597%, 50.196838%)" stop-opacity="1"/>
<stop offset="0.605469" stop-color="rgb(46.273804%, 43.136597%, 50.588989%)" stop-opacity="1"/>
<stop offset="0.609375" stop-color="rgb(45.881653%, 42.744446%, 50.588989%)" stop-opacity="1"/>
<stop offset="0.613281" stop-color="rgb(45.489502%, 42.744446%, 50.98114%)" stop-opacity="1"/>
<stop offset="0.617188" stop-color="rgb(45.097351%, 42.744446%, 50.98114%)" stop-opacity="1"/>
<stop offset="0.621094" stop-color="rgb(44.7052%, 42.744446%, 50.98114%)" stop-opacity="1"/>
<stop offset="0.625" stop-color="rgb(44.313049%, 42.352295%, 51.373291%)" stop-opacity="1"/>
<stop offset="0.628906" stop-color="rgb(44.313049%, 42.352295%, 51.373291%)" stop-opacity="1"/>
<stop offset="0.632813" stop-color="rgb(43.920898%, 42.352295%, 51.373291%)" stop-opacity="1"/>
<stop offset="0.636719" stop-color="rgb(43.528748%, 42.352295%, 51.765442%)" stop-opacity="1"/>
<stop offset="0.640625" stop-color="rgb(43.136597%, 41.960144%, 51.765442%)" stop-opacity="1"/>
<stop offset="0.644531" stop-color="rgb(43.136597%, 41.960144%, 52.157593%)" stop-opacity="1"/>
<stop offset="0.648438" stop-color="rgb(42.744446%, 41.960144%, 52.157593%)" stop-opacity="1"/>
<stop offset="0.652344" stop-color="rgb(42.352295%, 41.960144%, 52.157593%)" stop-opacity="1"/>
<stop offset="0.65625" stop-color="rgb(41.960144%, 41.567993%, 52.549744%)" stop-opacity="1"/>
<stop offset="0.660156" stop-color="rgb(41.567993%, 41.567993%, 52.549744%)" stop-opacity="1"/>
<stop offset="0.664063" stop-color="rgb(41.175842%, 41.567993%, 52.549744%)" stop-opacity="1"/>
<stop offset="0.667969" stop-color="rgb(40.783691%, 41.567993%, 52.549744%)" stop-opacity="1"/>
<stop offset="0.671875" stop-color="rgb(40.783691%, 41.567993%, 52.549744%)" stop-opacity="1"/>
<stop offset="0.675781" stop-color="rgb(40.391541%, 41.175842%, 52.941895%)" stop-opacity="1"/>
<stop offset="0.679688" stop-color="rgb(39.99939%, 41.175842%, 52.941895%)" stop-opacity="1"/>
<stop offset="0.683594" stop-color="rgb(39.607239%, 41.175842%, 52.941895%)" stop-opacity="1"/>
<stop offset="0.6875" stop-color="rgb(39.215088%, 41.175842%, 53.334045%)" stop-opacity="1"/>
<stop offset="0.691406" stop-color="rgb(38.822937%, 40.783691%, 53.334045%)" stop-opacity="1"/>
<stop offset="0.695313" stop-color="rgb(38.430786%, 40.783691%, 53.726196%)" stop-opacity="1"/>
<stop offset="0.699219" stop-color="rgb(38.038635%, 40.783691%, 53.726196%)" stop-opacity="1"/>
<stop offset="0.703125" stop-color="rgb(37.646484%, 40.783691%, 53.726196%)" stop-opacity="1"/>
<stop offset="0.707031" stop-color="rgb(37.254333%, 40.391541%, 54.118347%)" stop-opacity="1"/>
<stop offset="0.710938" stop-color="rgb(36.862183%, 40.391541%, 54.118347%)" stop-opacity="1"/>
<stop offset="0.714844" stop-color="rgb(36.470032%, 40.391541%, 54.118347%)" stop-opacity="1"/>
<stop offset="0.71875" stop-color="rgb(36.470032%, 40.391541%, 54.510498%)" stop-opacity="1"/>
<stop offset="0.722656" stop-color="rgb(36.077881%, 39.99939%, 54.510498%)" stop-opacity="1"/>
<stop offset="0.726563" stop-color="rgb(35.68573%, 39.99939%, 54.902649%)" stop-opacity="1"/>
<stop offset="0.730469" stop-color="rgb(35.293579%, 39.99939%, 54.902649%)" stop-opacity="1"/>
<stop offset="0.734375" stop-color="rgb(34.901428%, 39.99939%, 54.902649%)" stop-opacity="1"/>
<stop offset="0.738281" stop-color="rgb(34.509277%, 39.607239%, 55.2948%)" stop-opacity="1"/>
<stop offset="0.742188" stop-color="rgb(34.117126%, 39.607239%, 55.2948%)" stop-opacity="1"/>
<stop offset="0.746094" stop-color="rgb(33.724976%, 39.607239%, 55.2948%)" stop-opacity="1"/>
<stop offset="0.75" stop-color="rgb(33.332825%, 39.607239%, 55.2948%)" stop-opacity="1"/>
<stop offset="0.753906" stop-color="rgb(32.940674%, 39.215088%, 55.686951%)" stop-opacity="1"/>
<stop offset="0.757813" stop-color="rgb(32.548523%, 39.215088%, 55.686951%)" stop-opacity="1"/>
<stop offset="0.761719" stop-color="rgb(32.156372%, 39.215088%, 55.686951%)" stop-opacity="1"/>
<stop offset="0.765625" stop-color="rgb(31.764221%, 39.215088%, 56.079102%)" stop-opacity="1"/>
<stop offset="0.769531" stop-color="rgb(31.37207%, 38.822937%, 56.079102%)" stop-opacity="1"/>
<stop offset="0.773438" stop-color="rgb(30.979919%, 38.822937%, 56.471252%)" stop-opacity="1"/>
<stop offset="0.777344" stop-color="rgb(30.587769%, 38.822937%, 56.471252%)" stop-opacity="1"/>
<stop offset="0.78125" stop-color="rgb(30.195618%, 38.822937%, 56.471252%)" stop-opacity="1"/>
<stop offset="0.785156" stop-color="rgb(29.803467%, 38.430786%, 56.863403%)" stop-opacity="1"/>
<stop offset="0.789063" stop-color="rgb(29.019165%, 38.430786%, 56.863403%)" stop-opacity="1"/>
<stop offset="0.792969" stop-color="rgb(28.627014%, 38.430786%, 56.863403%)" stop-opacity="1"/>
<stop offset="0.796875" stop-color="rgb(28.234863%, 38.430786%, 56.863403%)" stop-opacity="1"/>
<stop offset="0.800781" stop-color="rgb(27.842712%, 38.038635%, 57.255554%)" stop-opacity="1"/>
<stop offset="0.804688" stop-color="rgb(27.450562%, 38.038635%, 57.255554%)" stop-opacity="1"/>
<stop offset="0.808594" stop-color="rgb(27.058411%, 38.038635%, 57.255554%)" stop-opacity="1"/>
<stop offset="0.8125" stop-color="rgb(26.66626%, 38.038635%, 57.255554%)" stop-opacity="1"/>
<stop offset="0.816406" stop-color="rgb(26.274109%, 37.646484%, 57.647705%)" stop-opacity="1"/>
<stop offset="0.820313" stop-color="rgb(25.881958%, 37.646484%, 57.647705%)" stop-opacity="1"/>
<stop offset="0.824219" stop-color="rgb(25.489807%, 37.646484%, 57.647705%)" stop-opacity="1"/>
<stop offset="0.828125" stop-color="rgb(25.097656%, 37.646484%, 58.039856%)" stop-opacity="1"/>
<stop offset="0.832031" stop-color="rgb(24.705505%, 37.254333%, 58.039856%)" stop-opacity="1"/>
<stop offset="0.835938" stop-color="rgb(24.313354%, 37.254333%, 58.432007%)" stop-opacity="1"/>
<stop offset="0.839844" stop-color="rgb(23.529053%, 37.254333%, 58.432007%)" stop-opacity="1"/>
<stop offset="0.84375" stop-color="rgb(23.136902%, 37.254333%, 58.432007%)" stop-opacity="1"/>
<stop offset="0.847656" stop-color="rgb(22.744751%, 36.862183%, 58.824158%)" stop-opacity="1"/>
<stop offset="0.851563" stop-color="rgb(22.3526%, 36.862183%, 58.824158%)" stop-opacity="1"/>
<stop offset="0.855469" stop-color="rgb(21.568298%, 36.862183%, 58.824158%)" stop-opacity="1"/>
<stop offset="0.859375" stop-color="rgb(21.176147%, 36.862183%, 58.824158%)" stop-opacity="1"/>
<stop offset="0.863281" stop-color="rgb(20.391846%, 36.470032%, 59.216309%)" stop-opacity="1"/>
<stop offset="0.867188" stop-color="rgb(19.999695%, 36.470032%, 59.216309%)" stop-opacity="1"/>
<stop offset="0.871094" stop-color="rgb(19.607544%, 36.470032%, 59.216309%)" stop-opacity="1"/>
<stop offset="0.875" stop-color="rgb(18.823242%, 36.470032%, 59.216309%)" stop-opacity="1"/>
<stop offset="0.878906" stop-color="rgb(18.431091%, 36.077881%, 59.608459%)" stop-opacity="1"/>
<stop offset="0.882813" stop-color="rgb(18.03894%, 36.077881%, 59.608459%)" stop-opacity="1"/>
<stop offset="0.886719" stop-color="rgb(17.254639%, 36.077881%, 59.608459%)" stop-opacity="1"/>
<stop offset="0.890625" stop-color="rgb(16.862488%, 36.077881%, 60.00061%)" stop-opacity="1"/>
<stop offset="0.894531" stop-color="rgb(16.078186%, 35.68573%, 60.00061%)" stop-opacity="1"/>
<stop offset="0.898438" stop-color="rgb(15.293884%, 35.68573%, 60.392761%)" stop-opacity="1"/>
<stop offset="0.902344" stop-color="rgb(14.509583%, 35.68573%, 60.392761%)" stop-opacity="1"/>
<stop offset="0.90625" stop-color="rgb(13.725281%, 35.68573%, 60.392761%)" stop-opacity="1"/>
<stop offset="0.910156" stop-color="rgb(12.940979%, 35.293579%, 60.784912%)" stop-opacity="1"/>
<stop offset="0.914063" stop-color="rgb(12.548828%, 35.293579%, 60.784912%)" stop-opacity="1"/>
<stop offset="0.917969" stop-color="rgb(11.372375%, 35.293579%, 60.784912%)" stop-opacity="1"/>
<stop offset="0.921875" stop-color="rgb(10.588074%, 35.293579%, 60.784912%)" stop-opacity="1"/>
<stop offset="0.925781" stop-color="rgb(9.411621%, 34.901428%, 61.177063%)" stop-opacity="1"/>
<stop offset="0.929688" stop-color="rgb(8.627319%, 34.901428%, 61.177063%)" stop-opacity="1"/>
<stop offset="0.933594" stop-color="rgb(7.450867%, 34.901428%, 61.177063%)" stop-opacity="1"/>
<stop offset="0.9375" stop-color="rgb(6.274414%, 34.901428%, 61.177063%)" stop-opacity="1"/>
<stop offset="0.941406" stop-color="rgb(4.705811%, 34.509277%, 61.569214%)" stop-opacity="1"/>
<stop offset="0.945313" stop-color="rgb(3.137207%, 34.509277%, 61.569214%)" stop-opacity="1"/>
<stop offset="0.949219" stop-color="rgb(2.352905%, 34.509277%, 61.569214%)" stop-opacity="1"/>
<stop offset="0.953125" stop-color="rgb(1.568604%, 34.509277%, 61.569214%)" stop-opacity="1"/>
<stop offset="0.957031" stop-color="rgb(0.784302%, 34.117126%, 61.961365%)" stop-opacity="1"/>
<stop offset="0.960938" stop-color="rgb(0.392151%, 34.117126%, 61.961365%)" stop-opacity="1"/>
<stop offset="0.964844" stop-color="rgb(0.392151%, 34.117126%, 61.961365%)" stop-opacity="1"/>
<stop offset="0.96875" stop-color="rgb(0%, 34.117126%, 62.353516%)" stop-opacity="1"/>
<stop offset="0.976563" stop-color="rgb(0%, 34.117126%, 62.353516%)" stop-opacity="1"/>
<stop offset="0.984375" stop-color="rgb(0%, 34.117126%, 62.745667%)" stop-opacity="1"/>
<stop offset="0.992188" stop-color="rgb(0%, 33.724976%, 63.137817%)" stop-opacity="1"/>
<stop offset="1" stop-color="rgb(0%, 33.724976%, 63.529968%)" stop-opacity="1"/>
</linearGradient>
</defs>
<path fill-rule="nonzero" fill="rgb(0%, 33.724976%, 63.529968%)" fill-opacity="1" d="M 182.898438 119.214844 C 182.898438 168.910156 142.617188 209.195312 92.921875 209.195312 C 43.226562 209.195312 2.945312 168.910156 2.945312 119.214844 C 2.945312 69.523438 43.226562 29.242188 92.921875 29.242188 C 142.617188 29.242188 182.898438 69.523438 182.898438 119.214844 "/>
<path fill="none" stroke-width="5.892" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 33.724976%, 63.529968%)" stroke-opacity="1" stroke-miterlimit="3.864" d="M 0.0010375 0.00155625 C 0.0010375 -49.693756 -40.280213 -89.978913 -89.975525 -89.978913 C -139.670838 -89.978913 -179.952088 -49.693756 -179.952088 0.00155625 C -179.952088 49.692962 -139.670838 89.974212 -89.975525 89.974212 C -40.280213 89.974212 0.0010375 49.692962 0.0010375 0.00155625 Z M 0.0010375 0.00155625 " transform="matrix(1, 0, 0, -1, 182.8974, 119.2164)"/>
<g clip-path="url(#clip-0)">
<path fill="none" stroke-width="5.834" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(100%, 100%, 100%)" stroke-opacity="1" stroke-miterlimit="3.864" d="M 0.00005 -0.00045625 C 0.00005 34.9097 28.300831 63.206575 63.207081 63.206575 C 98.117238 63.206575 126.414113 34.9097 126.414113 -0.00045625 C 126.414113 -34.910613 98.117238 -63.207488 63.207081 -63.207488 C 28.300831 -63.207488 0.00005 -34.910613 0.00005 -0.00045625 Z M 0.00005 -0.00045625 " transform="matrix(1, 0, 0, -1, 29.2812, 118.2222)"/>
</g>
<g clip-path="url(#clip-1)">
<g clip-path="url(#clip-2)">
<path fill-rule="nonzero" fill="url(#radial-pattern-0)" d="M 136.820312 139.710938 L 136.820312 165.804688 L 171.332031 165.804688 L 171.332031 139.710938 Z M 136.820312 139.710938 "/>
</g>
</g>
<path fill="none" stroke-width="1.945" stroke-linecap="round" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="1" stroke-miterlimit="3.864" d="M -0.00090625 0.0015625 C -0.00090625 0.0015625 24.210031 -14.838281 18.569406 -18.908594 C 8.604562 -26.092188 -10.301688 -23.721094 -10.301688 -23.721094 Z M -0.00090625 0.0015625 " transform="matrix(1, 0, 0, -1, 147.122, 139.7125)"/>
<g clip-path="url(#clip-3)">
<g clip-path="url(#clip-4)">
<path fill-rule="nonzero" fill="url(#radial-pattern-1)" d="M 7.585938 114.140625 L 7.585938 140.226562 L 42.097656 140.226562 L 42.097656 114.140625 Z M 7.585938 114.140625 "/>
</g>
</g>
<path fill="none" stroke-width="1.945" stroke-linecap="round" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="1" stroke-miterlimit="3.864" d="M 0.001075 -0.000425 C 0.001075 -0.000425 -24.209862 -14.840269 -18.569237 -18.906675 C -8.6083 -26.086363 10.301856 -23.719175 10.301856 -23.719175 Z M 0.001075 -0.000425 " transform="matrix(1, 0, 0, -1, 31.7958, 114.1402)"/>
<g clip-path="url(#clip-5)">
<path fill="none" stroke-width="1.556" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(100%, 100%, 100%)" stroke-opacity="1" stroke-miterlimit="3.864" d="M 0.000075 0.0015375 C -48.6093 0.0015375 -88.011644 39.403881 -88.011644 88.013256 C -88.011644 136.618725 -48.6093 176.021069 0.000075 176.021069 C 48.605544 176.021069 88.011794 136.618725 88.011794 88.013256 C 88.011794 39.403881 48.605544 0.0015375 0.000075 0.0015375 Z M 0.000075 0.0015375 " transform="matrix(1, 0, 0, -1, 92.9218, 207.2281)"/>
</g>
<g clip-path="url(#clip-6)">
<path fill-rule="nonzero" fill="url(#linear-pattern-0)" d="M 21.4375 47.546875 L 21.4375 155.105469 L 52.589844 155.105469 L 52.589844 47.546875 Z M 21.4375 47.546875 "/>
</g>
<path fill-rule="evenodd" fill="rgb(98.039246%, 65.098572%, 10.195923%)" fill-opacity="1" d="M 27.863281 44.757812 C 23.761719 44.757812 19.90625 43.121094 18.523438 44.253906 C 17.140625 45.386719 16.636719 47.480469 17.265625 49.363281 C 17.894531 51.253906 19.402344 51.207031 19.027344 54.101562 C 18.648438 56.992188 17.644531 59.839844 19.152344 59.964844 C 20.664062 60.09375 21.625 58.5 22.878906 56.363281 C 24.136719 54.226562 22.769531 50.730469 25.269531 51.125 C 27.660156 51.503906 28.496094 50.921875 28.496094 53.390625 C 28.496094 55.859375 27.863281 56.488281 28.621094 57.871094 C 29.375 59.257812 28.746094 61.097656 30.257812 60.59375 C 31.765625 60.09375 30.757812 59.382812 31.261719 58.25 C 31.765625 57.117188 32.691406 56.488281 33.148438 58 C 33.605469 59.507812 32.851562 60.847656 33.480469 62.105469 C 34.109375 63.363281 35.113281 65.246094 35.746094 63.488281 C 36.375 61.726562 35.367188 56.695312 36.121094 54.980469 C 36.875 53.265625 37.675781 50.496094 38.761719 51.378906 C 39.847656 52.257812 39.847656 52.714844 40.601562 54.226562 C 41.359375 55.734375 43.496094 56.613281 43.75 55.484375 C 44 54.351562 43.449219 51.882812 44.835938 51.503906 C 46.21875 51.125 46.972656 50.921875 47.351562 53.265625 C 47.726562 55.609375 46.71875 57.621094 47.851562 58 C 48.984375 58.375 48.402344 55.105469 49.742188 55.234375 C 51.078125 55.359375 50.070312 57.164062 50.574219 59.382812 C 51.078125 61.601562 53.59375 62.308594 53.96875 60.21875 C 54.347656 58.125 53.21875 50.875 54.097656 50.75 C 54.976562 50.621094 54.804688 54.980469 55.9375 54.980469 C 57.070312 54.980469 58.203125 50.667969 57.445312 47.398438 C 56.691406 44.128906 56.816406 40.824219 52.839844 42.035156 C 48.859375 43.246094 47.808594 44.253906 44.960938 44.507812 C 42.113281 44.757812 36.203125 44.253906 34.234375 44.253906 C 32.265625 44.253906 27.863281 44.757812 27.863281 44.757812 "/>
<path fill="none" stroke-width="7.228" stroke-linecap="round" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="1" stroke-miterlimit="3.864" d="M -0.0018625 -0.00194375 L -0.0018625 -93.642569 L -31.1503 -93.642569 L -31.1503 2.845712 C -31.8378 1.951181 -32.540925 1.423837 -33.435456 1.498056 C -34.943269 1.623056 -33.939362 4.470712 -33.560456 7.361337 C -33.185456 10.255869 -34.693269 10.208994 -35.322175 12.099619 C -35.951081 13.982431 -35.447175 16.076181 -34.064362 17.208994 C -32.68155 18.341806 -28.826081 16.705087 -24.724519 16.705087 C -24.724519 16.705087 -20.318269 17.208994 -18.353425 17.208994 C -16.388581 17.208994 -10.474519 16.705087 -7.626862 16.955087 C -4.779206 17.208994 -3.728425 18.216806 0.252044 19.427744 C 4.228606 20.638681 4.103606 17.333994 4.857513 14.064462 C 5.611419 10.794931 4.482513 6.482431 3.3497 6.482431 C 2.216888 6.482431 2.388763 10.841806 1.509856 10.7129 C 0.63095 10.5879 1.759856 3.3379 1.384856 1.24415 C 1.216888 0.326181 0.638763 -0.052725 -0.0018625 -0.00194375 Z M -14.579987 19.173837 C -14.579987 19.173837 -22.079987 26.505869 -21.701081 31.740244 C -21.326081 36.978525 -16.591706 44.103525 -15.9628 48.7129 C -15.333894 53.318369 -16.216706 57.849619 -16.216706 57.849619 C -16.216706 57.849619 -7.079987 37.732431 -7.958894 32.498056 C -8.841706 27.259775 -14.579987 19.173837 -14.579987 19.173837 Z M -14.579987 19.173837 " transform="matrix(1, 0, 0, -1, 52.5878, 61.4629)"/>
<path fill="none" stroke-width="4.337" stroke-linecap="round" stroke-linejoin="round" stroke="rgb(0%, 33.724976%, 63.529968%)" stroke-opacity="1" stroke-miterlimit="3.864" d="M -0.0018625 -0.00194375 L -0.0018625 -93.642569 L -31.1503 -93.642569 L -31.1503 2.845712 C -31.8378 1.951181 -32.540925 1.423837 -33.435456 1.498056 C -34.943269 1.623056 -33.939362 4.470712 -33.560456 7.361337 C -33.185456 10.255869 -34.693269 10.208994 -35.322175 12.099619 C -35.951081 13.982431 -35.447175 16.076181 -34.064362 17.208994 C -32.68155 18.341806 -28.826081 16.705087 -24.724519 16.705087 C -24.724519 16.705087 -20.318269 17.208994 -18.353425 17.208994 C -16.388581 17.208994 -10.474519 16.705087 -7.626862 16.955087 C -4.779206 17.208994 -3.728425 18.216806 0.252044 19.427744 C 4.228606 20.638681 4.103606 17.333994 4.857513 14.064462 C 5.611419 10.794931 4.482513 6.482431 3.3497 6.482431 C 2.216888 6.482431 2.388763 10.841806 1.509856 10.7129 C 0.63095 10.5879 1.759856 3.3379 1.384856 1.24415 C 1.216888 0.326181 0.638763 -0.052725 -0.0018625 -0.00194375 Z M -14.579987 19.173837 C -14.579987 19.173837 -22.079987 26.505869 -21.701081 31.740244 C -21.326081 36.978525 -16.591706 44.103525 -15.9628 48.7129 C -15.333894 53.318369 -16.216706 57.849619 -16.216706 57.849619 C -16.216706 57.849619 -7.079987 37.732431 -7.958894 32.498056 C -8.841706 27.259775 -14.579987 19.173837 -14.579987 19.173837 Z M -14.579987 19.173837 " transform="matrix(1, 0, 0, -1, 52.5878, 61.4629)"/>
<g clip-path="url(#clip-7)">
<path fill-rule="nonzero" fill="url(#linear-pattern-1)" d="M 21.4375 47.546875 L 21.4375 155.105469 L 52.589844 155.105469 L 52.589844 47.546875 Z M 21.4375 47.546875 "/>
</g>
<path fill-rule="evenodd" fill="rgb(98.039246%, 65.098572%, 10.195923%)" fill-opacity="1" d="M 27.863281 44.757812 C 23.761719 44.757812 19.90625 43.121094 18.523438 44.253906 C 17.140625 45.386719 16.636719 47.480469 17.265625 49.363281 C 17.894531 51.253906 19.402344 51.207031 19.027344 54.101562 C 18.648438 56.992188 17.644531 59.839844 19.152344 59.964844 C 20.664062 60.09375 21.625 58.5 22.878906 56.363281 C 24.136719 54.226562 22.769531 50.730469 25.269531 51.125 C 27.660156 51.503906 28.496094 50.921875 28.496094 53.390625 C 28.496094 55.859375 27.863281 56.488281 28.621094 57.871094 C 29.375 59.257812 28.746094 61.097656 30.257812 60.59375 C 31.765625 60.09375 30.757812 59.382812 31.261719 58.25 C 31.765625 57.117188 32.691406 56.488281 33.148438 58 C 33.605469 59.507812 32.851562 60.847656 33.480469 62.105469 C 34.109375 63.363281 35.113281 65.246094 35.746094 63.488281 C 36.375 61.726562 35.367188 56.695312 36.121094 54.980469 C 36.875 53.265625 37.675781 50.496094 38.761719 51.378906 C 39.847656 52.257812 39.847656 52.714844 40.601562 54.226562 C 41.359375 55.734375 43.496094 56.613281 43.75 55.484375 C 44 54.351562 43.449219 51.882812 44.835938 51.503906 C 46.21875 51.125 46.972656 50.921875 47.351562 53.265625 C 47.726562 55.609375 46.71875 57.621094 47.851562 58 C 48.984375 58.375 48.402344 55.105469 49.742188 55.234375 C 51.078125 55.359375 50.070312 57.164062 50.574219 59.382812 C 51.078125 61.601562 53.59375 62.308594 53.96875 60.21875 C 54.347656 58.125 53.21875 50.875 54.097656 50.75 C 54.976562 50.621094 54.804688 54.980469 55.9375 54.980469 C 57.070312 54.980469 58.203125 50.667969 57.445312 47.398438 C 56.691406 44.128906 56.816406 40.824219 52.839844 42.035156 C 48.859375 43.246094 47.808594 44.253906 44.960938 44.507812 C 42.113281 44.757812 36.203125 44.253906 34.234375 44.253906 C 32.265625 44.253906 27.863281 44.757812 27.863281 44.757812 "/>
<g clip-path="url(#clip-8)">
<g clip-path="url(#clip-9)">
<path fill-rule="nonzero" fill="url(#radial-pattern-2)" d="M 29.234375 55.066406 L 29.234375 181.476562 L 155.644531 181.476562 L 155.644531 55.066406 Z M 29.234375 55.066406 "/>
</g>
</g>
<path fill="none" stroke-width="2.392" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 33.724976%, 63.529968%)" stroke-opacity="1" stroke-miterlimit="3.864" d="M 0.001075 0.00146875 C 0.001075 34.907719 28.29795 63.204594 63.2042 63.204594 C 98.114356 63.204594 126.411231 34.907719 126.411231 0.00146875 C 126.411231 -34.908688 98.114356 -63.205563 63.2042 -63.205563 C 28.29795 -63.205563 0.001075 -34.908688 0.001075 0.00146875 Z M 0.001075 0.00146875 " transform="matrix(1, 0, 0, -1, 29.2333, 118.271)"/>
<path fill-rule="nonzero" fill="rgb(0%, 33.724976%, 63.529968%)" fill-opacity="1" d="M 87.050781 69.933594 C 86.492188 71.6875 88.648438 72.085938 89.445312 73.363281 C 91.039062 73.761719 90.402344 76.234375 92.238281 75.59375 C 93.035156 76.152344 94.070312 75.835938 94.308594 76.953125 C 94.152344 77.191406 94.308594 77.75 94.789062 77.589844 C 95.585938 77.507812 96.382812 77.269531 97.101562 77.828125 C 97.820312 78.308594 97.898438 79.105469 97.660156 79.742188 C 97.421875 80.300781 97.183594 80.859375 96.703125 81.097656 C 95.347656 81.65625 94.867188 80.621094 93.75 80.378906 L 93.355469 80.78125 C 94.46875 82.054688 96.542969 81.496094 97.339844 82.933594 C 96.464844 83.570312 98.21875 84.847656 96.703125 85.007812 C 95.507812 85.007812 94.308594 85.328125 93.355469 84.527344 L 90.324219 84.449219 C 89.84375 83.96875 90.324219 83.25 89.683594 82.855469 C 87.851562 83.011719 86.652344 81.894531 85.378906 81.097656 C 85.296875 80.460938 85.296875 79.902344 84.660156 79.664062 C 83.382812 80.140625 83.78125 78.464844 83.382812 77.910156 C 83.621094 77.191406 83.863281 76.394531 83.382812 75.59375 C 81.550781 74.878906 80.035156 75.914062 78.675781 77.03125 C 77.800781 77.113281 76.6875 77.910156 76.046875 76.953125 C 77.003906 74.238281 80.113281 72.964844 81.46875 70.492188 C 82.265625 70.011719 82.503906 70.011719 83.382812 70.25 C 84.417969 69.933594 85.695312 69.136719 86.734375 70.011719 Z M 87.050781 69.933594 "/>
<path fill-rule="nonzero" fill="rgb(0%, 33.724976%, 63.529968%)" fill-opacity="1" d="M 77.640625 70.8125 L 77.722656 70.730469 C 77.960938 71.289062 78.675781 71.847656 78.121094 72.566406 C 77.484375 73.042969 76.445312 73.761719 75.570312 73.679688 C 75.007812 73.921875 75.328125 74.558594 74.929688 74.796875 C 74.53125 75.277344 74.371094 75.914062 73.734375 76.074219 C 72.9375 76.234375 71.976562 75.199219 71.421875 76.152344 C 69.984375 76.632812 68.871094 75.4375 67.671875 75.039062 C 66.792969 74.957031 66.875 76.152344 65.917969 75.835938 C 64.082031 76.394531 63.445312 74.71875 62.488281 73.761719 C 62.328125 72.644531 63.527344 71.289062 64.402344 70.8125 C 65.121094 70.96875 65.679688 71.6875 65.996094 72.484375 C 66.238281 72.964844 66.078125 73.839844 66.875 74.238281 C 67.832031 72.804688 69.507812 73.203125 70.703125 72.164062 C 71.019531 71.207031 72.375 71.847656 72.859375 71.128906 C 73.574219 70.890625 73.414062 72.003906 74.050781 71.847656 C 75.007812 71.449219 76.046875 71.371094 76.601562 70.25 C 77.082031 70.011719 77.5625 70.570312 77.640625 70.8125 "/>
<path fill-rule="nonzero" fill="rgb(0%, 33.724976%, 63.529968%)" fill-opacity="1" d="M 75.566406 77.113281 C 75.410156 78.464844 74.609375 79.742188 73.335938 80.21875 C 72.21875 80.0625 71.101562 80.300781 70.222656 79.664062 C 69.746094 78.785156 70.703125 77.828125 71.019531 77.429688 C 71.339844 77.351562 72.378906 77.75 72.535156 76.871094 C 73.253906 77.03125 73.414062 76.632812 74.132812 76.472656 L 74.292969 76.074219 C 74.132812 76.871094 75.566406 76.074219 75.566406 77.113281 "/>
<path fill-rule="nonzero" fill="rgb(0%, 33.724976%, 63.529968%)" fill-opacity="1" d="M 67.832031 77.429688 C 68.148438 77.113281 68.707031 76.472656 69.109375 77.113281 C 69.027344 77.667969 69.664062 77.828125 69.746094 78.386719 C 69.027344 79.664062 68.628906 82.054688 66.636719 81.816406 L 65.519531 80.539062 C 64.164062 81.257812 63.125 79.902344 61.769531 80.378906 C 60.8125 80.0625 61.132812 79.265625 60.972656 78.464844 C 61.132812 78.066406 61.214844 77.429688 61.769531 77.191406 C 62.808594 77.03125 64.003906 77.191406 64.960938 77.589844 C 65.519531 76.550781 67.113281 76.710938 67.832031 77.429688 "/>
<path fill-rule="nonzero" fill="rgb(0%, 33.724976%, 63.529968%)" fill-opacity="1" d="M 80.59375 80.0625 L 81.070312 79.824219 C 80.910156 79.902344 81.152344 80.699219 81.46875 80.378906 L 81.3125 81.496094 C 82.027344 81.097656 82.425781 79.824219 83.464844 80.21875 C 83.382812 80.941406 84.023438 81.179688 84.179688 81.816406 C 83.941406 82.375 84.179688 82.933594 83.621094 83.332031 C 82.90625 83.171875 82.1875 83.96875 81.3125 83.492188 C 80.59375 82.933594 81.152344 82.214844 81.3125 81.496094 C 80.59375 81.816406 80.433594 82.855469 80.195312 83.570312 C 80.113281 84.769531 81.390625 85.484375 80.910156 86.679688 C 80.113281 87.320312 80.351562 88.515625 79.714844 89.15625 C 80.273438 89.710938 81.867188 89.871094 81.3125 90.988281 C 80.195312 91.867188 81.867188 92.503906 82.1875 93.222656 C 82.503906 93.460938 82.984375 93.378906 83.382812 93.300781 C 83.941406 92.421875 82.824219 90.429688 84.898438 90.667969 C 85.296875 90.507812 84.980469 90.191406 84.898438 89.953125 C 84.417969 89.394531 83.382812 89.394531 83.621094 88.355469 C 83.78125 87.480469 84.738281 87 84.339844 85.960938 C 85.21875 85.566406 84.179688 84.371094 85.21875 84.289062 C 86.894531 84.449219 89.207031 84.527344 90.160156 85.804688 C 91.757812 87.160156 94.070312 85.484375 95.824219 86.679688 C 97.898438 85.328125 100.371094 86.125 102.363281 86.839844 C 101.964844 87.480469 102.6875 88.4375 101.808594 89.074219 C 102.203125 89.871094 101.167969 90.507812 100.691406 90.910156 C 99.972656 91.226562 99.09375 91.308594 98.539062 92.023438 C 99.335938 93.699219 95.347656 95.054688 97.738281 96.332031 C 98.375 95.695312 99.015625 96.730469 99.734375 96.171875 C 100.371094 96.011719 100.210938 96.730469 100.453125 97.050781 C 99.8125 97.765625 99.652344 98.644531 98.699219 99.042969 C 98.9375 99.203125 99.015625 99.523438 98.855469 99.839844 C 98.777344 101.4375 95.585938 100 97.101562 101.675781 C 96.703125 102.953125 95.347656 103.75 94.152344 103.988281 C 94.628906 104.546875 95.507812 104.867188 95.824219 105.582031 C 95.824219 106.539062 95.1875 107.097656 94.546875 107.65625 C 93.992188 107.65625 93.273438 107.65625 92.953125 108.296875 C 92.796875 108.773438 93.195312 109.09375 93.433594 109.492188 C 92.953125 110.449219 93.59375 112.042969 92.15625 112.679688 C 91.519531 112.921875 90.640625 112.921875 90.003906 112.605469 C 89.605469 112.28125 89.527344 111.644531 89.683594 111.164062 C 88.886719 110.449219 87.691406 111.882812 87.132812 110.6875 C 86.09375 111.324219 84.980469 112.125 84.820312 113.398438 C 84.820312 114.035156 85.855469 113.957031 85.535156 114.835938 C 86.253906 115.3125 85.617188 116.507812 86.734375 116.589844 C 87.769531 117.703125 85.855469 119.539062 87.769531 119.859375 C 88.40625 120.019531 88.808594 119.941406 89.207031 119.539062 C 89.605469 119.144531 89.046875 118.742188 89.125 118.425781 C 89.84375 117.464844 90.960938 117.148438 92.074219 116.828125 C 92.714844 116.988281 92.953125 117.703125 93.273438 118.265625 C 93.511719 119.144531 92.398438 120.339844 93.59375 120.738281 C 93.910156 121.292969 93.113281 122.253906 93.910156 122.652344 C 94.546875 122.730469 95.109375 122.8125 95.664062 122.570312 C 95.507812 122.171875 95.1875 121.453125 95.90625 121.375 C 96.464844 121.136719 96.941406 121.535156 97.339844 121.855469 C 97.738281 122.332031 97.738281 123.050781 97.660156 123.609375 C 98.136719 123.6875 98.777344 124.246094 99.253906 123.6875 C 99.734375 122.332031 100.769531 121.453125 101.40625 120.257812 C 101.808594 119.941406 102.285156 119.699219 102.765625 120.019531 C 103.003906 120.257812 103.402344 120.578125 103.71875 120.257812 C 104.441406 119.78125 104.441406 118.824219 105.316406 118.742188 C 106.433594 119.222656 106.910156 117.785156 107.867188 118.1875 C 108.667969 120.417969 110.660156 119.0625 112.175781 120.019531 C 112.65625 120.578125 113.53125 120.816406 114.25 120.496094 C 114.410156 120.097656 114.570312 120.65625 114.808594 120.578125 C 115.285156 119.78125 116.402344 119.941406 117.199219 119.539062 C 119.113281 119.460938 120.230469 121.535156 122.226562 121.375 C 123.183594 120.894531 124.140625 119.941406 125.253906 120.578125 C 125.652344 121.933594 127.007812 121.214844 127.808594 121.691406 C 129.164062 123.050781 129.480469 125.363281 128.765625 127.035156 C 127.886719 127.753906 128.527344 128.632812 128.207031 129.507812 C 128.046875 131.105469 125.972656 131.902344 126.453125 133.574219 C 125.734375 134.855469 127.40625 135.96875 126.453125 137.246094 C 126.054688 138.761719 126.691406 141.234375 124.617188 141.710938 C 123.738281 141.394531 123.421875 142.191406 123.023438 142.667969 C 123.902344 144.664062 122.542969 146.65625 121.984375 148.570312 C 121.425781 149.6875 119.992188 148.570312 119.589844 149.769531 C 120.789062 151.203125 118.476562 153.117188 120.152344 154.234375 C 121.109375 156.464844 116.5625 157.34375 118.714844 159.578125 C 118.714844 160.933594 118.476562 162.449219 118.953125 163.722656 C 119.832031 164.679688 120.230469 165.71875 120.789062 166.835938 C 121.585938 167.394531 123.421875 166.996094 123.183594 168.667969 C 122.703125 170.421875 120.3125 169.707031 118.875 170.34375 C 117.519531 169.386719 116.324219 170.664062 114.964844 170.34375 C 113.929688 169.785156 112.734375 169.546875 112.335938 168.269531 C 112.414062 168.03125 112.496094 167.710938 112.496094 167.394531 C 111.21875 167.3125 109.863281 167.234375 109.382812 165.796875 C 108.746094 164.121094 111.695312 162.847656 109.304688 161.652344 C 109.304688 159.976562 107.867188 160.136719 107.867188 158.460938 C 106.671875 157.742188 107.152344 156.066406 106.433594 155.03125 C 106.273438 151.683594 105.476562 148.09375 103.960938 145.144531 C 102.765625 143.867188 103.402344 141.953125 102.605469 140.4375 C 101.566406 139.640625 101.25 137.484375 99.335938 138.203125 C 98.617188 136.847656 98.855469 135.011719 97.820312 133.816406 C 97.101562 132.621094 98.136719 130.785156 97.101562 129.589844 C 96.542969 128.792969 95.269531 128.949219 94.949219 127.914062 C 94.070312 127.355469 92.875 126.636719 91.519531 127.117188 C 90.878906 126.957031 91.199219 126.160156 90.640625 125.839844 C 89.445312 126.320312 88.488281 125.203125 87.449219 125.601562 C 86.894531 124.566406 85.058594 125.042969 85.296875 123.449219 C 84.980469 123.050781 84.582031 123.769531 84.179688 123.6875 C 82.984375 123.527344 82.425781 122.253906 81.070312 122.332031 C 80.035156 121.535156 79.875 120.019531 78.519531 119.539062 C 78.121094 118.105469 77.5625 116.667969 76.285156 115.710938 C 75.40625 114.992188 76.605469 112.839844 74.769531 113.398438 C 73.894531 114.519531 75.328125 115.074219 75.726562 115.792969 C 75.726562 117.066406 77.242188 118.1875 76.363281 119.539062 L 75.25 120.257812 L 73.8125 119.460938 C 73.972656 117.785156 72.058594 117.230469 71.738281 115.710938 C 70.941406 115.472656 69.824219 116.03125 69.269531 115.152344 C 69.027344 114.277344 69.664062 113.320312 68.390625 113.078125 C 67.671875 112.363281 67.910156 111.324219 66.875 111.007812 C 66.238281 109.96875 65.757812 108.453125 64.5625 107.816406 L 64.480469 106.699219 C 63.84375 106.9375 62.964844 106.699219 62.808594 106.0625 C 62.488281 105.105469 63.527344 104.308594 63.046875 103.507812 C 62.648438 104.945312 61.132812 104.070312 60.253906 104.386719 C 59.777344 104.226562 59.296875 103.828125 59.296875 103.347656 C 59.378906 102.074219 58.101562 101.675781 57.621094 100.636719 C 56.824219 100.71875 56.1875 100.160156 55.867188 99.441406 C 55.867188 98.40625 54.589844 98.007812 53.792969 97.609375 C 51.800781 97.128906 50.605469 99.28125 49.007812 99.363281 C 48.132812 99.523438 48.292969 100.558594 47.414062 100.480469 C 46.617188 101.996094 45.898438 103.507812 44.066406 103.667969 C 43.1875 104.867188 41.75 104.785156 40.476562 105.183594 L 38.960938 106.382812 C 38.878906 107.179688 39.039062 108.695312 37.765625 108.613281 C 37.125 108.695312 36.089844 108.933594 36.007812 107.976562 C 36.007812 106.699219 36.648438 105.503906 37.921875 104.867188 C 38.480469 103.589844 38.71875 102.472656 39.996094 101.515625 C 39.917969 99.921875 40.238281 98.882812 39.757812 97.289062 C 39.277344 97.128906 38.71875 96.570312 38.71875 95.933594 C 38.71875 95.214844 39.359375 94.65625 39.917969 94.335938 C 39.199219 93.699219 37.921875 92.824219 38.242188 91.785156 C 38.878906 90.910156 39.757812 90.429688 40.476562 89.792969 C 42.949219 89.792969 44.621094 87.957031 47.015625 87.796875 C 48.371094 86.441406 50.84375 87.082031 52.039062 85.328125 C 54.195312 85.164062 56.664062 84.6875 58.898438 85.40625 C 59.378906 84.769531 60.09375 84.371094 60.972656 84.289062 C 60.8125 82.933594 62.566406 82.292969 63.367188 81.816406 L 63.367188 81.894531 L 64.324219 81.894531 C 64.242188 81.816406 64.242188 81.894531 64.164062 81.894531 C 65.28125 82.054688 66.875 82.132812 67.910156 82.855469 C 69.027344 82.375 70.542969 82.535156 71.183594 81.257812 C 72.296875 80.300781 73.8125 81.578125 75.167969 80.859375 C 75.726562 80.300781 76.523438 79.34375 77.324219 79.027344 C 78.359375 79.105469 79.714844 79.421875 80.59375 80.0625 "/>
<path fill-rule="nonzero" fill="rgb(0%, 33.724976%, 63.529968%)" fill-opacity="1" d="M 59.777344 81.097656 C 59.457031 81.976562 58.582031 82.695312 57.785156 83.09375 C 57.304688 83.25 56.824219 83.25 56.265625 83.09375 C 55.628906 82.855469 56.1875 82.292969 56.027344 81.894531 C 56.347656 81.019531 57.464844 80.621094 58.339844 80.300781 C 59.058594 80.300781 59.457031 80.78125 59.777344 81.097656 "/>
<path fill-rule="nonzero" fill="rgb(0%, 33.724976%, 63.529968%)" fill-opacity="1" d="M 105.394531 89.15625 C 106.195312 90.113281 106.753906 91.308594 107.390625 92.34375 C 107.46875 93.300781 106.351562 93.539062 105.792969 93.9375 C 104.597656 93.382812 103.800781 94.339844 102.84375 95.054688 C 102.207031 95.214844 101.488281 95.214844 100.929688 94.816406 C 100.453125 94.417969 99.972656 93.382812 100.53125 92.664062 C 101.804688 91.945312 102.527344 90.910156 103.324219 89.710938 C 103.960938 89.394531 105 87.878906 105.394531 89.15625 "/>
<path fill-rule="nonzero" fill="rgb(0%, 33.724976%, 63.529968%)" fill-opacity="1" d="M 36.167969 110.128906 C 36.167969 110.691406 35.449219 110.925781 34.972656 111.007812 C 34.492188 111.085938 34.097656 110.691406 34.175781 110.207031 C 34.414062 109.648438 34.972656 109.410156 35.53125 109.410156 C 35.851562 109.648438 36.25 109.648438 36.167969 110.128906 "/>
<path fill-rule="nonzero" fill="rgb(0%, 33.724976%, 63.529968%)" fill-opacity="1" d="M 34.414062 111.726562 C 34.253906 112.125 33.695312 112.363281 33.21875 112.523438 C 32.820312 112.523438 32.261719 112.523438 32.183594 112.042969 C 32.101562 111.40625 32.820312 111.5625 33.136719 111.246094 C 33.457031 111.484375 34.414062 110.925781 34.414062 111.726562 "/>
<path fill-rule="nonzero" fill="rgb(0%, 33.724976%, 63.529968%)" fill-opacity="1" d="M 96.0625 113.238281 C 97.339844 113.558594 98.617188 111.886719 99.652344 113.320312 C 100.851562 113.078125 102.207031 113.636719 103.480469 114.035156 C 103.71875 114.675781 103.480469 115.390625 103.480469 116.109375 C 102.765625 116.90625 101.167969 116.589844 100.371094 116.351562 C 99.8125 114.992188 98.136719 115.636719 97.101562 115.710938 C 96.382812 116.109375 95.585938 116.269531 94.789062 116.109375 L 93.910156 115.152344 C 94.152344 114.277344 94.710938 113.558594 95.109375 112.679688 C 95.585938 112.679688 95.667969 113 96.0625 113.238281 "/>
<path fill-rule="nonzero" fill="rgb(0%, 33.724976%, 63.529968%)" fill-opacity="1" d="M 108.1875 113.636719 C 108.425781 114.355469 109.703125 113.875 109.222656 114.753906 C 109.304688 115.074219 109.941406 115.390625 109.464844 115.792969 C 108.027344 116.507812 106.351562 116.988281 104.835938 116.269531 C 104.597656 115.074219 105.316406 114.195312 105.953125 113.398438 C 106.59375 113.558594 107.46875 114.515625 108.1875 113.636719 "/>
<path fill-rule="nonzero" fill="rgb(0%, 33.724976%, 63.529968%)" fill-opacity="1" d="M 99.253906 117.066406 C 99.175781 117.464844 99.574219 118.902344 98.375 118.902344 L 96.625 119.0625 C 96.304688 118.582031 96.78125 118.101562 96.941406 117.703125 C 97.5 117.066406 98.539062 116.90625 99.253906 117.066406 "/>
<path fill-rule="evenodd" fill="rgb(0%, 33.724976%, 63.529968%)" fill-opacity="1" d="M 73.21875 63.023438 C 73.21875 63.023438 71.109375 63.160156 70.675781 64 C 69.765625 65.753906 70.742188 67.183594 70.742188 67.183594 C 70.742188 67.183594 70.351562 67.25 70.351562 67.574219 C 70.351562 67.902344 70.542969 68.296875 70.480469 68.691406 C 70.414062 69.078125 70.023438 69.210938 70.480469 69.597656 C 70.601562 69.703125 70.753906 69.734375 70.921875 69.722656 C 71.273438 69.832031 71.976562 69.355469 72.367188 69.40625 C 72.894531 69.46875 73.117188 69.660156 73.117188 69.660156 C 73.117188 69.660156 73.734375 69.695312 74.351562 69.316406 C 74.96875 68.941406 75.28125 68.425781 75.28125 68.425781 C 75.28125 68.425781 74.96875 67.910156 74.902344 67.15625 C 74.832031 66.402344 74.9375 65.921875 74.695312 65.679688 C 74.457031 65.441406 73.460938 65.421875 73.152344 65.097656 C 72.398438 64.308594 73.21875 63.023438 73.21875 63.023438 "/>
<path fill="none" stroke-width="0.12" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 33.724976%, 63.529968%)" stroke-opacity="1" stroke-miterlimit="3.864" d="M -0.00185 0.0010625 C -0.00185 0.0010625 -2.111225 -0.135656 -2.544819 -0.9755 C -3.454975 -2.729406 -2.478413 -4.159094 -2.478413 -4.159094 C -2.478413 -4.159094 -2.869038 -4.2255 -2.869038 -4.549719 C -2.869038 -4.877844 -2.677631 -5.272375 -2.740131 -5.666906 C -2.806538 -6.053625 -3.197163 -6.186437 -2.740131 -6.573156 C -2.619038 -6.678625 -2.466694 -6.709875 -2.298725 -6.698156 C -1.947163 -6.807531 -1.244038 -6.330969 -0.853413 -6.38175 C -0.326069 -6.44425 -0.103413 -6.635656 -0.103413 -6.635656 C -0.103413 -6.635656 0.513775 -6.670812 1.130962 -6.291906 C 1.74815 -5.916906 2.06065 -5.401281 2.06065 -5.401281 C 2.06065 -5.401281 1.74815 -4.885656 1.681744 -4.13175 C 1.611431 -3.377844 1.7169 -2.897375 1.474712 -2.655187 C 1.236431 -2.416906 0.240337 -2.397375 -0.0682563 -2.073156 C -0.822163 -1.284094 -0.00185 0.0010625 -0.00185 0.0010625 Z M -0.00185 0.0010625 " transform="matrix(1, 0, 0, -1, 73.2206, 63.0245)"/>
<path fill-rule="evenodd" fill="rgb(0%, 33.724976%, 63.529968%)" fill-opacity="1" stroke-width="0.12" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 33.724976%, 63.529968%)" stroke-opacity="1" stroke-miterlimit="3.864" d="M -0.00190625 -0.00090625 C -0.00190625 -0.00090625 0.377 -0.926687 0.103563 -1.372 C -0.173781 -1.817312 -0.275344 -2.262625 -0.00190625 -2.571219 C 0.271531 -2.879812 0.341844 -2.469656 0.580125 -2.813406 C 0.822313 -3.15325 1.029344 -3.668875 0.959031 -4.114187 C 0.892625 -4.563406 0.755906 -5.008719 0.994188 -5.247 C 1.236375 -5.489187 2.388719 -5.856375 2.228563 -6.27825 C 1.611375 -7.922781 -0.787062 -7.821219 -0.822219 -8.747 C -0.857375 -9.672781 -1.201125 -10.395437 -0.650344 -10.668875 C -0.103469 -10.942312 0.478563 -10.360281 0.892625 -10.563406 C 1.302781 -10.770437 1.4395 -11.1845 1.4395 -11.1845 C 1.4395 -11.1845 1.404344 -10.942312 1.919969 -11.114187 C 2.435594 -11.286062 2.744188 -11.489187 3.154344 -11.594656 C 3.568406 -11.696219 3.705125 -11.352469 3.705125 -11.352469 C 3.705125 -11.352469 4.185594 -11.661062 5.041063 -11.457937 C 5.900438 -11.250906 8.986375 -9.500906 8.607469 -8.575125 C 8.232469 -7.649344 7.267625 -7.696219 7.443406 -7.239187 C 7.787156 -6.344656 8.642625 -6.418875 8.986375 -5.899344 C 9.537156 -5.075125 8.951219 -3.977469 8.951219 -3.977469 C 8.951219 -3.977469 10.255906 -3.15325 10.462938 -2.4345 C 10.666063 -1.71575 10.701219 -1.063406 10.701219 -1.063406 C 10.701219 -1.063406 9.740281 -0.375906 9.568406 0.0694063 C 9.400438 0.514719 10.052781 1.885813 10.052781 1.885813 Z M -0.00190625 -0.00090625 " transform="matrix(1, 0, 0, -1, 76.6855, 58.0655)"/>
<path fill-rule="evenodd" fill="rgb(0%, 33.724976%, 63.529968%)" fill-opacity="1" stroke-width="0.12" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 33.724976%, 63.529968%)" stroke-opacity="1" stroke-miterlimit="3.864" d="M -0.0018625 0.001 C -0.0018625 0.001 -0.720612 -0.893531 -1.236237 -1.40525 C -1.751862 -1.920875 -3.294831 -3.190406 -2.986237 -3.944312 C -2.677644 -4.698219 -1.68155 -5.006812 -1.68155 -5.006812 C -1.68155 -5.006812 -1.611237 -5.866187 -2.298737 -6.690406 C -2.986237 -7.514625 -4.86905 -10.018531 -3.810456 -11.081031 C -2.74405 -12.143531 -1.439362 -11.389625 0.170013 -10.803687 C 1.783294 -10.221656 1.576263 -9.948219 2.056731 -10.084937 C 2.5372 -10.221656 3.291106 -11.010719 3.5997 -11.112281 C 3.908294 -11.21775 6.826263 -10.803687 6.826263 -10.803687 C 6.826263 -10.803687 6.826263 -11.010719 7.373138 -11.319312 C 7.923919 -11.627906 8.607513 -11.627906 8.607513 -11.627906 C 8.607513 -11.627906 8.095794 -12.350562 8.50595 -12.897437 C 8.916106 -13.444312 8.884856 -13.823219 8.986419 -14.338844 C 9.091888 -14.854469 8.986419 -14.991187 9.295013 -15.299781 C 9.603606 -15.608375 10.462981 -15.538062 10.462981 -15.538062 C 10.462981 -15.538062 11.595794 -16.772437 11.627044 -17.288062 C 11.6622 -17.803687 11.423919 -18.284156 11.423919 -18.284156 C 11.423919 -18.284156 13.994231 -20.721656 14.236419 -21.198219 C 14.4747 -21.678687 14.064544 -22.366187 14.302825 -22.776344 C 14.541106 -23.190406 15.744231 -24.870094 15.607513 -25.385719 C 15.470794 -25.897437 15.228606 -26.448219 15.5372 -26.823219 C 15.8497 -27.202125 17.392669 -27.752906 17.5997 -28.334937 C 17.802825 -28.916969 17.697356 -30.256812 18.490325 -30.428687 C 19.275481 -30.600562 22.673919 -30.358375 22.638763 -28.299781 C 22.603606 -26.241187 21.986419 -23.737281 23.396575 -23.120094 C 24.341888 -22.706031 24.834075 -23.499 25.384856 -23.053687 C 25.931731 -22.604469 25.759856 -22.194312 26.205169 -22.09275 C 26.650481 -21.987281 27.306731 -21.233375 27.306731 -20.752906 C 27.306731 -20.272437 26.959075 -19.690406 26.959075 -19.690406 C 26.959075 -19.690406 26.826263 -19.3115 27.545013 -19.276344 C 28.267669 -19.245094 29.158294 -19.276344 29.498138 -18.831031 C 29.841888 -18.385719 29.877044 -17.975562 29.877044 -17.975562 C 29.877044 -17.975562 30.459075 -17.803687 30.732513 -17.323219 C 31.009856 -16.84275 31.111419 -14.920875 31.111419 -14.920875 C 31.111419 -14.920875 31.076263 -15.194312 31.728606 -15.026344 C 32.38095 -14.854469 33.240325 -14.131812 33.892669 -13.858375 C 34.541106 -13.584937 35.158294 -13.651344 35.158294 -13.651344 L 28.677825 -7.409156 L 18.763763 -2.709937 L 11.935638 -0.549781 L 3.189544 0.274438 Z M -0.0018625 0.001 " transform="matrix(1, 0, 0, -1, 96.6503, 55.9385)"/>
<path fill-rule="evenodd" fill="rgb(0%, 33.724976%, 63.529968%)" fill-opacity="1" stroke-width="0.12" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 33.724976%, 63.529968%)" stroke-opacity="1" stroke-miterlimit="3.864" d="M -0.0018625 0.001025 C -0.0018625 0.001025 -0.497956 -1.178663 -0.888581 -1.518506 C -1.279206 -1.85835 -2.564362 -2.854444 -2.197175 -3.401319 C -1.833894 -3.9521 -0.810456 -4.284131 -0.810456 -4.284131 C -0.810456 -4.284131 -1.255769 -4.651319 -1.30655 -5.069288 C -1.361237 -5.491163 -1.517487 -6.799756 -1.595612 -7.034131 C -1.673737 -7.272413 -1.833894 -7.29585 -1.201081 -7.479444 C -0.576081 -7.663038 -0.263581 -7.401319 -0.0018625 -7.557569 C 0.263763 -7.713819 0.365325 -7.846631 0.365325 -7.846631 C 0.365325 -7.846631 1.228606 -7.584913 1.521575 -7.350538 C 1.806731 -7.112256 2.095794 -6.666944 2.095794 -6.666944 C 2.095794 -6.666944 2.8497 -6.588819 3.423919 -6.799756 C 3.998138 -7.006788 4.154388 -7.088819 4.416106 -6.928663 C 4.677825 -6.772413 5.25595 -5.463819 5.25595 -5.463819 L 2.06845 -0.940381 Z M -0.0018625 0.001025 " transform="matrix(1, 0, 0, -1, 134.2128, 70.6729)"/>
<path fill-rule="evenodd" fill="rgb(0%, 33.724976%, 63.529968%)" fill-opacity="1" stroke-width="0.12" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 33.724976%, 63.529968%)" stroke-opacity="1" stroke-miterlimit="3.864" d="M -0.00095 -0.0018625 C -0.00095 -0.0018625 0.182644 -1.521394 -0.1572 -1.857331 C -0.497044 -2.201081 -1.493137 -2.880769 -1.258762 -3.326081 C -1.020481 -3.771394 -0.942356 -4.005769 -0.942356 -4.005769 C -0.942356 -4.005769 -1.106419 -4.345613 -1.360325 -4.501863 C -1.614231 -4.658113 -2.184544 -5.099519 -1.747044 -5.412019 C -1.309544 -5.724519 -0.786106 -6.013581 -0.286106 -6.040925 C 0.209988 -6.064363 1.545925 -5.701081 1.545925 -5.701081 C 1.545925 -5.701081 1.4678 -6.669831 1.178738 -7.271394 C 0.889675 -7.872956 0.604519 -8.736238 0.838894 -9.208894 C 1.073269 -9.677644 0.81155 -9.939363 0.417019 -10.072175 C 0.0263938 -10.201081 -0.262669 -9.939363 -0.629856 -10.099519 C -0.993137 -10.255769 -1.25095 -10.490144 -1.668919 -10.779206 C -2.086887 -11.064363 -2.5322 -10.99405 -2.739231 -11.408113 C -2.950169 -11.818269 -2.950169 -12.263581 -2.950169 -12.263581 C -2.950169 -12.263581 -5.254856 -12.314363 -5.754856 -13.861238 C -6.25095 -15.404206 -6.25095 -17.466706 -5.778294 -18.095613 C -5.309544 -18.724519 -4.9697 -19.009675 -4.9697 -19.009675 C -4.9697 -19.009675 -4.993137 -20.111238 -4.547825 -20.318269 C -4.102512 -20.529206 -2.739231 -20.294831 -2.348606 -20.579988 C -1.954075 -20.86905 -1.793919 -21.001863 -1.56345 -21.001863 C -1.336887 -21.001863 0.31155 -20.134675 0.549831 -20.134675 L 1.022488 -20.134675 C 1.022488 -20.134675 2.956081 -18.204988 3.167019 -17.474519 C 3.37405 -16.740144 3.221706 -15.326081 3.221706 -15.326081 C 3.221706 -15.326081 3.456081 -15.169831 3.534206 -14.880769 C 3.612331 -14.595613 3.479519 -14.201081 3.56155 -13.939363 C 3.639675 -13.677644 3.768581 -13.49405 3.768581 -13.49405 C 3.768581 -13.49405 4.170925 -13.572175 4.68655 -13.310456 C 5.202175 -13.048738 5.776394 -12.341706 5.776394 -12.341706 C 5.776394 -12.341706 6.225613 -12.630769 6.327175 -13.076081 C 6.432644 -13.521394 6.327175 -14.673738 6.799831 -15.040925 C 7.272488 -15.404206 7.479519 -16.2753 7.608425 -17.158113 C 7.741238 -18.044831 8.237331 -18.853425 7.795925 -19.271394 C 7.350613 -19.689363 7.635769 -20.111238 6.381863 -19.454988 C 5.12405 -18.802644 4.897488 -18.7753 4.526394 -19.142488 C 4.163113 -19.509675 3.612331 -20.05655 3.612331 -20.451081 C 3.612331 -20.845613 3.401394 -21.185456 3.06155 -21.263581 C 2.721706 -21.341706 1.31155 -21.341706 1.31155 -21.341706 C 1.31155 -21.341706 0.760769 -20.607331 -0.286106 -20.896394 C -1.336887 -21.185456 -1.536106 -22.439363 -1.7197 -23.017488 C -1.903294 -23.591706 -2.37595 -25.861238 -2.661106 -26.412019 C -2.950169 -26.9628 -5.020481 -29.677644 -4.993137 -30.7753 C -4.9697 -31.876863 -4.7822 -31.560456 -4.415012 -31.982331 C -4.051731 -32.4003 -3.6572 -32.86905 -3.735325 -33.419831 C -3.81345 -33.970613 -4.262669 -34.80655 -4.024387 -35.4628 C -3.790012 -36.115144 -2.5322 -37.548738 -2.399387 -38.072175 C -2.270481 -38.595613 -2.481419 -39.013581 -2.192356 -39.486238 C -1.903294 -39.958894 -1.360325 -40.9003 -0.262669 -41.501863 C 0.838894 -42.103425 1.440456 -42.392488 1.7803 -42.708894 C 2.120144 -43.021394 2.174831 -43.255769 2.799831 -43.1503 C 3.428738 -43.044831 4.9678 -42.076081 5.43655 -41.790925 C 5.909206 -41.501863 6.670925 -41.001863 7.428738 -41.474519 C 8.18655 -41.947175 8.526394 -43.540925 8.631863 -44.189363 C 8.733425 -44.845613 9.206081 -47.673738 9.206081 -47.673738 L 11.557644 -43.021394 L 11.5303 -39.013581 L 11.549831 -28.982331 L 8.659206 -14.923738 L 3.87405 -4.05655 L 1.979519 -1.064363 Z M -0.00095 -0.0018625 " transform="matrix(1, 0, 0, -1, 144.0947, 82.4122)"/>
<g clip-path="url(#clip-10)">
<g clip-path="url(#clip-11)">
<path fill-rule="nonzero" fill="url(#linear-pattern-2)" d="M 12.265625 123.652344 L 12.265625 166.304688 L 171.503906 166.304688 L 171.503906 123.652344 Z M 12.265625 123.652344 "/>
</g>
</g>
<path fill="none" stroke-width="1.945" stroke-linecap="round" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="1" stroke-miterlimit="3.864" d="M 0.00010625 -0.00045 C 0.00010625 -0.00045 -5.812394 6.847206 -15.773331 7.183144 C -25.738175 7.522987 -66.015519 -3.258263 -98.855362 -6.234825 C -151.984269 -11.043419 -159.238175 0.269081 -159.238175 0.269081 L -156.058487 -26.766075 C -156.058487 -26.766075 -149.554581 -34.4067 -124.8163 -34.965294 C -79.890519 -35.980919 -28.726456 -16.805138 -16.054581 -18.836388 C -3.382706 -20.867638 -5.074112 -27.445763 -5.074112 -27.445763 Z M 0.00010625 -0.00045 " transform="matrix(1, 0, 0, -1, 171.5038, 130.8433)"/>
<path fill-rule="evenodd" fill="rgb(100%, 88.235474%, 71.765137%)" fill-opacity="1" d="M 38.007812 42.289062 C 38.007812 42.289062 30.507812 34.957031 30.882812 29.722656 C 31.261719 24.484375 35.996094 17.359375 36.625 12.753906 C 37.253906 8.144531 36.371094 3.613281 36.371094 3.613281 C 36.371094 3.613281 45.507812 23.726562 44.628906 28.964844 C 43.746094 34.203125 38.007812 42.289062 38.007812 42.289062 "/>
<path fill="none" stroke-width="1.446" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(98.039246%, 65.098572%, 10.195923%)" stroke-opacity="1" stroke-miterlimit="3.864" d="M -0.0008875 -0.0009625 C -0.0008875 -0.0009625 -7.500888 7.331069 -7.125888 12.565444 C -6.746981 17.803725 -2.012606 24.928725 -1.3837 29.534194 C -0.754794 34.143569 -1.637606 38.674819 -1.637606 38.674819 C -1.637606 38.674819 7.499112 18.561537 6.620206 13.323256 C 5.737394 8.084975 -0.0008875 -0.0009625 -0.0008875 -0.0009625 Z M -0.0008875 -0.0009625 " transform="matrix(1, 0, 0, -1, 38.0087, 42.2881)"/>
<path fill-rule="evenodd" fill="rgb(100%, 100%, 100%)" fill-opacity="1" d="M 38.921875 25.070312 C 40.328125 28.996094 41.375 32.566406 41.136719 33.980469 C 40.777344 36.109375 39.28125 39.050781 37.878906 41.453125 C 36.402344 39.496094 34.996094 37.203125 35.105469 35.660156 C 35.238281 33.828125 36.199219 31.367188 37.570312 28.210938 C 38.03125 27.148438 38.496094 26.09375 38.921875 25.070312 "/>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-0-0" x="67.2124" y="52.3631"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-1-0" x="71.1011" y="50.9891"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-2-0" x="75.417" y="49.7743"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-3-0" x="78.3252" y="49.1034"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-4-0" x="82.0366" y="48.4593"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-5-0" x="83.541" y="48.1956"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-6-0" x="88.9614" y="47.7127"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-7-0" x="93.4248" y="47.6121"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-8-0" x="97.5918" y="47.7601"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-9-0" x="101.749" y="48.1766"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-10-0" x="103.2852" y="48.3494"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-11-0" x="107.6992" y="49.1366"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-12-0" x="111.7314" y="50.0999"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-13-0" x="116.0068" y="51.4119"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-14-0" x="119.8906" y="52.8617"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-15-0" x="123.6982" y="54.5726"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-16-0" x="125.1025" y="55.2415"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-17-0" x="128.1992" y="56.8885"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-18-0" x="131.3887" y="58.8197"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-19-0" x="134.3203" y="60.8162"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-20-0" x="135.5937" y="61.7015"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-21-0" x="139.0889" y="64.4774"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-22-0" x="142.1943" y="67.2948"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-23-0" x="143.3262" y="68.3675"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-24-0" x="145.7627" y="70.8841"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-25-0" x="148.6709" y="74.2728"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-26-0" x="151.1904" y="77.6327"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-27-0" x="152.0908" y="78.8744"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-28-0" x="154.333" y="82.3778"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-29-0" x="156.2246" y="85.7249"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-30-0" x="157.9277" y="89.1576"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-31-0" x="159.4375" y="92.6712"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-32-0" x="160.6621" y="95.9305"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-33-0" x="161.9805" y="100.2337"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-34-0" x="162.9014" y="103.9154"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-35-0" x="163.7471" y="108.6351"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-36-0" x="164.2119" y="112.4466"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-37-0" x="164.4619" y="116.5902"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-38-0" x="34.8315" y="169.2474"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-39-0" x="41.5752" y="176.057"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-40-0" x="46.8467" y="180.4847"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-41-0" x="54.604" y="185.5814"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-42-0" x="61.4829" y="189.0716"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-43-0" x="66.9209" y="191.2611"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-44-0" x="71.6802" y="192.7826"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-45-0" x="75.1191" y="193.8089"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-46-0" x="84.7651" y="195.4173"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-47-0" x="92.9561" y="195.8236"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-48-0" x="100.0879" y="195.4632"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-49-0" x="105.5146" y="194.7581"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-50-0" x="110.416" y="193.7581"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-51-0" x="113.9512" y="192.9749"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-52-0" x="123.4307" y="189.5326"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-53-0" x="131.0449" y="185.8197"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-54-0" x="140.6104" y="179.1576"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-55-0" x="145.6406" y="174.8099"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-56-0" x="54.6655" y="193.6117"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-57-0" x="57.4692" y="195.0286"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-58-0" x="62.9658" y="197.3763"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-59-0" x="69" y="199.4281"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-60-0" x="75.1079" y="200.9417"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-61-0" x="76.4492" y="201.2435"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-62-0" x="80.3608" y="201.9124"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-63-0" x="82.8643" y="202.2552"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-64-0" x="85.8848" y="202.5628"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-65-0" x="88.8833" y="202.7591"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-66-0" x="91.9834" y="202.8724"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-67-0" x="97.1123" y="202.7435"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-68-0" x="99.0225" y="202.6576"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-69-0" x="103.9932" y="202.14"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-70-0" x="108.0947" y="201.473"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-71-0" x="109.9639" y="201.1273"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-72-0" x="113.8633" y="200.2142"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-73-0" x="117.0586" y="199.3382"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-74-0" x="121.4902" y="197.846"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-75-0" x="123.9297" y="196.9193"/>
</g>
<g fill="rgb(100%, 100%, 100%)" fill-opacity="1">
<use xlink:href="#glyph-76-0" x="126.7607" y="195.7318"/>
</g>
</svg>
