<?php
session_start();

// Set your database path (adjust if necessary)
$dbPath = __DIR__ . '/../db/news.db';

try {
    $pdo = new PDO('sqlite:' . $dbPath);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    // Enable foreign keys
    $pdo->exec("PRAGMA foreign_keys = ON");
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username']);
    $password = $_POST['password'];
    
    // Prepare a statement to prevent SQL injection.
    $stmt = $pdo->prepare("SELECT * FROM admin WHERE username = :username");
    $stmt->bindParam(':username', $username);
    $stmt->execute();
    
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($admin && password_verify($password, $admin['password'])) {
        // Credentials are valid. Set session variables.
        $_SESSION['admin_id'] = $admin['id'];
        $_SESSION['username'] = $admin['username'];
        header("Location: dashboard");
        exit;
    } else {
        $error = "Invalid username or password.";
    }
} else {
    $error = "";
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Admin Login</title>
  <link rel="stylesheet" href="../css/news_managing.css">
</head>
<body>
  <form method="POST" action="login">
    <?php if (!empty($error)): ?>
        <p style="color:red; text-align:center; margin-bottom:20px;"><?php echo htmlspecialchars($error); ?></p>
    <?php endif; ?>
    <label for="username">Username:</label>
    <input type="text" id="username" name="username" required /><br><br>
    
    <label for="password">Password:</label>
    <input type="password" id="password" name="password" required /><br><br>
    
    <button type="submit">Login</button>
  </form>
</body>
</html>