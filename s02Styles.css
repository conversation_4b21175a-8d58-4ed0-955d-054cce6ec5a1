/*----------------------------------------------------------------------------------------------------------------------------------
----------------------------------------------------------   Home Page   -----------------------------------------------------------
----------------------------------------------------------------------------------------------------------------------------------*/

/*----------------------------------------------------------------
-----------------------Button Section Styles----------------------
------------------------------------------------------------------*/

.section-title-btn {
    text-align: center;
    display: block;
    margin: 50 auto;
}
@media (min-width: 992px) {
    .section-title-btn {
        margin-bottom: 200px;
    }
}

.section-title-btn .button .btn {
    color: white;
    font-size: clamp(14px, 2vw, 16px);
    border-radius: 25px;
    width: 30%;
    transition: all 0.5s ease;
}
.section-title-btn .button .btn:hover{
    font-size: clamp(16px, 2.3vw, 20px);
    font-weight: bolder;
}
.section-title-btn .button .btn:focus{
    color:white;
}
.section-title-btn .button .btn:before{
	content: "";
	position: absolute;
	z-index: -1;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	background: #2C2D3F;
	-webkit-transform: scaleX(0);
	transform: scaleX(0);
	-webkit-transform-origin: 50%;
	transform-origin: 50%;
	-webkit-transition:all 0.5s ease;
	-moz-transition:all 0.5s ease;
	transition:all 0.5s ease;
	border-radius:25px;
}
.section-title-btn .button .btn:hover:before{
	-webkit-transform: scaleX(1);
	transform: scaleX(1);
}

/*----------------------------------------------------------------
--------------------------------END-------------------------------
------------------------------------------------------------------*/





/*----------------------------------------------------------------
-------------------------Vision and Values------------------------
------------------------------------------------------------------*/

.schedule .single-schedule .inner .single-content {
    direction: rtl;
    text-align: right;
}

/*----------------------------------------------------------------
--------------------------------END-------------------------------
------------------------------------------------------------------*/





/*----------------------------------------------------------------
---------------------------Testimonials--------------------------
------------------------------------------------------------------*/

.testimonials {
    background-image: url('img/testi-bg.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    padding: 60px 0;
    overflow: hidden;
}

.testimonials .section-title h2 {
    color: #444444;
    text-align: center;
    margin-bottom: 10px;
	font-size: 28px
}

.testimonials .single-testimonial {
    text-align: left;
    position: relative;
    background: #f9fcff;
    padding: 40px 30px;
    margin: 5px;
    margin-bottom: 27px;
    margin-right: 30px;
    border-radius: 5px;
    -webkit-transition: all 0.4s ease;
    -moz-transition: all 0.4s ease;
    transition: all 0.4s ease;
    margin: 0;
    margin: 30px 20px;
}

.testimonials .single-testimonial:hover {
    box-shadow: 0px 10px 10px #0000001c;
    transform: translateY(-4px);
}

.testimonials .single-testimonial img {
    position: absolute;
    left: 30px;
    bottom: -26px;
    height: 53px;
    width: 53px;
    border-radius: 100%;
}

.testimonials .single-testimonial p {
    color: #868686;
    font-size: 14px;
    direction: rtl;
    text-align: right;
}

.testimonials .single-testimonial .name {
    margin-top: 22px;
    color: #2C2D3F;
    font-weight: 500;
    font-size: 20px;
}

.testimonials .owl-nav {
    position: absolute;
    top: 50%;
    width: 100%;
    display: flex;
    justify-content: space-between;
    transform: translateY(-50%);
}

.testimonials .owl-carousel .owl-nav div {
    background: #1A76D1;
    color: #fff;
    border: none;
    padding: 10px 8px;
    border-radius: 10%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.testimonials .owl-carousel .owl-nav div:hover {
    background: #2C2D3F;
}

.testimonials .owl-dots {
    text-align: center;
    margin-top: 20px;
}

.testimonials .owl-dots .owl-dot {
    display: inline-block;
}

.testimonials .owl-dots .owl-dot span {
    width: 10px;
    height: 10px;
    display: block;
    background: #dadada;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.testimonials .owl-dots .owl-dot.active span,
.testimonials .owl-dots .owl-dot:hover span {
    background: #1A76D1;
    width: 13px;
    height: 13px;
    margin-left: 5px;
    margin-right: 5px;
}

/*----------------------------------------------------------------
--------------------------------END-------------------------------
------------------------------------------------------------------*/





/*----------------------------------------------------------------
------------------------------Services----------------------------
------------------------------------------------------------------*/

.Services{
	position: relative;
	padding: 10px 0;
	overflow: hidden;}

.Services-content {
	margin-top: 10px;
	text-align: center;
}


.Services .section-title h2 {
    color: #444444;
    text-align: center;
    margin-bottom: 10px;
	font-size: 28px
}

.single-what-Services{
    text-align: center;
    direction: rtl;
    padding:20px 15px;
    border-radius: 3px;
    box-shadow: 0 0px 15px #1a76d130;
    margin-bottom: 30px;
    -webkit-transition: .3s linear; 
    -moz-transition:.3s linear; 
    -ms-transition:.3s linear; 
    -o-transition:.3s linear;
    transition: .3s linear;
}

.solo-service {
    max-width: 1000px;
    margin: auto;
}

.single-what-Services img {
    width: 90%;
    margin: 10px auto;
    height: auto;
    border-radius: 5px;
    margin-bottom: 15px;
    transition: transform 0.4s ease;
    object-fit: cover;
    transform: rotate3d(1, 1, 1, 2deg);
}

.single-what-Services img:hover {
    transform: scale(1.03) rotate3d(1, 1, 1, 0deg);
}

.single-what-Services-icon {
    display: inline-block;
    color: #50616c;
	font-size: 36px;
    width: 80px;
    height: 80px;
    line-height: 80px;
    background: #eef2f6;
    border-radius: 50%;
}
.single-what-Services h2{
	text-align: center;
}
.single-what-Services h2 a {
    font-size:  18px;
    margin: 35px 0 20px;
}
.single-what-Services h2 a span {text-transform:  lowercase;}
.single-what-Services p {margin-bottom: 25px;text-transform: initial;}

.single-what-Services-icon [class^="flaticon-"]:before,.single-what-Services-icon [class*=" flaticon-"]:before,.single-what-Services-icon [class^="flaticon-"]:after,.single-what-Services-icon [class*=" flaticon-"]:after {font-size: 35px;}
.single-what-Services:hover .single-what-Services-icon [class^="flaticon-"]:before,.single-what-Services:hover .single-what-Services-icon [class*=" flaticon-"]:before,.single-what-Services:hover .single-what-Services-icon [class^="flaticon-"]:after,.single-what-Services:hover .single-what-Services-icon [class*=" flaticon-"]:after {color:#ff545a;}

.welcome-hero-btn.how-work-btn {
    display: inline-block;
    margin: 0;
    width: 100px;
    height: 35px;
    font-size: 12px;
    background: transparent;
    color: #767f86;
    border: 1px solid #d3d6d9;
    border-radius: 3px;
}

.single-what-Services:hover h2 a,.single-what-Services:hover p{color: #fff;}
.single-what-Services:hover .single-what-Services-icon{background: #fff;}
.single-what-Services:hover .welcome-hero-btn.how-work-btn{background: #fff;color: #1A76D1;}
.single-what-Services:hover{box-shadow: 0 0px 10px rgba(71,71,71,.4);background: #1A76D1;}




.service-section-title-btn {
    text-align: center;
    display: block;
    margin: 50 auto;
    
}
.service-section-title-btn .button .btn {
    font-weight: bold;
    color: white;
    font-size: clamp(14px, 2vw, 16px);
    border-radius: 5px;
    width: 70%;
    transition: all 0.5s ease;
}

/*----------------------------------------------------------------
--------------------------------END-------------------------------
------------------------------------------------------------------*/





/*----------------------------------------------------------------
-------------------------Gallery Testiomial-----------------------
------------------------------------------------------------------*/

.gallery-testimonials {
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    overflow: hidden;
}

.gallery-testimonials .single-testimonial {
    text-align: left;
    position: relative;
    background: #f9fcff;
    margin: 30px 20px; /* Adjusted margin */
    border-radius: 5px;
    -webkit-transition: all 0.4s ease;
    -moz-transition: all 0.4s ease;
    transition: all 0.4s ease;
    height: 40vw; /* Set the height to 30% of the viewport width */
    overflow: hidden; /* Hide any overflow */
}

.gallery-testimonials .single-testimonial img {
    width: 100%;
    height: 100%;
    max-height: 100%;
    object-fit: cover
}

.gallery-testimonials .single-testimonial:hover {
    box-shadow: 0px 10px 10px #0000001c;
    transform: translateY(-4px);
}

.gallery-testimonials .owl-nav {
    position: absolute;
    top: 50%;
    width: 100%;
    display: flex;
    justify-content: space-between;
    transform: translateY(-50%);
}

.gallery-testimonials .owl-carousel .owl-nav div {
    background: #1A76D1;
    color: #fff;
    border: none;
    padding: 10px 8px;
    border-radius: 10%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.gallery-testimonials .owl-carousel .owl-nav div:hover {
    background: #2C2D3F;
}

/*----------------------------------------------------------------
--------------------------------END-------------------------------
------------------------------------------------------------------*/




/*----------------------------------------------------------------
---------------------------News and Blogs-------------------------
------------------------------------------------------------------*/

.news-section {
    display: flex;
    flex-wrap: wrap;
    margin: 15%;
    justify-content: space-between;
}

@media (max-width: 568px) {
    .news-section {
        margin: 15% 10%;
    }
}

@media (max-width: 455px) {
    .news-section {
        margin: 15% 0%;
    }
    .news-column {
        min-width: 200px;
    }
}

.news-column {
    flex: 1;
    margin: 20px;
    min-width: 300px;
}

.header-news-column {
    background: #f9fcff;
    border-radius: 5px;
    margin-bottom: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.header-news-column h2{
    color: #444444;
    text-align: center;
	font-size: 24px;
    font-weight: bold;
    padding: 5px;
}


.body-news-column {  
    background: #f9fcff;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.body-news-column h2 {
    direction: rtl;
    text-align: right;
    color: #444444;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
}
.body-news-column p {
    direction: rtl;
    text-align: right;
    color: #444444;
    font-size: 14px;
    margin-bottom: 10px;
}

.body-news-column .news-image {
    border-radius: 25px;
    margin-bottom: 10px;
}
.body-news-column .news-image img {
    border-radius: 3%;
    margin-bottom: 10px;
}


.body-news-column {
    display: none; 
    transition: transform 0.5s ease, opacity 0.5s ease;
    opacity: 0;
}

.body-news-column.active {
    display: block;
    opacity: 1;
}

.nav-news-column {
    background: #f9fcff;
    padding: 10px;
    margin-top: 10px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
}


.news-section .button .btn {

    width: 100%;
}

/*----------------------------------------------------------------
--------------------------------END-------------------------------
------------------------------------------------------------------*/





/*----------------------------------------------------------------
------------------------Sucess Testimonials-----------------------
------------------------------------------------------------------*/

.testimonials .single-testimonial p {
    color: #252525;
}
.testimonials .single-testimonial i {
    color: #1A76D1;
    font-size: 25px;
}

/*----------------------------------------------------------------
--------------------------------END-------------------------------
------------------------------------------------------------------*/



/*----------------------------------------------------------------------------------------------------------------------------------
----------------------------------------------------------   About Page   ----------------------------------------------------------
----------------------------------------------------------------------------------------------------------------------------------*/



/*----------------------------------------------------------------
--------------------------Gallery Diamond-------------------------
------------------------------------------------------------------*/

.definition {
    background: #f9fcff;
    padding: 50px 0;
    border-radius: 1.2vw;
}   

@media (min-width: 990px) {
    .definition {
        margin-bottom: 200px;
    }
}

.definition .section-title h2 {
    font-size: clamp(36px, 4vw, 58px);
    color: #444444;
    text-align: center;
}

.gallery-diamond-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}

.gallery-diamond {
    --size: 100px;
    display: grid;
    grid-template-columns: repeat(6, var(--size));
    grid-auto-rows: var(--size);
    margin-bottom: var(--size);
    place-items: start center;
    gap: 5px;
}

.gallery-diamond:has(:hover) img:not(:hover),
.gallery-diamond:has(:focus) img:not(:focus) {
    filter: brightness(0.5) contrast(0.5);
}

.gallery-diamond img {
    object-fit: cover;
    width: calc(var(--size) * 2);
    height: calc(var(--size) * 2);
    clip-path: path("M90,10 C100,0 100,0 110,10 190,90 190,90 190,90 200,100 200,100 190,110 190,110 110,190 110,190 100,200 100,200 90,190 90,190 10,110 10,110 0,100 0,100 10,90Z");
    transition: clip-path 0.25s, filter 0.75s;
    grid-column: auto / span 2;
    border-radius: 5px;
}

.gallery-diamond img:nth-child(5n - 1) {
    grid-column: 2 / span 2;
}

.gallery-diamond img:hover,
.gallery-diamond img:focus {
    clip-path: path("M0,0 C0,0 200,0 200,0 200,0 200,100 200,100 200,100 200,200 200,200 200,200 100,200 100,200 100,200 100,200 0,200 0,200 0,100 0,100 0,100 0,100 0,100Z");
    z-index: 1;
    transition: clip-path 0.25s, filter 0.25s;
}

.gallery-diamond img:focus {
    outline: 1px dashed black;
    outline-offset: -5px;
}

/*--------------------------------------------------------------*/
.definition-p {
    display: flex;
    border-radius: 1.2vw;
    justify-content: center;
    align-items: center;
    padding: 1% 4%;
    margin: 20px auto;
    width: 90%;
}

.definition-p p{
    direction: rtl;
    text-align: justify;    
    font-size:24px;
    font-weight: bold;
    color: #3d3d3d;
}
.definition hr {
    margin: 0% 10%;
}

.definition-p2 {
    display: flex;
    border-radius: 1.2vw;
    justify-content: center;
    align-items: center;
    padding: 1% 4%;
    margin: 10px auto;
    width: 90%;
}

.definition-p2 p{
    direction: rtl;
    text-align: justify;    
    font-size:20px;
    color: #3d3d3d;
}

/*----------------------------------------------------------------
--------------------------------END-------------------------------
------------------------------------------------------------------*/





/*----------------------------------------------------------------
--------------------------Founder Message-------------------------
------------------------------------------------------------------*/

@media (min-width: 990px) {
    .founder-message {
        margin-top: clamp(400px, 25vw, 450px);
        margin-bottom: 200px
    }
}

.founder-img {
    width: clamp(180px, 20vw, 300px);
    height: clamp(180px, 20vw, 300px);
    border-radius: 50%;
    position: absolute;
    top: clamp(-150px, -10vw, -80px);
    left: 5%;
    border: 3px solid #1A76D1;
}

.founder-message-p {
    padding-top: 3%;
}
@media (max-width: 550px) {
    .founder-message-h {
        padding-top: 80px;
    }
    
}

.founder-message-quote {
    float: left;
}

/*----------------------------------------------------------------
--------------------------------END-------------------------------
------------------------------------------------------------------*/





/*----------------------------------------------------------------
-------------------------------Goals------------------------------
------------------------------------------------------------------*/

.goals img:hover {
    transform: scale(1.1);
    transition: all 0.5s ease;
    
}

.goals .section-title h2 {
    font-size: clamp(36px, 4vw, 58px);
    text-shadow: 3px 5px 2px #3333;
	color: #1A76D1;
    text-align: center;
}

.goals .section-title h2:hover {
    font-size: clamp(46px, 4vw, 88px);;
    transition: all 0.5s ease;
}

.goals-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.goal-item {
    border: 1px solid #ddd;
    padding: 15px;
    border-radius: 5px;
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
    direction: rtl;
    text-align: right;
}

.goal-item:hover {
    background-color: #f9f9f9;
}

.goal-title {
    font-size: clamp(1.2em, 2vw, 2.2em);
    font-weight: 700;
    text-shadow: 3px 1px 2px #3333;
	color: #1A76D1;
    margin: 0;
    position: relative;
    padding-left: 20px;
}

.goal-title::before {
    content: "\f104";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
}

.goal-item.expanded .goal-title::before {
    content: "\f107";
}

.goal-description {
    display: none;
    margin-top: 10px;
    font-size: 18px;
    line-height: 1.5;
}

.goal-description {
    overflow: hidden;
    transition: all 0.5s ease;
}

/*----------------------------------------------------------------
--------------------------------END-------------------------------
------------------------------------------------------------------*/





/*----------------------------------------------------------------
---------------------------Our Work-------------------------------
------------------------------------------------------------------*/

/* Our Work Section - Updated */
.our-work {
    background: linear-gradient(135deg, #f1f9ff, #f9fcff);
    padding: 50px 30px;
    border-radius: 10px;
    margin: 50px auto;
    max-width: 1200px;
    box-shadow: 0 8px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}
.our-work:hover {
    transform: translateY(-5px);
}
.our-work .section-title h2 {
    font-size: clamp(36px, 4vw, 58px);
    color: #1A76D1;
    text-align: center;
    margin-bottom: 20px;
    text-shadow: 3px 5px 2px rgba(51,51,51,0.3);
}
.our-work-description {
    direction: rtl;
    text-align: right;
    font-size: 18px;
    line-height: 1.7;
    color: #202020;
    padding: 25px;
    border: 1px solid #ddd;
    border-radius: 10px;
    background-color: #fff;
    box-shadow: 0 6px 15px rgba(0,0,0,0.07);
    transition: background-color 0.5s ease, transform 0.5s ease;
}
.our-work-description:hover {
    background-color: #f1f1f1;
    transform: scale(1.02);
}

/*--------------------------------------------------------------*/

.our-work-gallery-testimonials {
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    overflow: hidden;
}

.our-work-gallery-testimonials .single-testimonial {
    text-align: left;
    position: relative;
    background: #f9fcff;
    margin: 20px 10px; /* Adjusted margin */
    border-radius: 1vw;
    -webkit-transition: all 0.4s ease;
    -moz-transition: all 0.4s ease;
    transition: all 0.4s ease;
    height: 18vw; /* Set the height to 30% of the viewport width */
    overflow: hidden; /* Hide any overflow */
}

.our-work-gallery-testimonials .single-testimonial img {
    width: 100%;
    height: 100%;
    max-height: 100%;
    object-fit: cover
}

.our-work-gallery-testimonials .single-testimonial:hover {
    box-shadow: 0px 10px 10px #0000001c;
    transform: translateY(-4px);
}

/*----------------------------------------------------------------
--------------------------------END-------------------------------  
------------------------------------------------------------------*/





/*----------------------------------------------------------------
-----------------------------Our Team-----------------------------
------------------------------------------------------------------*/


/* Our Team Slider Container */
.team-slider {
    position: relative;
}

/* Container for team cards as a horizontal slider */
.team-cards-container {
    display: flex;
    flex-wrap: nowrap;
    overflow-x: hidden; /* hides extra incomplete cards */
    gap: 20px;
    padding: 20px 0;
    -webkit-overflow-scrolling: touch;
}

/* Navigation buttons */
.team-nav {
    position: absolute;
    top: 50%;
    background: #1A76D1;
    color: #fff;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    transform: translateY(-50%);
    cursor: pointer;
    opacity: 0.8;
    transition: opacity 0.3s ease;
    z-index: 10;
}
.team-nav:hover {
    opacity: 1;
}
.team-nav.prev {
    left: 10px;
}
.team-nav.next {
    right: 10px;
}

/* Card styling remains similar but update image area to be square */
.new-card {
    flex: 0 0 auto;
    width: 240px;
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border: 1px solid #e0e0e0;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease, border 0.3s ease;
    cursor: pointer;
}
.new-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 24px rgba(0,0,0,0.2);
    border-color: #1A76D1;
}

/* Image area updated to be square */
.team-image {
    width: 100%;
    height: 240px; /* square size (equal to card width) */
    overflow: hidden;
    border-bottom: 1px solid #e0e0e0;
}
.team-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}
.new-card:hover .team-image img {
    transform: scale(1.05);
}

/* Team details styling remains */
.team-details {
    padding: 12px;
    text-align: center;
    background: #fff;
    transition: background 0.3s ease;
}
.team-details h3 {
    margin: 8px 0 5px;
    font-size: 1.1rem;
    color: #333;
    font-weight: 600;
    position: relative;
}
.team-details h3::after {
    content: "";
    position: absolute;
    bottom: -4px;
    left: 50%;
    width: 0;
    height: 2px;
    background: #1A76D1;
    transition: width 0.3s ease, left 0.3s ease;
}
.new-card:hover .team-details h3::after {
    width: 50%;
    left: 25%;
}
.team-details p {
    margin: 0;
    font-size: 0.9rem;
    color: #777;
}

.our-team .section-title h2 {
    font-family: 'Poppins', sans-serif;
    font-size: clamp(36px, 4vw, 58px);
    text-shadow: 3px 5px 2px #3333;
	color: #1A76D1;
    text-align: center;
    transition: all 0.5s ease;
}

.our-team .section-title h2:hover {
    font-size: clamp(46px, 4vw, 68px);;
}


/*----------------------------------------------------------------
--------------------------------END-------------------------------
------------------------------------------------------------------*/





/*----------------------------------------------------------------
--------------------------about buttons---------------------------
------------------------------------------------------------------*/

.about-title-btn {
    display: flex;
    flex-wrap: nowrap;
    justify-content: center;
    gap: 20px;
}

.about-title-btn .btn {
    font-weight: bold;
    color: white;
    font-size: clamp(14px, 2vw, 16px);
    border-radius: 5px;
    width: clamp(120px, 30vw, 350px); /* Increased button width */
    transition: all 0.5s ease;
}

/*----------------------------------------------------------------
--------------------------------END-------------------------------
------------------------------------------------------------------*/



/*----------------------------------------------------------------------------------------------------------------------------------
--------------------------------------------------------   Services Page   ---------------------------------------------------------
----------------------------------------------------------------------------------------------------------------------------------*/



/*----------------------------------------------------------------
---------------------------Services A-----------------------------
------------------------------------------------------------------*/
.services-a .services-a-title {
    font-size: clamp(1.2em, 2vw, 2.2em);
    font-weight: 700;
    text-shadow: 3px 1px 2px #3333;
	color: #1A76D1;
    margin: 0;
    position: relative;
    direction: rtl;
    text-align: justify;
}


.services-a img:hover {
    transform: scale(1.1);
    transition: all 0.5s ease;
    
}

.services-a .section-title h2 {
    font-size: clamp(36px, 4vw, 58px);
    text-shadow: 3px 5px 2px #3333;
	color: #1A76D1;
    text-align: center;
}

.services-a .section-title h2:hover {
    font-size: clamp(46px, 4vw, 88px);;
    transition: all 0.5s ease;
}

.Services .active-services:hover{
    box-shadow: 0 0px 10px rgba(71,71,71,.4);
    background: #ffffff;
    transition: all 0.5s ease;
}

.Services .active-services p{
    color: #50616c;
    font-size: 18px;
}

.Services .active-services:hover p{
    color: #50616c;
}

.Services .active-services:hover .single-what-Services-icon{
    background-color: #eef2f6;
}

.Services .active-services h2{
    padding: 20px 0px;
    font-weight: bold;
    font-size: clamp(36px, 4vw, 58px);
}


/*----------------------------------------------------------------
--------------------------------END-------------------------------
------------------------------------------------------------------*/





/*----------------------------------------------------------------
--------------------------Where Are We----------------------------
------------------------------------------------------------------*/
.Wherearewe .Wherearewe-title {
    font-size: clamp(1.6em, 2vw, 2.6em);
    font-weight: 800;
    text-shadow: 3px 1px 2px #3333;
	color: #1A76D1;
    margin: 0;
    position: relative;
    direction: rtl;
    text-align: center;
    padding: 10px 0px;
}
.Wherearewe .Wherearewe-text {
    font-size: clamp(1.2em, 2vw, 2.2em);
    font-weight: 700;
    text-shadow: 3px 1px 2px #3333;
	color: #444444;
    margin: 0;
    position: relative;
    direction: rtl;
    text-align: center;
}

.Wherearewe .section-title img:hover {
    transform: scale(1.1);
    transition: all 0.5s ease;
}

.Wherearewe .section-title h2 {
    font-size: clamp(36px, 4vw, 58px);
    text-shadow: 3px 5px 2px #3333;
	color: #1A76D1;
    text-align: center;
}

.Wherearewe .section-title h2:hover {
    font-size: clamp(46px, 4vw, 88px);;
    transition: all 0.5s ease;
}

.Wherearewe .section-title .ourquote {
    font-size: clamp(24px, 3.8vw, 36px);
    text-shadow: 3px 5px 2px #3333;
	color: #1A76D1;
    text-align: center;
}
.Wherearewe .section-title .ourquote:hover {
    font-size: clamp(28px, 3.8vw, 42px);;
    transition: all 0.5s ease;
}

.areaofworkclassforimg img {
    display: block;
    margin-left: auto;
    margin-right: auto;
    width: 90%;
    margin: 10px auto;
    height: auto;
    border-radius: 5px;
    margin-bottom: 15px;
    transition: transform 0.4s ease;
    object-fit: cover;
    transform: rotate3d(1, 1, 1, 2deg);
}

.Wherearewe img:hover {
    transform: scale(1.03) rotate3d(1, 1, 1, 0deg);
}

/*----------------------------------------------------------------*/

.areasofwork-h3 {
    text-align: center;
    font-size: clamp(24px, 4vw, 36px);
}

.goal-item:hover .areasofwork-h3{
    font-size: clamp(28px, 4vw, 42px);
    transition: all 0.5s ease;
}

.areasofwork-img {
    max-height: 400px;
    display: block;
    margin-left: auto;
    margin-right: auto;
}

/*----------------------------------------------------------------
--------------------------------END-------------------------------
------------------------------------------------------------------*/



/*----------------------------------------------------------------------------------------------------------------------------------
--------------------------------------------------------   News_Blogs Page   -------------------------------------------------------
----------------------------------------------------------------------------------------------------------------------------------*/



/*----------------------------------------------------------------
---------------------------News and Blogs-------------------------
------------------------------------------------------------------*/

.news-page .section-title h2 {
    font-family: 'Poppins', sans-serif;
    font-size: clamp(36px, 4vw, 58px);
    text-shadow: 3px 5px 2px #3333;
	color: #1A76D1;
    text-align: center;
    transition: all 0.5s ease;
}

.news-page .section-title h2:hover {
    font-size: clamp(46px, 4vw, 68px);;
}

.news-page .news-container {
    justify-content: center;
    border-radius: 10px;
    margin: 50px auto;
    max-width: 1200px;
    box-shadow: 0 8px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
    padding: 50px 20%;
}
@media (max-width: 1220px) {
    .news-page .news-container {
        padding: 50px 10%;
    }
}

.news-page .news-container:hover {
    transform: translateY(-5px);
}

.news-page .single-news {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border: 1px solid #e0e0e0;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease, border 0.3s ease;
    cursor: pointer;
    padding: 10px 10px;
    margin-bottom: 30px;
}

.news-page .single-news:hover {
    box-shadow: 0 8px 24px rgba(0,0,0,0.2);
    border-color: #1A76D1;
}

.news-page .single-news h3 {
    text-align: center;
    font-size: 1.8rem;
    color: #333;
    font-weight: 600;
    position: relative;
    margin-bottom: 5px;
    margin-top: 20px;
}
.news-page .single-news h3::after {
    content: "";
    position: absolute;
    bottom: -4px;
    left: 50%;
    width: 0;
    height: 2px;
    background: #1A76D1;
    transition: width 0.3s ease, left 0.3s ease;
}
.news-page .single-news:hover h3::after {
    width: 60%;
    left: 20%;
}

.news-page .single-news .news-content {
    direction: rtl;
    text-align: right;
    margin: 0;
    font-size: 1.2rem;
    color: #333;
    text-align: justify;
    padding: 0 30px;
    padding-bottom: 20px;
}
.news-page .single-news img {
    object-fit: cover;
    border-radius: 10px;
    border: #e0e0e0 2px solid;
    display: block;
    width: 100%;
    max-width: 500px;
    height: auto;
    margin: 10px auto;
}

/*-----------------------------------------------------------------
--------------------------------END-------------------------------
------------------------------------------------------------------*/





/*----------------------------------------------------------------
-----------------------------news-----------------------------
------------------------Pagination Styles-------------------------
------------------------------------------------------------------*/
.pagination {
    margin: 20px 0;
    text-align: center;
    font-family: 'Poppins', sans-serif;
    font-size: 20px;
}

.pagination a {
    text-decoration: none;
    color: #1A76D1;
    margin: 0 10px;
    padding: 8px 16px;
    border: 1px solid #1A76D1;
    border-radius: 4px;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.pagination a:hover {
    background-color: #1A76D1;
    color: #fff;
}

.pagination span {
    font-weight: bold;
    color: #333;
}

/*----------------------------------------------------------------
----------------------------END-----------------------------------
------------------------------------------------------------------*/



/*----------------------------------------------------------------------------------------------------------------------------------
---------------------------------------------------------   Contact Page   ---------------------------------------------------------
----------------------------------------------------------------------------------------------------------------------------------*/



/*----------------------------------------------------------------
--------------------------Contact Us------------------------------
------------------------------------------------------------------*/

.contact-us .section-title h2 {
    font-family: 'Poppins', sans-serif;
    font-size: clamp(36px, 4vw, 58px);
    text-shadow: 3px 5px 2px #3333;
	color: #1A76D1;
    text-align: center;
    transition: all 0.5s ease;
}

.contact-us .section-title h2:hover {
    font-size: clamp(46px, 4vw, 68px);;
}

.contact-us .contact-us-form {
    direction: rtl;
    text-align: right;
    padding: 50px 20%;
    margin: 50px auto;
    max-width: 1200px;
    box-shadow: 0 8px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
    border-radius: 10px;
}
.contact-us .contact-us-form h2 {
    font-size: clamp(24px, 4vw, 36px);
    text-shadow: 3px 5px 2px #3333;
	color: #1A76D1;
    text-align: center;
}

.contact-us .contact-us-form h2:before {
    left: 50%;
    transform: translateX(-50%);
    width: 40%;
}

.contact-us .contact-us-form p {
    font-size: clamp(18px, 3.8vw, 24px);
    text-shadow: 1px 2px 2px #3333;
	color: #202020;
    text-align: center;
}

/*----------------------------------------------------------------
--------------------------------END-------------------------------
------------------------------------------------------------------*/

/*----------------------------------------------------------------
--------------------------Contact Info----------------------------
------------------------------------------------------------------*/

.contact-info {
    display: grid;
    grid-template-columns: repeat(2, 1fr);  /* Always 2 columns */
    gap: 25px;
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
    perspective: 1000px;
}

.contact-info .single-info {
    background: linear-gradient(135deg, #1a76d1 0%, #2196F3 100%);
    padding: 40px 60px;
    height: 200px !important;  /* Increased height and added !important to override */
    border-radius: 15px;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    -webkit-transition: all .4s cubic-bezier(0.4, 0, 0.2, 1);
    transition: all .4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 10px 20px rgba(26, 118, 209, 0.1);
}

.contact-info .single-info:before {
    position: absolute;
    z-index: -1;
    content: '';
    bottom: -10px;
    left: 0;
    right: 0;
    margin: 0 auto;
    width: 85%;
    height: 90%;
    background: #1A76D1;
    opacity: 0;
    filter: blur(15px);
    transition: all .4s cubic-bezier(0.4, 0, 0.2, 1);
}

.contact-info .single-info:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(120deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%);
    opacity: 0;
    transition: opacity 0.4s ease;
}

.contact-info .single-info:hover:before {
    opacity: 0.8;
}

.contact-info .single-info:hover:after {
    opacity: 1;
}

.contact-info .single-info:hover {
    transform: translateY(-7px) scale(1.02);
    box-shadow: 0 20px 30px rgba(26, 118, 209, 0.2);
}

.contact-info .single-info i {
    font-size: 42px;
    color: #fff;
    position: absolute;
    left: 40px;
    transition: all 0.4s ease;
    text-shadow: 2px 2px 8px rgba(0,0,0,0.1);
}

.contact-info .single-info:hover i {
    transform: scale(1.1) rotate(5deg);
}

.contact-info .single-info .content {
    margin-left: 45px;
    position: relative;
    z-index: 1;
}

.contact-info .single-info .content h3 {
    color: #fff;
    font-size: 22px;
    font-weight: 600;
    margin-bottom: 8px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.contact-info .single-info .content p {
    color: rgba(255,255,255,0.9);
    margin-top: 5px;
    font-size: clamp(14px, 1.6vw, 18px);
    line-height: 1.6;
}

.contact-info .single-info .content a {
    color: #fff;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    display: inline-block;
}

.contact-info .single-info .content a:after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 1px;
    background: #fff;
    transition: width 0.3s ease;
}

.contact-info .single-info .content a:hover:after {
    width: 100%;
}

.contact-info .single-info .content a:hover {
    opacity: 0.9;
    transform: translateX(3px);
}

/* Add extra small screen breakpoint */
@media (max-width: 768px) {
    .contact-info {
        grid-template-columns: 1fr;  /* Single column only on very small screens */
    }
}

/*----------------------------------------------------------------
--------------------------------END-------------------------------
------------------------------------------------------------------*/





/*----------------------------------------------------------------
---------------------------Map Embedded---------------------------
------------------------------------------------------------------*/

.map-section {
    padding-bottom: 100px;
}

.map-section h3 {
    font-size: clamp(24px, 4vw, 36px);
    text-shadow: 3px 5px 2px #3333;
	color: #1A76D1;
    text-align: center;
}

.map-section p {
    font-size: clamp(18px, 3.8vw, 24px);
    text-shadow: 1px 2px 2px #3333;
	color: #202020;
    text-align: center;
    margin-top: 10px;
    margin-bottom: 25px;
}

.map-container {
    position: relative;
    width: 100%;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.map-container iframe {
    display: block;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.map-container:hover {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

@media (max-width: 768px) {
    .map-section {
        padding: 40px 0;
    }
    
    .map-container iframe {
        height: 350px;
    }
}

/*----------------------------------------------------------------
--------------------------------END-------------------------------
------------------------------------------------------------------*/



/*----------------------------------------------------------------------------------------------------------------------------------
-------------------------------------------------------   Join/Donate Page   -------------------------------------------------------
----------------------------------------------------------------------------------------------------------------------------------*/



/*----------------------------------------------------------------
----------------------------join-section--------------------------
------------------------------------------------------------------*/

/* General Section Styling */
.join-section,
.donation-section {
    direction: rtl;
    text-align: right;
    padding: 80px 0;
}

.donation-section.gray-bg {
    background: #f9f9f9;
}

/* Section Title Enhancements */
.section-title {
    margin-bottom: 50px;
}

.join-section .section-title h2 ,
.donation-section .section-title h2 {
    font-size: clamp(36px, 4vw, 58px);
    text-shadow: 3px 5px 2px #3333;
    color: #1A76D1;
    text-align: center;
    position: relative;
    padding-bottom: 20px;
}

.join-section .section-title h2:before ,
.donation-section .section-title h2:before {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 150px;
    height: 3px;
    background: #1A76D1;
}

.section-title p {
    font-size: clamp(18px, 2vw, 24px);
    color: #666;
    text-align: center;
    margin-top: 20px;
    line-height: 1.6;
}

/* Service Cards Styling */
.single-service {
    background: #fff;
    padding: 40px 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.07);
    transition: all 0.4s ease;
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
    text-align: right;
    min-height: 400px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.single-service:before {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    width: 3px;
    height: 0;
    background: #1A76D1;
    transition: all 0.4s ease;
}

.single-service:hover:before {
    height: 100%;
}

.single-service:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
}

.single-service i {
    font-size: 50px;
    color: #1A76D1;
    margin-bottom: 20px;
    display: inline-block;
    transition: all 0.4s ease;
}

.single-service:hover i {
    transform: translateY(-5px);
}

.single-service h4 {
    font-size: 22px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #2C2D3F;
    position: relative;
    padding-bottom: 10px;
}

.single-service h4:after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 50px;
    height: 2px;
    background: #1A76D1;
}

.single-service p {
    font-size: 18px;
    line-height: 1.8;
    color: #666;
    margin-bottom: 25px;
}

/* Button Styling */
.single-service .btn {
    background: #1A76D1;
    color: #fff;
    padding: 12px 25px;
    border-radius: 30px;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    z-index: 1;
    display: inline-block;
    text-align: center;
}

.single-service .btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: #2C2D3F;
    transition: all 0.4s ease;
    z-index: -1;
}

.single-service .btn:hover:before {
    width: 100%;
}

.single-service .btn:hover {
    color: #fff;
}

/* Responsive Design */
@media only screen and (max-width: 991px) {
    .single-service {
        min-height: 350px;
        padding: 30px 20px;
    }

    .section-title {
        margin-bottom: 40px;
    }
}

@media only screen and (max-width: 767px) {
    .join-section,
    .donation-section {
        padding: 50px 0;
    }

    .single-service {
        min-height: auto;
    }

    .single-service i {
        font-size: 40px;
    }

    .single-service h4 {
        font-size: 20px;
    }

    .section-title h2 {
        font-size: clamp(36px, 4vw, 42px);
    }
}

/* Animation Effects */
@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-5px);
    }
    100% {
        transform: translateY(0px);
    }
}

.single-service i {
    animation: float 3s ease-in-out infinite;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    .single-service,
    .single-service i,
    .single-service .btn,
    .single-service:before {
        transition: none;
        animation: none;
    }
}

/* Print Styles */
@media print {
    .single-service {
        box-shadow: none;
        border: 1px solid #ddd;
    }

    .btn {
        border: 1px solid #1A76D1;
    }
}

/*----------------------------------------------------------------
--------------------------------END-------------------------------
------------------------------------------------------------------*/





/*----------------------------------------------------------------
------------------------Illustration Section----------------------
------------------------------------------------------------------*/

.illustration-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    overflow: hidden;
}

.illustration-content {
    padding-right: 30px;
}

.illustration-content h1 {
    font-size: 3rem;
    color: #333;
    margin-bottom: 20px;
    font-weight: 700;
}

.illustration-content .lead {
    font-size: 1.25rem;
    color: #666;
    margin-bottom: 40px;
}

.illustration-stats {
    display: flex;
    justify-content: space-between;
    margin-top: 40px;
}

.stat-item {
    text-align: center;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
}

.stat-item i {
    font-size: 2.5rem;
    color: #1A76D1;
    margin-bottom: 15px;
}

.stat-item h3 {
    font-size: 2rem;
    color: #333;
    margin-bottom: 5px;
}

.stat-item p {
    color: #666;
    margin: 0;
}

.illustration-image {
    text-align: center;
}

.illustration-image img {
    max-width: 100%;
    height: auto;
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
    100% {
        transform: translateY(0px);
    }
}

@media (max-width: 991px) {
    .illustration-content {
        text-align: center;
        padding-right: 0;
        margin-bottom: 40px;
    }
    
    .illustration-stats {
        justify-content: center;
        flex-wrap: wrap;
        gap: 20px;
    }
    
    .stat-item {
        flex: 0 0 calc(33.333% - 20px);
    }
}

@media (max-width: 767px) {
    .stat-item {
        flex: 0 0 calc(50% - 20px);
    }
}

/*----------------------------------------------------------------
--------------------------------END-------------------------------
------------------------------------------------------------------*/



/*----------------------------------------------------------------------------------------------------------------------------------
-------------------------------------------------------   Our Health Center   ------------------------------------------------------
----------------------------------------------------------------------------------------------------------------------------------*/



/*----------------------------------------------------------------
-----------------------------Home page----------------------------
------------------------------------------------------------------*/

.our-health-center-section {
    background: linear-gradient(120deg, #f1f9ff 60%, #e3f0fa 100%); 
    padding: 64px 0 80px 0; 
    border-radius: 28px; 
    margin: 48px auto; 
    max-width: 1200px; 
    box-shadow: 0 12px 48px rgba(26,118,209,0.13); 
    direction: rtl;
}


.our-health-center-section * { font-family: 'Thabit', serif; }
.our-health-center-section h2 {
    color: #1A76D1;
    font-size: clamp(36px, 4vw, 54px);
    font-weight: 900;
    margin-bottom: 24px;
    text-shadow: 2px 4px 16px #1a76d11a;
    letter-spacing: 0.5px;
    display: flex;
    align-items: center;
    gap: 14px;
}
.our-health-center-section h2 i {
    background: #e3f0fa;
    border-radius: 50%;
    padding: 14px;
    font-size: 1.4em;
    color: #1A76D1;
    box-shadow: 0 2px 8px #1a76d11a;
}
.our-health-center-section p {
    text-align: right;
    font-size: 1.25em;
    color: #222;
    margin-bottom: 20px;
    font-weight: 600;
    line-height: 2.1;
}
.our-health-center-section ul {
    list-style: none;
    padding: 0;
    margin-bottom: 28px;
}
.our-health-center-section ul li {
    margin-bottom: 16px;
    font-size: 1.15em;
    display: flex;
    align-items: center;
    background: #f6fbff;
    border-radius: 12px;
    padding: 14px 22px;
    box-shadow: 0 2px 12px #1a76d108;
    transition: background 0.2s;
    font-weight: 500;
    position: relative;
}
.our-health-center-section ul li::before {
    content: '';
    display: block;
    width: 6px;
    height: 60%;
    background: linear-gradient(180deg, #1A76D1 0%, #32B87D 100%);
    border-radius: 6px;
    position: absolute;
    right: 0;
    top: 20%;
}
.our-health-center-section ul li:hover {
    background: #e3f0fa;
}
.our-health-center-section ul li span {
    font-size: 1.7em;
    color: #1A76D1;
    margin-left: 18px;
    min-width: 44px;
    text-align: center;
    background: #eaf6ff;
    border-radius: 50%;
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px #1a76d11a;
}
.our-health-center-section blockquote {
    background: linear-gradient(90deg, #eaf6ff 80%, #d8eafd 100%);
    border-right: 5px solid #1A76D1;
    border-left: none;
    padding: 24px 32px;
    border-radius: 14px;
    font-size: 1.2em;
    font-weight: 600;
    color:rgba(26, 118, 209, 0.8);
    margin-bottom: 26px;
    font-style: italic;
    position: relative;
    box-shadow: 0 2px 16px #1a76d11a;
}
.our-health-center-section blockquote i {
    opacity: 0.7;
}
.our-health-center-section .btn,
.our-health-center-section button.btn {
    position: relative;
    overflow: hidden;
    background: #1A76D1;
    color: #fff !important;
    border-radius: 30px;
    padding: 14px 40px;
    font-size: 1.15em;
    font-weight: 700;
    box-shadow: 0 2px 18px #1a76d13a;
    transition: background 0.3s, box-shadow 0.3s, transform 0.2s, color 0.2s;
    margin: 0 10px 10px 0;
    border: none;
    outline: none;
    display: inline-flex;
    align-items: center;
    gap: 12px;
    letter-spacing: 0.5px;
    cursor: pointer;
    z-index: 1;
}

.our-health-center-section .btn-health {
    background: linear-gradient(90deg, #2196F3, #32B87D) !important;

}

.our-health-center-section .btn::before,
.our-health-center-section button.btn::before {
    content: "";
    position: absolute;
    left: 50%;
    top: 50%;
    width: 0;
    height: 0;
    background: rgba(255,255,255,0.13);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.4s cubic-bezier(.4,0,.2,1), height 0.4s cubic-bezier(.4,0,.2,1);
    z-index: 0;
}

.our-health-center-section .btn:hover::before,
.our-health-center-section button.btn:hover::before,
.our-health-center-section .btn:focus::before,
.our-health-center-section button.btn:focus::before {
    width: 300%;
    height: 300%;
}

.our-health-center-section .btn:hover,
.our-health-center-section button.btn:hover,
.our-health-center-section .btn:focus,
.our-health-center-section button.btn:focus {
    background: #155a9c !important;
    color: #fff !important;
    box-shadow: 0 8px 32px #1a76d11a;
    transform: translateY(-2px) scale(1.04);
    text-decoration: none;
}

.our-health-center-section .btn[style*="background:#32B87D"],
.our-health-center-section button.btn[style*="background:#32B87D"] {
    background: #32B87D !important;
}
.our-health-center-section .btn[style*="background:#32B87D"]:hover,
.our-health-center-section button.btn[style*="background:#32B87D"]:hover {
    background: #249c61 !important;
}
.our-health-center-section .btn[style*="background:#2196F3"],
.our-health-center-section button.btn[style*="background:#2196F3"] {
    background: #2196F3 !important;
}
.our-health-center-section .btn[style*="background:#2196F3"]:hover,
.our-health-center-section button.btn[style*="background:#2196F3"]:hover {
    background: #1769aa !important;
}

/* Ensure text and icons are above the effect */
.our-health-center-section .btn > *,
.our-health-center-section button.btn > * {
    position: relative;
    z-index: 1;
}

.our-health-center-section .btn-health:hover {
    background: linear-gradient(90deg, #1769aa, #249c61) !important;
}
.our-health-center-gallery {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    justify-content: center;
    width: 100%;
}
.our-health-center-gallery img {
    border-radius: 16px;
    box-shadow: 0 4px 24px #1a76d11a;
    transition: transform 0.3s, box-shadow 0.3s;
    cursor: pointer;
    object-fit: cover;
    border: 3px solid #fff;
    filter: grayscale(10%) brightness(0.98);
}
.our-health-center-gallery img:nth-child(1),
.our-health-center-gallery img:nth-child(2) {
    width: calc(50% - 10px);
    aspect-ratio: 4/3;
    min-width: unset;
    flex: 1 0 auto;
}
.our-health-center-gallery img:last-child {
    width: 100%;
    aspect-ratio: 16/7;
    margin-top: 20px;
}
.our-health-center-gallery img:hover {
    transform: scale(1.055) rotate(-1deg);
    box-shadow: 0 12px 40px #1a76d122;
    border-color: #1A76D1;
    filter: none;
}
@media (max-width: 991px) {
    .our-health-center-section .row {
        flex-direction: column-reverse;
    }
    .our-health-center-gallery {
        gap: 15px;
    }
    .our-health-center-gallery img:nth-child(1),
    .our-health-center-gallery img:nth-child(2) {
        width: calc(50% - 7.5px);
    }
    .our-health-center-gallery img:last-child {
        margin-top: 15px;
    }
}
@media (max-width: 480px) {
    .our-health-center-gallery {
        gap: 10px;
    }
    .our-health-center-gallery img:nth-child(1),
    .our-health-center-gallery img:nth-child(2) {
        width: calc(50% - 5px);
    }
    .our-health-center-gallery img:last-child {
        margin-top: 10px;
    }
}
.our-health-center-section .action-buttons {
    margin-top: 26px;
    text-align: center;
}
.our-health-center-section .action-buttons .btn {
    min-width: 160px;
    font-size: 1.09em;
}
.our-health-center-section .bottom-message {
    text-align: center;
    margin-top: 44px;
    font-size: 1.25em;
    color: #1A76D1;
    font-weight: 700;
    letter-spacing: 0.5px;
    border-radius: 12px;
    padding: 16px 0;
    box-shadow: 0 2px 12px #1a76d108;
}

/*----------------------------------------------------------------
--------------------------------END-------------------------------
------------------------------------------------------------------*/


/*----------------------------------------------------------------
--------------------------Page------------------------------------
------------------------------------------------------------------*/
.health-center-link {
    display: flex !important;
    align-items: center;
    position: relative;
    padding-right: 15px !important;
}

.special-badge {
    position: absolute;
    right: -10px;
    top: 50%;
    transform: translateY(-50%);
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border-radius: 50%;
    width: 22px;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(255, 0, 0, 0.1);
    transition: all 0.3s ease;
    animation: badgePulse 2s infinite;
    font-size: 1.1em;
}

@keyframes badgePulse {
    0% {
        transform: translateY(-50%) scale(1);
        box-shadow: 0 2px 8px rgba(255, 0, 0, 0.1);
    }
    50% {
        transform: translateY(-50%) scale(1.1);
        box-shadow: 0 4px 12px rgba(255, 0, 0, 0.2);
    }
    100% {
        transform: translateY(-50%) scale(1);
        box-shadow: 0 2px 8px rgba(255, 0, 0, 0.1);
    }
}

.special-badge:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #fff 100%);
    transform: translateY(-50%) rotate(180deg);
    box-shadow: 0 4px 15px rgba(255, 0, 0, 0.15);
}

@media (max-width: 767px) {
    .special-badge {
        left: 100px;
    }
    .special-badge i {
        display: contents !important;
    }
}

/*------------------------------------------------------------------
------------------------------------------------------------------*/

/* Health Center Specific Heading Styles */
.health-center-hero h2,
.about-domiz h2,
.current-reality h2,
.health-services h2,
.our-mission h2 {
    font-family: 'Thabit', serif;
    font-size: clamp(2.5rem, 5vw, 3.5rem);
    font-weight: 800;
    color: #1A76D1;
    text-align: center;
    margin-bottom: 1.5rem;
    position: relative;
    padding-bottom: 1.5rem;
    letter-spacing: -0.02em;
    line-height: 1.2;
    transition: all 0.3s ease;
    text-shadow: 0 2px 4px rgba(26, 118, 209, 0.1); /* Added from further down, consolidated */
}

/* Decorative underline for h2 */
.health-center-hero h2::after,
.about-domiz h2::after,
.current-reality h2::after,
.health-services h2::after,
.our-mission h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, #1A76D1 0%, #64B5F6 100%);
    border-radius: 2px;
    transition: all 0.3s ease;
}

/* Additional decoration for h2 */
.health-center-hero h2::before,
.about-domiz h2::before,
.current-reality h2::before,
.health-services h2::before,
.our-mission h2::before {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 4px;
    background: rgba(26, 118, 209, 0.3);
    border-radius: 2px;
    transition: all 0.3s ease;
}

/* H3 Styles for Health Center - Consolidated styles for h3 from duplicated blocks */
.health-center-hero h3,
.about-domiz h3,
.our-mission h3 {
    font-size: clamp(1.8rem, 3vw, 2.5rem); /* Default size, overwrites the later larger clamp if not in specific context */
    font-weight: 700;
    color: #2C2D3F;
    margin-bottom: 1.5rem;
    position: relative;
    padding-right: 1.25rem;
    font-family: 'Thabit', serif;
    line-height: 1.4;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1); /* Added text-shadow from hover effect, always applied */
    opacity: 0; /* Animation properties consolidated here */
    transform: translateY(20px);
    animation: fadeInUp 0.6s ease forwards;
}

/* Right border accent for h3 */
.health-center-hero h3::before,
.about-domiz h3::before,
.our-mission h3::before {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 70%; /* Corrected height from 100% to 70% based on visual consistency and removed duplicated rule with 100% height*/
    background: linear-gradient(to bottom, #1A76D1, #64B5F6);
    border-radius: 2px;
    transition: all 0.3s ease;
}

/* Hover effects for h2 */
.health-center-hero h2:hover::after,
.about-domiz h2:hover::after,
.current-reality h2:hover::after,
.health-services h2:hover::after,
.our-mission h2:hover::after {
    width: 100px;
    transition: width 0.3s ease;
}

/* Responsive adjustments for h2 and h3 headings */
@media (max-width: 768px) {
    .health-center-hero h2,
    .about-domiz h2,
    .current-reality h2,
    .health-services h2,
    .our-mission h2 {
        font-size: clamp(2rem, 4vw, 2.5rem);
        padding-bottom: 1rem;
    }

    .health-center-hero h3,
    .about-domiz h3,
    .our-mission h3 {
        font-size: clamp(1.5rem, 2.5vw, 2rem);
        padding-right: 1rem;
    }
}

/* Print styles for headings */
@media print {
    .health-center-hero h2,
    .about-domiz h2,
    .current-reality h2,
    .health-services h2,
    .our-mission h2,
    .health-center-hero h3,
    .about-domiz h3,
    .our-mission h3 {
        color: #000;
        text-shadow: none;
    }
}

.section-title .section-icon {
    position: relative;
    margin: 25px auto;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f1f9ff 0%, #e3f0fa 100%);
    border-radius: 50%;
    box-shadow:
        0 10px 20px rgba(26, 118, 209, 0.1),
        0 6px 6px rgba(26, 118, 209, 0.06);
    transition: all 0.4s cubic-bezier(0.215, 0.61, 0.355, 1);
    animation: iconPulse 2s infinite; /* Animation moved here, consolidated */
}

.section-title .section-icon::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #1A76D1, #64B5F6);
    border-radius: 50%;
    z-index: -1;
    opacity: 0;
    transition: all 0.4s ease;
}

.section-title .section-icon i {
    font-size: 2.5em;
    color: #1A76D1;
    transition: all 0.4s ease;
    transform-origin: center;
}

/* Hover Effects for section icon */
.section-title .section-icon:hover {
    transform: translateY(-5px);
    box-shadow:
        0 15px 30px rgba(26, 118, 209, 0.2),
        0 8px 8px rgba(26, 118, 209, 0.1);
}

.section-title .section-icon:hover::before {
    opacity: 0;
}

.section-title .section-icon:hover i {
    color: white;
    transform: scale(1.1) rotate(5deg);
}

/* Pulse animation for section icon */
@keyframes iconPulse {
    0% {
        box-shadow: 0 0 0 0 rgba(26, 118, 209, 0.4);
    }
    70% {
        box-shadow: 0 0 0 20px rgba(26, 118, 209, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(26, 118, 209, 0);
    }
}

/* Disable animation for users who prefer reduced motion - Adjusted styles to match intended fallback */
@media (prefers-reduced-motion: reduce) {
    .section-title .section-icon {
        animation: none;
        transform: none; /* Removed translateX(-50%) as it doesn't seem relevant here */
        width: auto; /* Changed from 40px to auto to remove fixed width and let content dictate */
        height: auto; /* Changed from 4px to auto to remove fixed height and let content dictate */
        background: none; /* Removed background to remove color */
        border-radius: 0; /* Removed border-radius to remove rounding */
        margin-bottom: 25px; /* Re-added margin-bottom to maintain spacing, original value was 8px which seems too small */
        box-shadow: none; /* Removed box-shadow */
    }
    .section-title .section-icon::before { /* Hide pseudo elements */
        display: none;
    }
}

/* Mission Section Specific Styles for Health Center - Adjusted selector for better specificity */
.our-mission.section .mission-content {
    max-width: 900px;
    margin: 0 auto;
    padding: 40px;
    background: #ffffff;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    position: relative;
}

.our-mission.section .mission-content h3 {
    font-size: 38px;
    color: #1A76D1;
    margin-bottom: 25px;
    font-weight: 800;
    position: relative;
    display: inline-block;
    padding-bottom: 15px;
}

.our-mission.section .mission-content h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #1A76D1, transparent);
    border-radius: 2px;
}

.our-mission.section .mission-content .lead {
    font-size: 20px;
    line-height: 1.8;
    color: #555;
    margin-bottom: 30px;
    font-weight: 400;
}

.our-mission.section .mission-content .lead strong,
.health-center-content .lead strong {
    color: #1A76D1;
    font-weight: 700;
}

.our-mission.section .quote-box {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border-radius: 15px;
    padding: 35px;
    margin-top: 40px;
    position: relative;
    border-right: 4px solid #1A76D1;
    box-shadow: 0 5px 20px rgba(26, 118, 209, 0.08);
    transition: all 0.3s ease;
}

.our-mission.section .quote-box i {
    font-size: 48px;
    color: #1A76D1;
    opacity: 0.2;
    position: absolute;
    top: 20px;
    right: 20px;
    transition: all 0.3s ease;
}

.our-mission.section .quote-box p {
    font-family: 'Thabit', serif;
    margin-right: 60px;
    font-size: 18px;
    font-weight: 700;
    line-height: 1.7;
    color: #2C2D3F;
    font-style: italic;
    margin: 0;
    position: relative;
    z-index: 1;
}

/* Hover Effects for mission section quote box */
.our-mission.section .mission-content:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08);
}

.our-mission.section .quote-box:hover {
    border-right-width: 6px;
}

.our-mission.section .quote-box:hover i {
    opacity: 0.3;
    transform: rotate(-10deg);
}

/* Responsive Adjustments for mission section */
@media (max-width: 768px) {
    .our-mission.section .mission-content {
        padding: 25px;
    }

    .our-mission.section .mission-content h3 {
        font-size: 32px;
    }

    .our-mission.section .mission-content .lead {
        font-size: 18px;
    }

    .our-mission.section .quote-box {
        padding: 25px;
        margin-top: 30px;
    }

    .our-mission.section .quote-box p {
        font-size: 16px;
    }

    .our-mission.section .quote-box i {
        font-size: 36px;
    }
}

/* Print Styles for mission section */
@media print {
    .our-mission.section .mission-content {
        box-shadow: none;
        border: 1px solid #eee;
    }

    .our-mission.section .quote-box {
        box-shadow: none;
        border: 1px solid #eee;
        padding-right: 20px;
        font-family: 'Thabit', serif;
        line-height: 1.4;
    }
}

/* Animation keyframes - fadeInUp animation consolidated */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Hover effect for main headings - Corrected selector to avoid duplicate style*/
.section-title h2:hover {
    transform: scale(1.02);
}

/* Responsive adjustments for section title h2 and hero h3 */
@media (max-width: 768px) {
    .health-center-hero .section-title h2,
    .about-domiz .section-title h2,
    .current-reality .section-title h2,
    .health-services .section-title h2 {
        font-size: clamp(32px, 4vw, 42px);
        padding-bottom: 15px;
    }

}

/* Pulse animation - animation keyframes consolidated and removed duplicated keyframes */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(233, 30, 99, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(233, 30, 99, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(233, 30, 99, 0);
    }
}

.health-center-link:hover .special-badge {
    animation: none;
    transform: translateY(-50%) scale(1.1);
}

/*----------------------------------------------------------------*/
/*------------------------- Section Styles -----------------------*/
/*----------------------------------------------------------------*/

/* Hero Section */
.health-center-hero {
    background: linear-gradient(135deg, #f1f9ff 0%, #e3f0fa 100%);
    padding: 100px 0 80px;
    border-radius: 30px;
    margin: 40px auto;
    position: relative;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(26, 118, 209, 0.08);
}

.health-center-hero::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: url('/img/pattern.png') repeat;
    opacity: 0.05;
    animation: backgroundShift 30s linear infinite;
}

@keyframes backgroundShift {
    0% { transform: translateX(0) translateY(0); }
    50% { transform: translateX(5%) translateY(-5%); }
    100% { transform: translateX(0) translateY(0); }
}

.health-center-hero .health-center-content {
    padding-right: 60px;
}

.health-center-hero .lead {
    font-size: 1.25rem;
    line-height: 1.9;
    color: #445566;
    margin-bottom: 35px;
    font-weight: 400;
}

.health-center-hero .achievement-box {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 20px 25px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(26, 118, 209, 0.08);
    transition: all 0.4s cubic-bezier(0.215, 0.61, 0.355, 1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.health-center-hero .achievement-box:hover {
    transform: translateX(-10px) translateY(-2px);
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 8px 30px rgba(26, 118, 209, 0.12);
    border-color: rgba(26, 118, 209, 0.2);
}

.health-center-hero .achievement-box i {
    color: #1A76D1;
    font-size: 28px;
    margin-right: 20px;
    transition: transform 0.4s ease;
}

.health-center-hero .achievement-box:hover i {
    transform: scale(1.1) rotate(5deg);
}

.health-center-hero .health-center-image img {
    border-radius: 24px;
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.12),
        0 10px 30px rgba(26, 118, 209, 0.1);
    transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
    transform: perspective(1000px) rotateY(-5deg);
    backface-visibility: hidden;
}

.health-center-hero .health-center-image:hover img {
    transform: perspective(1000px) rotateY(-2deg) translateY(-15px);
    box-shadow:
        0 35px 60px rgba(0, 0, 0, 0.15),
        0 15px 40px rgba(26, 118, 209, 0.15);
}

/* About Domiz Section */
.about-domiz {
    padding: 120px 0;
    background: linear-gradient(to bottom, #fff, #f8fbff);
}

.domiz-info h3 {
    color: #1A76D1;
    font-size: 2.25rem;
    margin-bottom: 30px;
    font-weight: 700;
    position: relative;
    padding-bottom: 20px;
}

.domiz-info h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 4px;
    background: linear-gradient(to right, #1A76D1, #64B5F6);
    border-radius: 2px;
}

.domiz-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
    margin-top: 40px;
}

.single-stat {
    background: rgba(255, 255, 255, 0.9);
    padding: 40px;
    border-radius: 24px;
    text-align: center;
    box-shadow:
        0 10px 30px rgba(0, 0, 0, 0.06),
        0 5px 15px rgba(26, 118, 209, 0.05);
    transition: all 0.4s cubic-bezier(0.215, 0.61, 0.355, 1);
    border: 1px solid rgba(26, 118, 209, 0.08);
    backdrop-filter: blur(10px);
}

.single-stat:hover {
    transform: translateY(-10px);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.08),
        0 10px 20px rgba(26, 118, 209, 0.08);
    border-color: rgba(26, 118, 209, 0.2);
    background: rgba(255, 255, 255, 0.95);
}

.single-stat i {
    font-size: 48px;
    color: #1A76D1;
    margin-bottom: 25px;
    transition: all 0.4s ease;
}

.single-stat:hover i {
    transform: scale(1.1) rotate(5deg);
    color: #2196F3;
}

.single-stat h3 {
    font-size: 3rem;
    color: #2c3e50;
    margin-bottom: 15px;
    font-weight: 800;
    background: linear-gradient(135deg, #1A76D1, #2196F3);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* Reality Boxes */
.current-reality {
    background: #f8fbff;
    padding: 100px 0;
}

.reality-box {
    background: white;
    padding: 35px;
    border-radius: 20px;
    margin-bottom: 30px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.06);
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    border: 1px solid rgba(26, 118, 209, 0.1);
}

.reality-box:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 45px rgba(26, 118, 209, 0.12);
    border-color: #1A76D1;
}

.reality-box .icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #f1f9ff 0%, #e3f0fa 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 25px;
    transition: all 0.4s ease;
}

.reality-box:hover .icon {
    background: linear-gradient(135deg, #1A76D1, #2196F3);
    transform: scale(1.1) rotate(5deg); /* Corrected missing hover transform */
}

.reality-box .icon i {
    font-size: 35px;
    color: #1A76D1;
    transition: all 0.4s ease;
}

.reality-box:hover .icon i {
    color: white;
    transform: scale(1.1);
}

/* Service Boxes */
.health-services {
    padding: 100px 0;
    background: white;
}

.service-box {
    background: white;
    padding: 35px;
    border-radius: 24px;
    margin-bottom: 30px;
    box-shadow:
        0 10px 30px rgba(0, 0, 0, 0.06),
        0 5px 15px rgba(26, 118, 209, 0.05);
    transition: all 0.4s cubic-bezier(0.215, 0.61, 0.355, 1);
    text-align: center;
    border: 1px solid rgba(26, 118, 209, 0.08);
    position: relative;
    overflow: hidden;
}

.service-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(26, 118, 209, 0.05), transparent);
    opacity: 0;
}

.service-box:hover {
    transform: translateY(-10px);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.08),
        0 10px 20px rgba(26, 118, 209, 0.08);
    border-color: #1A76D1;
}


.service-box:hover::before {
    opacity: 1;
}

.service-icon {
    width: 90px;
    height: 90px;
    background: linear-gradient(135deg, #f1f9ff 0%, #e3f0fa 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 30px;
    transition: all 0.4s ease;
}

.service-box:hover .service-icon {
    background: linear-gradient(135deg, #1A76D1, #2196F3);
    transform: scale(1.1) rotate(5deg);
}

.service-icon i {
    font-size: 40px;
    color: #1A76D1;
    transition: all 0.4s ease;
}

.service-box:hover .service-icon i {
    color: white;
    transform: scale(1.1);
}

/* Mission Section */
.our-mission {
    background: #f9fcff;
    padding: 80px 0;
}

.mission-content {
    max-width: 800px;
    margin: 0 auto;
}

.mission-content h3 {
    color: #1A76D1;
    font-size: 36px;
    margin-bottom: 30px;
}

.quote-box {
    font-size: 18px;
    background: white;
    padding: 30px;
    border-radius: 15px;
    margin-top: 40px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.05);
    position: relative;
}

.quote-box i {
    font-size: 48px;
    color: #1A76D1;
    opacity: 0.2;
    position: absolute;
    top: 20px;
    right: 20px;
}

/* CTA Section */
.cta {
    padding: 80px 0;
}

.cta-inner {
    background: linear-gradient(135deg, #1A76D1 0%, #1557a0 100%);
    padding: 60px;
    border-radius: 20px;
    text-align: center;
    color: white;
}

.cta-inner p {
    font-size: 18px;
    line-height: 1.8;
    margin-bottom: 30px;
    color: white;
}

.cta-inner h2 {
    font-size: 36px;
    margin-bottom: 20px;
    color: white;
}

.cta-buttons {
    margin-top: 30px;
}

.cta-buttons .btn {
    margin: 10px;
    padding: 15px 30px;
    border-radius: 30px;
    font-size: 18px;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.cta-buttons .btn:before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s ease, height 0.6s ease;
    z-index: -1;
}

.cta-buttons .btn:hover:before {
    width: 300px;
    height: 300px;
}

.cta-buttons .btn:first-child {
    background: white;
    color: #1A76D1;
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
}

.cta-buttons .btn:last-child {
    background: transparent;
    border: 2px solid white;
    color: white;
}

.cta-buttons .btn:first-child:hover {
    background: #f8f9fa;
    color: #1557a0;
    transform: translateY(-3px);
}

.cta-buttons .btn:last-child:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-color: rgba(255, 255, 255, 0.8);
    transform: translateY(-3px);
}

.cta-buttons .btn:hover {
    box-shadow: 0 7px 20px rgba(0, 0, 0, 0.2);
}

/* Responsive Design */
@media (max-width: 991px) {
    .health-center-hero .health-center-content {
        padding-right: 0;
        margin-bottom: 50px;
        text-align: center;
    }

    .domiz-stats {
        grid-template-columns: 1fr;
    }

    .service-box,
    .reality-box {
        padding: 10px;
    }
    .reality-box {
        padding-right: 50px;
    }
}

@media (max-width: 767px) {
    .health-center-hero {
        padding: 60px 0;
    }

    .health-center-hero h3 {
        font-size: clamp(24px, 3vw, 32px);
    }

    .single-stat {
        padding: 30px;
    }

    .single-stat h3 {
        font-size: 7vw !important;
    }
}

@media (max-width: 480px) {
    .health-center-hero {
        padding: 40px 0;
        margin: 20px auto;
    }

    .service-box,
    .reality-box {
        padding: 5%;
    }
    .reality-box {
        padding-right: 30px;
    }

    .service-icon,
    .reality-box .icon {
        width: 70px;
        height: 70px;
    }

    .service-icon i,
    .reality-box .icon i {
        font-size: 30px;
    }
}

/* Global RTL Adjustments */
.health-center-hero,
.about-domiz,
.current-reality,
.health-services,
.our-mission,
.cta {
    direction: rtl;
    text-align: right;
}

/* Hero Section RTL adjustments - Consolidated with LTR adjustments where possible */

.health-center-hero .health-center-content {
    padding-left: 40px; /* RTL Specific */
    padding-right: 0; /* Resetting LTR padding */
}

.health-center-hero h3 {
    font-size: clamp(24px, 3vw, 36px); /* RTL Specific, overwrites the default clamp size */
    color: #1A76D1; /* Shared */
    margin-bottom: 25px; /* Shared */
    font-weight: 700; /* Shared */
    position: relative; /* Shared */
    padding-right: 20px; /* RTL Specific padding, overwrites default */
    padding-left: 0; /* Resetting LTR padding if any was set previously */
}

.health-center-hero h3::before {
    content: ''; /* Shared */
    position: absolute; /* Shared */
    right: 0; /* RTL Specific, changed from left: 0 */
    left: auto; /* Resetting LTR left if any was set previously */
    top: 50%; /* Shared */
    transform: translateY(-50%); /* Shared */
    width: 4px; /* Shared */
    height: 100%; /* RTL Specific, overwrites default if necessary */
    background: #1A76D1; /* Shared */
    border-radius: 2px; /* Shared */
}

.health-center-hero .lead {
    font-size: 18px; /* RTL Specific, overwrites default */
    line-height: 2; /* RTL Specific, overwrites default */
    color: #444; /* RTL Specific, overwrites default */
    margin-bottom: 35px; /* Shared */
}

.health-center-hero .achievement-box {
    display: flex; /* Shared */
    align-items: center; /* Shared */
    margin-bottom: 20px; /* Shared */
    padding: 15px 20px; /* RTL Specific, overwrites default */
    background: white; /* RTL Specific, overwrites default */
    border-radius: 15px; /* RTL Specific, overwrites default */
    box-shadow: 0 3px 15px rgba(26,118,209,0.08); /* RTL Specific, overwrites default */
    transition: all 0.4s ease; /* Shared */
    border: 1px solid rgba(26,118,209,0.1); /* RTL Specific, overwrites default */
}

.health-center-hero .achievement-box:hover {
    transform: translateX(-8px); /* RTL Specific, changed direction */
    background: #f8fbff; /* RTL Specific, overwrites default */
    border-color: #1A76D1; /* RTL Specific, overwrites default */
}

.health-center-hero .achievement-box i {
    color: #1A76D1; /* Shared */
    font-size: 24px; /* RTL Specific, overwrites default */
    margin-left: 15px; /* RTL Specific margin, overwrites default margin-right */
    margin-right: 0; /* Resetting LTR margin-right */
}

.health-center-hero .health-center-image {
    position: relative; /* Shared */
    padding: 20px; /* RTL Specific, overwrites default if any */
}

.health-center-hero .health-center-image::before {
    content: ''; /* Shared */
    position: absolute; /* Shared */
    top: 0; /* Shared */
    right: 0; /* RTL Specific, changed from left: 0 */
    left: auto; /* Resetting LTR left if any was set previously */
    width: 100%; /* Shared */
    height: 100%; /* Shared */
    background: #1A76D1; /* RTL Specific, overwrites default if necessary */
    border-radius: 20px; /* RTL Specific, overwrites default if necessary */
    transform: rotate(-3deg); /* RTL Specific, overwrites default if necessary */
    z-index: 0; /* Shared */
    opacity: 0.1; /* Shared */
}

.health-center-hero .health-center-image img {
    border-radius: 20px; /* RTL Specific, overwrites default */
    box-shadow: 0 20px 40px rgba(0,0,0,0.15); /* RTL Specific, overwrites default */
    transition: all 0.5s ease; /* Shared */
    position: relative; /* Shared */
    z-index: 1; /* Shared */
}

.health-center-hero .health-center-image:hover img {
    transform: translateY(-12px) scale(1.02); /* Shared */
}

/* About Domiz Section RTL adjustments - Consolidated with LTR where possible */

.domiz-info h3 {
    color: #1A76D1; /* Shared */
    font-size: 28px; /* RTL Specific, overwrites default */
    margin-bottom: 25px; /* Shared */
    position: relative; /* Shared */
    padding-right: 18px; /* RTL Specific padding, overwrites default */
    padding-left: 0; /* Resetting LTR padding if any was set previously */
}

.domiz-info h3::before {
    content: ''; /* Shared */
    position: absolute; /* Shared */
    right: 0; /* RTL Specific, changed from left: 0 */
    left: auto; /* Resetting LTR left if any was set previously */
    top: 50%; /* Shared */
    transform: translateY(-50%); /* Shared */
    width: 4px; /* Shared */
    height: 80%; /* RTL Specific, overwrites default if necessary */
    background: #1A76D1; /* Shared */
    border-radius: 2px; /* Shared */
}

.domiz-info p {
    line-height: 1.9; /* RTL Specific, overwrites default */
    color: #555; /* RTL Specific, overwrites default */
    font-size: 16px; /* RTL Specific, overwrites default */
}

.domiz-stats {
    display: grid; /* Shared */
    grid-template-columns: repeat(2, 1fr); /* Shared */
    gap: 25px; /* Shared */
}

.single-stat {
    background: white; /* RTL Specific, overwrites default */
    padding: 35px; /* RTL Specific, overwrites default */
    border-radius: 20px; /* RTL Specific, overwrites default */
    text-align: center; /* Shared */
    box-shadow: 0 8px 25px rgba(0,0,0,0.05); /* RTL Specific, overwrites default */
    transition: all 0.4s ease; /* Shared */
    border: 1px solid rgba(26,118,209,0.1); /* RTL Specific, overwrites default */
    position: relative; /* Shared */
    overflow: hidden; /* Shared */
}

.single-stat::before {
    content: ''; /* Shared */
    position: absolute; /* Shared */
    top: 0; /* Shared */
    right: 0; /* RTL Specific, changed from left: 0 */
    left: auto; /* Resetting LTR left if any was set previously */
    width: 100%; /* Shared */
    height: 100%; /* Shared */
    background: linear-gradient(135deg, rgba(26,118,209,0.05) 0%, transparent 100%); /* RTL Specific, overwrites default if necessary */
    opacity: 0; /* Shared */
}

.single-stat:hover {
    transform: translateY(-8px); /* Shared */
    border-color: #1A76D1; /* RTL Specific, overwrites default */
}

.single-stat:hover::before {
    opacity: 1; /* Shared */
}

.single-stat i {
    font-size: 45px; /* RTL Specific, overwrites default */
    color: #1A76D1; /* Shared */
    margin-bottom: 20px; /* Shared */
}

.single-stat h3 {
    font-size: 42px; /* RTL Specific, overwrites default */
    color: #333; /* RTL Specific, overwrites default */
    margin-bottom: 15px; /* Shared */
    font-weight: 700; /* Shared */
}

.reality-box h4 {
    color: #333; /* RTL Specific, overwrites default */
    font-size: 22px; /* RTL Specific, overwrites default */
    margin-bottom: 20px; /* Shared */
    font-weight: 600; /* RTL Specific, overwrites default */
}

.reality-box ul {
    list-style: none; /* Shared */
    padding: 0; /* Shared */
    margin: 0; /* Shared */
}

.reality-box ul li {
    color: #555; /* RTL Specific, overwrites default */
    margin-bottom: 12px; /* Shared */
    padding-right: 25px; /* RTL Specific padding, overwrites default */
    padding-left: 0; /* Resetting LTR padding if any was set previously */
    position: relative; /* Shared */
    line-height: 1.6; /* RTL Specific, overwrites default */
}

.reality-box ul li:before {
    content: "•"; /* Shared */
    color: #1A76D1; /* Shared */
    position: absolute; /* Shared */
    right: 0; /* RTL Specific, changed from left: 0 */
    left: auto; /* Resetting LTR left if any was set previously */
    font-size: 20px; /* RTL Specific, overwrites default */
    line-height: 1; /* RTL Specific, overwrites default */
}


.service-icon {
    width: 90px; /* Shared */
    height: 90px; /* Shared */
    background: linear-gradient(135deg, #f1f9ff 0%, #e3f0fa 100%); /* Shared */
    border-radius: 50%; /* Shared */
    display: flex; /* Shared */
    align-items: center; /* Shared */
    justify-content: center; /* Shared */
    margin: 0 0 25px; /* RTL Specific margin, overwrites default margin: 0 auto 30px */
    margin-right: auto; /* Centering horizontally in RTL */
    margin-left: auto;  /* Centering horizontally in RTL */
    transition: all 0.4s ease; /* Shared */
}

.service-icon i {
    font-size: 40px; /* Shared */
    color: #1A76D1; /* Shared */
    transition: all 0.4s ease; /* Shared */
}

/* Mission Section RTL adjustments - Consolidated with LTR where possible */

.mission-content {
    max-width: 900px; /* Shared */
    margin: 0 auto; /* Shared */
}

.mission-content h3 {
    color: #1A76D1; /* Shared */
    font-size: 36px; /* RTL Specific, overwrites default */
    margin-bottom: 35px; /* Shared */
    text-align: center; /* Shared */
    position: relative; /* Shared */
    padding-bottom: 20px; /* Shared */
}

.mission-content h3::after {
    content: ''; /* Shared */
    position: absolute; /* Shared */
    bottom: 0; /* Shared */
    right: 50%; /* RTL Specific, changed from left: 50% to right: 50% */
    left: auto; /* Resetting LTR left if any was set previously */
    transform: translateX(50%); /* RTL Specific, changed from translateX(-50%) to translateX(50%) */
    width: 80px; /* Shared */
    height: 4px; /* Shared */
    background: #1A76D1; /* Shared */
    border-radius: 2px; /* Shared */
}

.quote-box {
    background: white; /* RTL Specific, overwrites default */
    padding: 40px; /* RTL Specific, overwrites default */
    border-radius: 20px; /* RTL Specific, overwrites default */
    margin-top: 50px; /* Shared */
    box-shadow: 0 8px 25px rgba(0,0,0,0.05); /* RTL Specific, overwrites default */
    position: relative; /* Shared */
    border: 1px solid rgba(26,118,209,0.1); /* RTL Specific, overwrites default */
}

.quote-box i {
    font-size: 60px; /* RTL Specific, overwrites default */
    color: #1A76D1; /* Shared */
    opacity: 0.1; /* Shared */
    position: absolute; /* Shared */
    top: 20px; /* Shared */
    right: 20px; /* RTL Specific, changed from left: 20px to right: 20px */
    left: auto; /* Resetting LTR left if any was set previously */
}

/* CTA Section RTL adjustments - Consolidated with LTR where possible */

.cta-inner {
    background: linear-gradient(135deg, #1A76D1 0%, #1557a0 100%); /* Shared */
    padding: 70px; /* RTL Specific, overwrites default */
    border-radius: 30px; /* Shared */
    text-align: center; /* Shared */
    color: white; /* Shared */
    position: relative; /* Shared */
    overflow: hidden; /* Shared */
}

.cta-inner::before {
    content: ''; /* Shared */
    position: absolute; /* Shared */
    top: 0; /* Shared */
    right: 0; /* Shared */
    width: 100%; /* Shared */
    height: 100%; /* Shared */
    background: url('/img/pattern.png') repeat; /* Shared */
    opacity: 0.1; /* Shared */
}

.cta-inner h2 {
    font-size: 42px; /* RTL Specific, overwrites default */
    margin-bottom: 25px; /* Shared */
    color: white; /* Shared */
    position: relative; /* Shared */
    font-weight: 800; /* Shared */
    line-height: 1.2; /* Shared */
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Shared */
    background: linear-gradient(135deg, #ffffff, #f1f9ff); /* Shared */
    -webkit-background-clip: text; /* Shared */
    background-clip: text; /* Shared */
    -webkit-text-fill-color: transparent; /* Shared */
    text-align: center; /* Shared */
    margin-left: auto; /* Shared */
    margin-right: auto; /* Shared */
    max-width: 800px; /* Shared */
    padding: 0 15px; /* Shared */
}

/* Responsive adjustments for CTA inner h2 - No RTL specific changes, reusing existing media query */

.cta-inner p {
    font-size: 20px; /* RTL Specific, overwrites default */
    line-height: 1.8; /* Shared */
    margin-bottom: 30px; /* Shared */
    color: rgba(255, 255, 255, 0.9); /* Shared */
    max-width: 800px; /* Shared */
    margin-left: auto; /* Shared */
    margin-right: auto; /* Shared */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1); /* Shared */
}

.cta-buttons {
    margin-top: 40px; /* Shared */
    display: flex; /* Shared */
    justify-content: center; /* Shared */
    gap: 20px; /* Shared */
}

.cta-buttons .btn {
    padding: 15px 35px; /* Shared */
    border-radius: 30px; /* Shared */
    font-size: 18px; /* Shared */
    transition: all 0.4s ease; /* Shared */
    min-width: 200px; /* Shared */
}

.cta-buttons .btn:first-child {
    background: white; /* Shared */
    color: #1A76D1; /* Shared */
}

.cta-buttons .btn:first-child:hover {
    background: #f1f9ff; /* Shared */
}

.cta-buttons .btn:last-child {
    background: transparent; /* Shared */
    border: 2px solid white; /* Shared */
    color: white; /* Shared */
}

.cta-buttons .btn:last-child:hover {
    background: rgba(255,255,255,0.1); /* Shared */
}

.cta-buttons .btn:hover {
    transform: translateY(-5px); /* Shared */
    box-shadow: 0 8px 25px rgba(0,0,0,0.2); /* Shared */
}

/* Section Titles RTL adjustments - Consolidated with LTR where possible */
.section-title {
    text-align: center; /* Shared */
    margin-bottom: 60px; /* Shared */
}

.section-title h2 {
    font-size: clamp(32px, 4vw, 42px); /* Shared */
    color: #1A76D1; /* Shared */
    position: relative; /* Shared */
    padding-bottom: 20px; /* Shared */
    margin-bottom: 20px; /* Shared */
}

.section-title h2::after {
    content: ''; /* Shared */
    position: absolute; /* Shared */
    bottom: 0; /* Shared */
    right: 50%; /* RTL Specific, changed from left: 50% to right: 50% */
    left: auto; /* Resetting LTR left if any was set previously */
    transform: translateX(50%); /* RTL Specific, changed from translateX(-50%) to translateX(50%) */
    width: 80px; /* Shared */
    height: 4px; /* Shared */
    background: #1A76D1; /* Shared */
    border-radius: 2px; /* Shared */
}

/* Responsive Design - No RTL specific changes in these media queries, reusing existing */

/* Common Image Styles - No changes needed */
img {
    image-rendering: -webkit-optimize-contrast;
    backface-visibility: hidden;
    transform: translateZ(0);
    -webkit-font-smoothing: subpixel-antialiased;
}

/* Health Center Hero Section Images - No RTL specific changes needed*/
.health-center-image {
    position: relative;
    perspective: 1000px;
}

.health-center-image img {
    width: 100%;
    height: auto;
    border-radius: 24px;
    box-shadow:
        0 20px 40px rgba(0,0,0,0.08),
        0 5px 15px rgba(26,118,209,0.1);
    transition: all 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);
    filter: brightness(1.02) contrast(1.02);
}

.health-center-image::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 24px;
    box-shadow: 0 25px 50px rgba(26,118,209,0.2);
    opacity: 0;
    transition: all 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.health-center-image:hover img {
    transform: translateY(-15px) rotateX(3deg);
    filter: brightness(1.05) contrast(1.05);
}

.health-center-image:hover::after {
    opacity: 1;
}

/* Gallery Strip Images - No RTL specific changes needed */
.hc-gallery-strip {
    padding: 60px 0;
    overflow: hidden;
    background: linear-gradient(to bottom, rgba(241,249,255,0.5), transparent);
}

.hc-image-strip {
    display: flex;
    gap: 25px;
    justify-content: center;
    padding: 20px 0;
}

.strip-img {
    width: calc(33.333% - 17px);
    height: 320px;
    object-fit: cover;
    border-radius: 20px;
    box-shadow:
        0 10px 30px rgba(0,0,0,0.1),
        0 4px 8px rgba(26,118,209,0.05);
    transition: all 0.5s cubic-bezier(0.215, 0.61, 0.355, 1);
    filter: saturate(0.95);
}

.strip-img:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow:
        0 20px 40px rgba(0,0,0,0.15),
        0 8px 16px rgba(26,118,209,0.1);
    filter: saturate(1.1) brightness(1.02);
}

/* Domiz Showcase Images - No RTL specific changes needed */
.showcase-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
    margin-top: 50px;
    padding: 20px;
}

.showcase-img {
    width: 100%;
    height: 450px;
    object-fit: cover;
    border-radius: 16px;
    box-shadow:
        0 15px 35px rgba(0,0,0,0.1),
        0 5px 15px rgba(0,0,0,0.05);
    transition: all 0.6s cubic-bezier(0.19, 1, 0.22, 1);
    filter: brightness(0.98);
}

.showcase-img:hover {
    transform: scale(1.03) translateY(-10px);
    box-shadow:
        0 25px 50px rgba(0,0,0,0.15),
        0 10px 20px rgba(26,118,209,0.1);
    filter: brightness(1.05) contrast(1.02);
}

/* Reality Images - No RTL specific changes needed */
.floating-image-container {
    display: flex;
    gap: 20px;
    justify-content: center;
    margin-top: 50px;
    perspective: 1000px;
}

.float-img {
    width: calc(33.333% - 14px);
    height: 280px;
    object-fit: cover;
    border-radius: 12px;
    box-shadow:
        0 10px 25px rgba(0,0,0,0.08),
        0 5px 10px rgba(26,118,209,0.05);
    transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
    transform-origin: center center;
}

.float-img:hover {
    transform: translateY(-15px) rotateX(2deg) rotateY(-2deg);
    box-shadow:
        0 20px 40px rgba(0,0,0,0.12),
        0 8px 16px rgba(26,118,209,0.08);
    filter: contrast(1.02) brightness(1.05);
}

/* Services Gallery Images - No RTL specific changes needed */
.services-image-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin: 50px 0;
    padding: 15px;
}



.service-gallery-img {
    width: 100%;
    height: 220px;
    object-fit: cover;
    border-radius: 10px;
    box-shadow:
        0 8px 20px rgba(0,0,0,0.06),
        0 4px 8px rgba(26,118,209,0.04);
    transition: all 0.4s cubic-bezier(0.215, 0.61, 0.355, 1);
    filter: saturate(0.95) brightness(0.98);
}

.service-gallery-img:hover {
    transform: scale(1.04) translateY(-8px);
    box-shadow:
        0 15px 30px rgba(0,0,0,0.1),
        0 8px 16px rgba(26,118,209,0.08);
    filter: saturate(1.1) brightness(1.03);
    z-index: 1;
}

/* Mission Feature Image - No RTL specific changes needed */
.mission-image-wrapper {
    margin-right: 20%;
    margin-left: 20%;
    height: 100%;
    position: relative;
    margin-top: 50px;
    padding: 20px;
}

.mission-feature-img {
    width: 100%;
    max-height: 550px;
    object-fit: cover;
    border-radius: 24px;
    box-shadow:
        0 20px 40px rgba(0,0,0,0.12),
        0 8px 16px rgba(26,118,209,0.08);
    transition: all 0.7s cubic-bezier(0.19, 1, 0.22, 1);
    filter: saturate(0.98);
}

.mission-feature-img:hover {
    transform: scale(1.02) translateY(-15px);
    box-shadow:
        0 30px 60px rgba(0,0,0,0.15),
        0 12px 24px rgba(26,118,209,0.1);
    filter: saturate(1.05) brightness(1.02);
}

/* CTA Strip Images - No RTL specific changes needed */
.final-image-row {
    display: flex;
    gap: 25px;
    margin-top: 50px;
    padding: 15px;
}

.cta-strip-img {
    width: calc(50% - 13px);
    height: 320px;
    object-fit: cover;
    border-radius: 18px;
    box-shadow:
        0 15px 35px rgba(0,0,0,0.1),
        0 5px 15px rgba(26,118,209,0.06);
    transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
    filter: brightness(0.98);
}

.cta-strip-img:hover {
    transform: translateY(-12px) scale(1.02) rotate(-1deg);
    box-shadow:
        0 25px 50px rgba(0,0,0,0.15),
        0 10px 20px rgba(26,118,209,0.1);
    filter: brightness(1.05) contrast(1.02);
}

/* Responsive Adjustments - No RTL specific changes needed in these media queries, reusing existing */
@media (max-width: 1200px) {
    .showcase-img {
        height: 400px;
    }

    .service-gallery-img {
        height: 200px;
    }
}

@media (max-width: 992px) {
    .mission-image-wrapper {
        margin-right: 0%;
        margin-left: 0%;
    }
    
    .strip-img {
        height: 280px;
    }

    .float-img {
        height: 250px;
    }
}

@media (max-width: 768px) {

    .services-image-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .strip-img,
    .float-img {
        height: 220px;
    }

    .showcase-img {
        height: 350px;
    }

    .cta-strip-img {
        height: 280px;
    }
}

@media (max-width: 480px) {
    .hc-image-strip,
    .floating-image-container,
    .final-image-row {
        flex-direction: column;
    }

    .strip-img,
    .float-img,
    .cta-strip-img {
        width: 100%;
        margin-bottom: 20px;
        height: 250px;
    }

    .showcase-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .services-image-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .mission-feature-img {
        max-height: 400px;
    }
}
