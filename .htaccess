# ==============================================================================
# Apache Server Configuration File (.htaccess)
# ==============================================================================

# Turn on the Rewrite Engine (Required for many rules below)
RewriteEngine On

# Set default character set to avoid encoding issues
AddDefaultCharset UTF-8

# Prevent Directory Listing (Security)
Options -Indexes

# Follow symbolic links if necessary (check server config)
# Options +FollowSymLinks
# Use SymLinksIfOwnerMatch if FollowSymLinks is disallowed
Options +SymLinksIfOwnerMatch

# ==============================================================================
# SECURITY ENHANCEMENTS
# ==============================================================================

# ------------------------------------------------------------------------------
# Deny Access to Sensitive Files & Directories
# ------------------------------------------------------------------------------

# Protect .htaccess and other sensitive file types
<FilesMatch "(\.(htaccess|htpasswd|ini|log|sh|sql|db|conf|bak|old|psd|md)|~)$">
    Require all denied
</FilesMatch>

# Deny access to the Bin directory (adjust if needed)
RewriteRule ^Bin/ - [F,L,NC]

# Deny access to database directories explicitly (redundant but safe)
RewriteRule ^pages/contact/contact_database/ - [F,L,NC]
RewriteRule ^pages/join_donate/join_donate_databases/ - [F,L,NC]
RewriteRule ^mail/newsletter\.db$ - [F,L,NC] # Specific block just in case

# ------------------------------------------------------------------------------
# Security Headers
# ------------------------------------------------------------------------------
<IfModule mod_headers.c>
    # Prevent MIME-sniffing security risks
    Header set X-Content-Type-Options "nosniff"
    # Prevent Clickjacking attacks
    Header set X-Frame-Options "SAMEORIGIN"
    # Control referrer information sent
    Header set Referrer-Policy "strict-origin-when-cross-origin"
    # Content Security Policy (CSP) - Add if needed, requires careful configuration
    # Header set Content-Security-Policy "default-src 'self';"
</IfModule>

# ------------------------------------------------------------------------------
# Force HTTPS (Strongly Recommended for Production)
# ------------------------------------------------------------------------------
# Uncomment the following lines AFTER you have installed an SSL certificate
RewriteCond %{HTTPS} off
RewriteCond %{HTTP_HOST} !^localhost [NC] # Optional: Exclude localhost
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301,NE]

# ==============================================================================
# URL REWRITING & HANDLING
# ==============================================================================

# ------------------------------------------------------------------------------
# Custom Error Pages
# ------------------------------------------------------------------------------
ErrorDocument 404 /404/404.php

# ------------------------------------------------------------------------------
# Canonical URLs (Remove Trailing Slashes)
# ------------------------------------------------------------------------------
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} (.+)/$
RewriteRule ^ %1 [L,R=301]

# ------------------------------------------------------------------------------
# Remove index.php from URLs
# ------------------------------------------------------------------------------
RewriteCond %{THE_REQUEST} ^[A-Z]{3,}\s(.*)/index\.php [NC]
RewriteRule ^(.*)/index\.php$ /$1 [R=301,L]

# ------------------------------------------------------------------------------
# OPTIONAL: Remove .php and .html Extensions (Clean URLs)
# ------------------------------------------------------------------------------
# Uncomment these rules if you want URLs like /about instead of /about.php
# Make sure your internal links are updated accordingly (e.g., href="/about")

# # 1. Rewrite extensionless URL to .php file
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{DOCUMENT_ROOT}/$1.php -f
RewriteRule ^([^/]+)/?$ $1.php [L]

# # Rewrite extensionless URL within directories to .php file (e.g., /pages/About/about)
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{DOCUMENT_ROOT}/$1.php -f
RewriteRule ^(.*)$ $1.php [L]

# # Rewrite extensionless URL to .html file
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{DOCUMENT_ROOT}/$1.html -f
RewriteRule ^([^/]+)/?$ $1.html [L]

# # Rewrite extensionless URL within directories to .html file (e.g., /pages/About/about)
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{DOCUMENT_ROOT}/$1.html -f
RewriteRule ^(.*)$ $1.html [L]

# # 2. Redirect URLs with .php/.html extension to extensionless version (SEO)
RewriteCond %{THE_REQUEST} \s/+(.+?)\.(php|html)[\s?] [NC]
RewriteRule ^ /%1 [R=301,L,NE]

# ==============================================================================
# PERFORMANCE ENHANCEMENTS
# ==============================================================================

# ------------------------------------------------------------------------------
# Enable Gzip/Deflate Compression (Requires mod_deflate)
# ------------------------------------------------------------------------------
<IfModule mod_deflate.c>
    # Compress common text-based file types
    AddOutputFilterByType DEFLATE text/plain text/html text/xml text/css text/javascript
    AddOutputFilterByType DEFLATE application/xml application/xhtml+xml application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript application/x-javascript
    AddOutputFilterByType DEFLATE image/svg+xml
    AddOutputFilterByType DEFLATE application/vnd.ms-fontobject application/x-font-ttf font/opentype font/ttf font/eot font/otf

    # Optional: Handle browser specific issues
    BrowserMatch ^Mozilla/4 gzip-only-text/html
    BrowserMatch ^Mozilla/4\.0[678] no-gzip
    BrowserMatch \bMSIE !no-gzip !gzip-only-text/html
    Header append Vary User-Agent env=!dont-vary
</IfModule>

# ------------------------------------------------------------------------------
# Leverage Browser Caching (Requires mod_expires)
# ------------------------------------------------------------------------------
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresDefault                              "access plus 1 month"

    # HTML
    ExpiresByType text/html                     "access plus 1 day"

    # Data formats
    ExpiresByType application/xml               "access plus 0 seconds"
    ExpiresByType application/json              "access plus 0 seconds"
    ExpiresByType application/rss+xml           "access plus 1 hour"
    ExpiresByType application/atom+xml          "access plus 1 hour"

    # Media: images, video, audio
    ExpiresByType image/gif                     "access plus 1 year"
    ExpiresByType image/png                     "access plus 1 year"
    ExpiresByType image/jpg                     "access plus 1 year"
    ExpiresByType image/jpeg                    "access plus 1 year"
    ExpiresByType image/webp                    "access plus 1 year"
    ExpiresByType image/svg+xml                 "access plus 1 year"
    ExpiresByType image/x-icon                  "access plus 1 year"
    ExpiresByType video/ogg                     "access plus 1 year"
    ExpiresByType audio/ogg                     "access plus 1 year"
    ExpiresByType video/mp4                     "access plus 1 year"
    ExpiresByType video/webm                    "access plus 1 year"

    # CSS and JavaScript
    ExpiresByType text/css                      "access plus 1 year"
    ExpiresByType application/javascript        "access plus 1 year"
    ExpiresByType text/javascript               "access plus 1 year"

    # Web fonts
    ExpiresByType application/vnd.ms-fontobject "access plus 1 year"
    ExpiresByType application/x-font-ttf        "access plus 1 year"
    ExpiresByType application/x-font-woff       "access plus 1 year"
    ExpiresByType application/font-woff         "access plus 1 year"
    ExpiresByType application/font-woff2        "access plus 1 year"
    ExpiresByType font/opentype                 "access plus 1 year"
    ExpiresByType font/ttf                      "access plus 1 year"
    ExpiresByType font/eot                      "access plus 1 year"
</IfModule>

# Alternative Cache-Control Headers (more modern, use if mod_expires is unavailable or preferred)
# <IfModule mod_headers.c>
#   <FilesMatch "\.(ico|jpe?g|png|gif|webp|svg|js|css|otf|eot|ttf|woff|woff2)$">
#     Header set Cache-Control "max-age=31536000, public" # 1 year
#   </FilesMatch>
#   <FilesMatch "\.(html|php|xml|json)$">
#     Header set Cache-Control "max-age=86400, public" # 1 day (adjust as needed)
#     # Or for highly dynamic content:
#     # Header set Cache-Control "no-cache, no-store, must-revalidate"
#     # Header set Pragma "no-cache"
#     # Header set Expires "0"
#   </FilesMatch>
# </IfModule>

# ==============================================================================
# END OF .htaccess
# ==============================================================================